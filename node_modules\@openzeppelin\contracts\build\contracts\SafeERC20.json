{"_format": "hh-sol-artifact-1", "contractName": "SafeERC20", "sourceName": "contracts/token/ERC20/utils/SafeERC20.sol", "abi": [{"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "currentAllowance", "type": "uint256"}, {"internalType": "uint256", "name": "requestedDecrease", "type": "uint256"}], "name": "SafeERC20FailedDecreaseAllowance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220d958548954339e4559b2e73514d5979d1756a10c8375e2176ec21500cadb161464736f6c63430008180033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220d958548954339e4559b2e73514d5979d1756a10c8375e2176ec21500cadb161464736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}