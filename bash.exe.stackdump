Stack trace:
Frame         Function      Args
0007FFFFAB00  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFAB00, 0007FFFF9A00) msys-2.0.dll+0x2118E
0007FFFFAB00  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFADD8) msys-2.0.dll+0x69BA
0007FFFFAB00  0002100469F2 (00021028DF99, 0007FFFFA9B8, 0007FFFFAB00, 000000000000) msys-2.0.dll+0x69F2
0007FFFFAB00  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFAB00  00021006A545 (0007FFFFAB10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFADE0  00021006B9A5 (0007FFFFAB10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE89B10000 ntdll.dll
7FFE87E20000 KERNEL32.DLL
7FFE874E0000 KERNELBASE.dll
7FFE88030000 USER32.dll
7FFE873B0000 win32u.dll
7FFE88770000 GDI32.dll
7FFE87870000 gdi32full.dll
7FFE872E0000 msvcp_win.dll
7FFE873E0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE87CA0000 advapi32.dll
7FFE885F0000 msvcrt.dll
7FFE89200000 sechost.dll
7FFE897F0000 RPCRT4.dll
7FFE87380000 bcrypt.dll
7FFE86A80000 CRYPTBASE.DLL
7FFE877E0000 bcryptPrimitives.dll
7FFE87C70000 IMM32.DLL
7FFE861D0000 nvinitx.dll
7FFE861C0000 VERSION.dll
00000F000000 detoured.dll
7FFE80000000 nvd3d9wrapx.dll
7FFE89380000 SETUPAPI.dll
7FFE87AF0000 cfgmgr32.dll
7FFE7FFD0000 nvdxgiwrapx.dll
