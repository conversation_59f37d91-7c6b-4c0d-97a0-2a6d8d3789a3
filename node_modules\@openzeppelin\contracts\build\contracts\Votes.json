{"_format": "hh-sol-artifact-1", "contractName": "Votes", "sourceName": "contracts/governance/utils/Votes.sol", "abi": [{"inputs": [], "name": "CheckpointUnorderedInsertion", "type": "error"}, {"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "timepoint", "type": "uint256"}, {"internalType": "uint48", "name": "clock", "type": "uint48"}], "name": "ERC5805FutureLookup", "type": "error"}, {"inputs": [], "name": "ERC6372InconsistentClock", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "name": "InvalidAccountNonce", "type": "error"}, {"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "uint8", "name": "bits", "type": "uint8"}, {"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "SafeCastOverflowedUintDowncast", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "expiry", "type": "uint256"}], "name": "VotesExpiredSignature", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "delegator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "fromDelegate", "type": "address"}, {"indexed": true, "internalType": "address", "name": "toDelegate", "type": "address"}], "name": "DelegateChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "delegate", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "previousVotes", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newVotes", "type": "uint256"}], "name": "DelegateVotesChanged", "type": "event"}, {"anonymous": false, "inputs": [], "name": "EIP712DomainChanged", "type": "event"}, {"inputs": [], "name": "CLOCK_MODE", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "clock", "outputs": [{"internalType": "uint48", "name": "", "type": "uint48"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}], "name": "delegate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "delegatee", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "expiry", "type": "uint256"}, {"internalType": "uint8", "name": "v", "type": "uint8"}, {"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "delegates", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "timepoint", "type": "uint256"}], "name": "getPastTotalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "timepoint", "type": "uint256"}], "name": "getPastVotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getVotes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}