from sqlalchemy.orm import Session
from api.v1.websockets.models import Notification, NotificationType, NotificationPriority
from api.v1.websockets.services import NotificationService
from api.v1.websockets.schemas import NotificationCreate
from api.db.database import <PERSON>L<PERSON>al
from datetime import datetime
from fastapi import Request
from typing import Optional
import user_agents  # pip install user-agents

import logging
from api.core.logging_config import get_logger
from fastapi import HTTP<PERSON>x<PERSON>, status
logger = get_logger(__name__)




def handle_auth_notification(user_id: int, action: str, request: Optional[Request] = None):
    """Handle auth-related notifications"""
    messages = {
        "signup": "Welcome! Your account has been created successfully.",
        "change_password": "Your password has been changed successfully.",
        "change_password_failed": "Failed to changed password.",
        "login": "You have successfully logged in.",
        "login_failed": "Login failed. Please check your credentials and try again.",
        "logout": "You have been logged out successfully.",
        "password_reset": "Your password has been reset successfully.",
        "google_login": "You have successfully logged in with Google.",
        "wallet_login": "You have successfully logged in with your wallet."
    }
    
    titles = {
        "signup": "Welcome to Your Account",
        "change_password": "Password Changed Successful",
        "change_password_failed": "Password Change Failed",
        "login": "Login Successful",
        "login_failed": "Login Failed",
        "logout": "Logout Successful",
        "password_reset": "Password Reset Successful",
        "google_login": "Google Login Successful",
        "wallet_login": "Wallet Login Successful"
    }
    
    if action in messages:
        message = messages[action]
        title = titles.get(action, "Authentication Notification")
        
        # Get browser and device info if request is provided
        browser_info = {}
        if request:
            user_agent = user_agents.parse(request.headers.get("user-agent", ""))
            browser_info = {
                "browser": f"{user_agent.browser.family} {user_agent.browser.version_string}",
                "os": f"{user_agent.os.family} {user_agent.os.version_string}",
                "device": user_agent.device.family,
                "ip_address": request.client.host,
                "timestamp": datetime.now().isoformat()
            }

        # Set priority based on security-related actions
        priority = NotificationPriority.HIGH if "reset" in action or "change" in action or "changed" in action else NotificationPriority.NORMAL
        
        # Create notification with enhanced structure
        notification = NotificationCreate(
            title=title,
            message=message,
            type=NotificationType.AUTH,
            priority=priority,
            metadata={
                "device_info": browser_info if browser_info else None,
                "transaction_details": {
                    "action_type": action,
                    "status": "success" if not "failed" in action else "failed",
                    "detail": "Authentication type - google" if "google" in action else 
                                "Authentication type - wallet" if "wallet" in action else "Authentication type - password",
                }
            },
            action_url=None
        )
        
        notification_service = NotificationService()
        notification_service.publish_notification(user_id=user_id, notification=notification)





# Authentication-specific exceptions
class AuthError(Exception):
    """Base exception for authentication-related errors"""
    pass

class InvalidCredentialsError(AuthError):
    """Exception raised for invalid credentials"""
    pass

class TokenExpiredError(AuthError):
    """Exception raised for expired tokens"""
    pass

class BlacklistedTokenError(AuthError):
    """Exception raised for blacklisted tokens"""
    pass

class MissingTokenError(AuthError):
    """Exception raised when token is missing"""
    pass

class PasswordMismatchError(AuthError):
    """Exception raised when passwords don't match"""
    pass

class GoogleAuthError(AuthError):
    """Exception raised during Google authentication"""
    pass

class WalletAuthError(AuthError):
    """Exception raised during wallet authentication"""
    pass

class APIKeyError(AuthError):
    """Exception raised for API key related errors"""
    pass



def handle_auth_error(error: Exception) -> None:
    """
    Convert authentication errors to appropriate HTTP exceptions
    """
    
    if isinstance(error, HTTPException):
        # If it's already an HTTPException, re-raise it
        raise error
    
    elif isinstance(error, InvalidCredentialsError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    elif isinstance(error, TokenExpiredError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    elif isinstance(error, BlacklistedTokenError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has been revoked",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    elif isinstance(error, MissingTokenError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication token is missing",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    elif isinstance(error, PasswordMismatchError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Passwords do not match"
        )
    
    elif isinstance(error, GoogleAuthError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Google authentication error: {str(error)}"
        )
    
    elif isinstance(error, WalletAuthError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Wallet authentication error: {str(error)}"
        )
    
    elif isinstance(error, APIKeyError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"API key error: {str(error)}"
        )
    
    elif isinstance(error, ValueError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(error)
        )
    
    elif isinstance(error, ConnectionError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Authentication service connection failed"
        )
    
    elif isinstance(error, TimeoutError):
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Authentication request timed out"
        )
    
    else:
        # Fallback for unexpected errors
        logger.error(f"Unexpected authentication error: {str(error)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected authentication error occurred"
        )