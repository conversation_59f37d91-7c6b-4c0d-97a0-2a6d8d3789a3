from sqlalchemy.orm import Session
from api.v1.websockets.models import Notification
from api.v1.websockets.services import NotificationService
from api.v1.websockets.schemas import NotificationCreate
from api.v1.websockets.models import NotificationType, NotificationPriority
from datetime import datetime

"""
# Notification helper functions
async def save_notification(db: Session, user_id: int, message: str, notification_type: str):
    notification = Notification(
        user_id=user_id,
        message=message,
        type=notification_type
    )
    db.add(notification)
    db.commit()
    db.refresh(notification)
    return notification

async def handle_contract_notification(db: Session, user_id: int, action: str, wallet_address: str = None):
    messages = {
        "transfer": f"Transferred {action.get('amount')} tokens from {action.get('sender_address')} to {action.get('recipient_address')}.",
        "approve": f"Approved {action.get('spender_address')} to spend {action.get('amount')} tokens on behalf of {action.get('owner_address')}.",   
        "transfer_from": f"Transferred {action.get('amount')} tokens from {action.get('sender_address')} to {action.get('recipient_address')}.",
        "increase_allowance": f"Increased allowance by {action.get('added_value')} tokens for spender {action.get('spender_address')} by owner {action.get('owner_address')}.",
        "decrease_allowance": f"Decreased allowance by {action.get('subtracted_value')} tokens for spender {action.get('spender_address')} by owner {action.get('owner_address')}.",
    }
    
    if action["type"] in messages:
        message = messages[action["type"]]
        await save_notification(
            db=db,
            user_id=user_id,
            message=message,
            notification_type="erc20"
        )
"""


import logging
from api.core.logging_config import get_logger
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from api.v1.websockets.models import Notification

# Configure logging
logger = get_logger(__name__)

# Base exception class for ERC20 operations
class ERC20Error(Exception):
    """Base exception for ERC20-related errors"""
    pass

# Transaction-specific exceptions
class TokenTransferError(ERC20Error):
    """Exception raised when token transfer fails"""
    pass

class InsufficientTokenBalanceError(ERC20Error):
    """Exception raised when token balance is insufficient for operation"""
    pass

class TokenApprovalError(ERC20Error):
    """Exception raised when token approval operation fails"""
    pass

class TokenMintError(ERC20Error):
    """Exception raised when token minting operation fails"""
    pass

class TokenBurnError(ERC20Error):
    """Exception raised when token burning operation fails"""
    pass

class AllowanceError(ERC20Error):
    """Exception raised when there's an issue with token allowance"""
    pass

class GasEstimationError(ERC20Error):
    """Exception raised when gas estimation fails for token operations"""
    pass

class TransactionPendingError(ERC20Error):
    """Exception raised when a transaction is still pending"""
    pass

class TransactionFailedError(ERC20Error):
    """Exception raised when a transaction fails"""
    pass

class ContractInteractionError(ERC20Error):
    """Exception raised for general contract interaction issues"""
    pass

class TokenValidationError(ERC20Error):
    """Exception raised when token data validation fails"""
    pass

class TokenDataFetchError(ERC20Error):
    """Exception raised when fetching token data fails"""
    pass

class RateLimitExceededError(ERC20Error):
    """Exception raised when token operation rate limit is exceeded"""
    pass

class TokenNotFoundError(ERC20Error):
    """Exception raised when token is not found"""
    pass

class InvalidTokenParameterError(ERC20Error):
    """Exception raised for invalid token parameters"""
    pass

class CacheError(ERC20Error):
    """Exception raised for caching-related errors"""
    pass



# Error handler function
def handle_erc20_error(error: Exception) -> None:
    """
    Convert ERC20 errors to appropriate HTTP exceptions
    """
    logger.error(f"ERC20 error: {str(error)}")
    
    if isinstance(error, HTTPException):
        # If it's already an HTTPException, re-raise it
        raise error
    
    elif isinstance(error, InsufficientTokenBalanceError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Insufficient token balance: {str(error)}"
        )
    
    elif isinstance(error, TokenTransferError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Token transfer failed: {str(error)}"
        )
    
    elif isinstance(error, TokenApprovalError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Token approval failed: {str(error)}"
        )
    
    elif isinstance(error, TokenMintError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Token minting failed: {str(error)}"
        )
    
    elif isinstance(error, TokenBurnError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Token burning failed: {str(error)}"
        )
    
    elif isinstance(error, AllowanceError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Token allowance error: {str(error)}"
        )
    
    elif isinstance(error, GasEstimationError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Gas estimation failed for token operation: {str(error)}"
        )
    
    elif isinstance(error, TransactionPendingError):
        raise HTTPException(
            status_code=status.HTTP_102_PROCESSING,
            detail=f"Transaction is still pending: {str(error)}"
        )
    
    elif isinstance(error, TransactionFailedError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Transaction failed: {str(error)}"
        )
    
    elif isinstance(error, TokenNotFoundError):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Token not found: {str(error)}"
        )
    
    elif isinstance(error, TokenValidationError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Token validation failed: {str(error)}"
        )
    
    elif isinstance(error, TokenDataFetchError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch token data: {str(error)}"
        )
    
    elif isinstance(error, RateLimitExceededError):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"Rate limit exceeded for token operations: {str(error)}"
        )
    
    elif isinstance(error, InvalidTokenParameterError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid token parameter: {str(error)}"
        )
    
    elif isinstance(error, ContractInteractionError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Contract interaction error: {str(error)}"
        )
    
    elif isinstance(error, ValueError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(error)
        )
    
    elif isinstance(error, ConnectionError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Blockchain service connection failed"
        )
    
    elif isinstance(error, TimeoutError):
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Blockchain request timed out"
        )
    
    
    elif isinstance(error, CacheError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service temporarily unavailable"
        )    
    
    
    else:
        # Fallback for unexpected errors
        logger.error(f"Unexpected ERC20 error: {str(error)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during token operation"
        )

# Notification helper functions
async def save_notification(db: Session, user_id: int, message: str, notification_type: str):
    """Helper function to save notifications"""

    notification_service = NotificationService()
    
    # Create notification with enhanced structure
    notification = NotificationCreate(
        title=f"{notification_type.capitalize()} Notification",
        message=message,
        type=getattr(NotificationType, notification_type.upper(), NotificationType.SYSTEM),
        priority=NotificationPriority.NORMAL,
        metadata={
            "timestamp": datetime.now().isoformat(),
            "notification_type": notification_type,
            "action": notification_type
        },
        action_url=None
    )
    
    await notification_service.publish_notification(user_id=user_id, notification=notification)

    """
    notification = Notification(
        user_id=user_id,
        message=message,
        type=notification_type
    )
    db.add(notification)
    db.commit()
    db.refresh(notification)

    notification_service = NotificationService()
    #await notification_service.publish_notification(user_id=user_id, notification=message)
    """


async def handle_contract_notification(db: Session, user_id: int, action: dict, wallet_address: str = None):
    """Handle ERC20-related notifications"""
    messages = {
        # Transaction creation notification
        "transaction_created": f"Created a {action.get('type')} transaction for {action.get('amount', '')} tokens.",
        
        # Existing operations
        "transfer": f"Transferred {action.get('amount')} tokens from {action.get('sender_address')} to {action.get('recipient_address')}.",
        "approve": f"Approved {action.get('spender_address')} to spend {action.get('amount')} tokens on behalf of {action.get('owner_address')}.",   
        "transfer_from": f"Transferred {action.get('amount')} tokens from {action.get('sender_address')} to {action.get('recipient_address')}.",
        "increase_allowance": f"Increased allowance by {action.get('added_value')} tokens for spender {action.get('spender_address')}.",
        "decrease_allowance": f"Decreased allowance by {action.get('subtracted_value')} tokens for spender {action.get('spender_address')}.",
        
        # Additional operations
        "mint": f"Minted {action.get('amount')} tokens to address {action.get('recipient_address')}.",
        "burn": f"Burned {action.get('amount')} tokens from address {action.get('address')}.",
        "transaction_pending": f"Your {action.get('operation_type')} transaction is pending confirmation.",
        "transaction_completed": f"Your {action.get('operation_type')} transaction was completed successfully.",
        "transaction_failed": f"Your {action.get('operation_type')} transaction failed: {action.get('reason', 'unknown error')}."
    }
    
    titles = {
        "transaction_created": "Transaction Created",
        "transfer": "Token Transfer",
        "approve": "Token Approval",
        "transfer_from": "Token Transfer",
        "increase_allowance": "Allowance Increased",
        "decrease_allowance": "Allowance Decreased",
        "mint": "Tokens Minted",
        "burn": "Tokens Burned",
        "transaction_pending": "Transaction Pending",
        "transaction_completed": "Transaction Completed",
        "transaction_failed": "Transaction Failed"
    }
    
    if action["type"] in messages:
        message = messages[action["type"]]
        title = titles.get(action["type"], "ERC20 Notification")
        
        # Set priority based on action type
        priority = NotificationPriority.HIGH if "failed" in action["type"] else NotificationPriority.NORMAL
        
        # Create notification with enhanced structure
        notification = NotificationCreate(
            title=title,
            message=message,
            type=NotificationType.CONTRACT,
            priority=priority,
            metadata={
                "timestamp": datetime.now().isoformat(),
                "contract_type": "erc20",
                "action": action["type"],
                "wallet_address": wallet_address,
                "transaction_details": {
                    "user_id": user_id,
                    "action_type": action["type"],
                    "amount": action.get("amount"),
                    "from_address": action.get("sender_address") or action.get("from_address"),
                    "to_address": action.get("recipient_address") or action.get("to_address"),
                    "status": "failed" if "failed" in action["type"] else 
                             "pending" if "pending" in action["type"] else "success"
                }
            },
            action_url=None
        )
        
        notification_service = NotificationService()
        await notification_service.publish_notification(user_id=user_id, notification=notification)