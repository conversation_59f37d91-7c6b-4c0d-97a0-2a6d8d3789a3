from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Foreign<PERSON>ey, String, DateTime, BIGINT, Text, JSON, Integer
from sqlalchemy.orm import relationship
from datetime import datetime, date, timezone
from api.db.database import Base
import passlib.hash as _hash


class BlackListToken(Base):
    __tablename__ = "blacklist_tokens"
    id = Column(BIGINT, primary_key=True, autoincrement=True, index=True)
    created_by = Column(BIGINT, ForeignKey('user.id'), index=True)
    token = Column(String(255), index=True)
    date_created = Column(DateTime(timezone=True), default=datetime.now)


class APIKey(Base):
    __tablename__ = "api_keys"

    id = Column(BIGINT, primary_key=True, autoincrement=True, index=True)
    user_id = Column(BIGINT, ForeignKey('user.id'), index=True)
    subscription_plan_id = Column(BIGINT, ForeignKey('subscription_plan.id'), nullable=False)

    name =  Column(String(255), nullable=True)
    api_key = Column(String(500), nullable=False, index=True, unique=True)
    key_hash = Column(Text, nullable=False)
    active = Column(Boolean, default=True)

    # Usage tracking
    current_monthly_requests = Column(Integer, default=0)
    current_daily_requests = Column(Integer, default=0)
    last_request_time = Column(DateTime(timezone=True))
    requests_this_minute = Column(Integer, default=0)
    last_reset_date = Column(DateTime(timezone=True), default=datetime.now)

    date_created = Column(DateTime(timezone=True), default=datetime.now)
    last_used = Column(DateTime(timezone=True))

    subscription_plan = relationship("SubscriptionPlan", back_populates="api_keys")

    def verify(self, api_key: str):
        return _hash.sha256_crypt.verify(api_key, self.key_hash)

    def check_rate_limit(self) -> bool:
        now = datetime.now(timezone.utc)

        # Reset daily counter
        if self.last_reset_date.date() != now.date():
            self.current_daily_requests = 0
            self.last_reset_date = now

        # Reset minute counter
        if self.last_request_time and (now - self.last_request_time).seconds >= 60:
            self.requests_this_minute = 0

        # Check limits
        if (self.current_monthly_requests >= self.subscription_plan.monthly_request_limit or
            self.current_daily_requests >= self.subscription_plan.daily_request_limit or
            self.requests_this_minute >= self.subscription_plan.api_rate_limit):
            return False

        return True

    def increment_usage(self):
        now = datetime.now(timezone.utc)
        self.current_monthly_requests += 1
        self.current_daily_requests += 1
        self.requests_this_minute += 1
        self.last_request_time = now
        self.last_used = now



# Helper function to validate API requests
def validate_api_request(api_key: APIKey, endpoint: str, payload_size: int) -> dict:
    """
    Validates an API request against the subscription plan limits
    Returns a dictionary with validation status and reason if failed
    """
    if not api_key.active:
        return {"valid": False, "reason": "API key is inactive"}

    if endpoint not in api_key.subscription_plan.allowed_endpoints:
        return {"valid": False, "reason": "Endpoint not allowed for this subscription tier"}

    if not api_key.check_rate_limit():
        return {"valid": False, "reason": "Rate limit exceeded"}

    if payload_size > api_key.subscription_plan.max_payload_size * 1024:
        return {"valid": False, "reason": "Payload size exceeds maximum allowed"}

    api_key.increment_usage()
    return {"valid": True, "reason": None}
