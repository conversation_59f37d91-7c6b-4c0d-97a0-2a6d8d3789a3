from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, Text, Integer, String, DateTime, BIGINT, select
from sqlalchemy.orm import relationship
from datetime import datetime, date
from api.db.database import Base, get_db
from config import config
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import OperationalError, DatabaseError
from sqlalchemy import text
from config import config
from api.core.logging_config import get_logger
import tenacity
import pytest
from main import app
from fastapi.testclient import TestClient

# Import all models to ensure they are registered with Base.metadata
# This is important for table creation to work correctly
from api.v1.user.models import User, Avater
from api.v1.auth.models import BlackListToken, APIKey
# Import other models as needed
from api.v1.user.models import *
from api.v1.auth.models import *
from api.v1.account.models import *
from api.v1.erc721_contract.models import *
from api.v1.erc20_contract.models import *
from api.v1.deploy_contracts.models import *
from api.v1.contracts.models import *
from api.v1.subscription.models import *
from api.v1.payment.models import *
from api.v1.websockets.models import *
from api.v1.wallet.models import *
from api.v1.plugins.models import *

# Create a mock class for AsyncSession to use in tests
class MockAsyncSession:
    """A mock AsyncSession for testing."""
    def __init__(self):
        self.committed = False
        self.rolled_back = False
        self.closed = False
        self.added_objects = []
        self.deleted_objects = []
        self.refreshed_objects = []
        self.execute_results = {}

    async def commit(self):
        self.committed = True
        return None

    async def rollback(self):
        self.rolled_back = True
        return None

    async def close(self):
        self.closed = True
        return None

    def add(self, obj):
        self.added_objects.append(obj)

    async def delete(self, obj):
        self.deleted_objects.append(obj)

    async def refresh(self, obj):
        self.refreshed_objects.append(obj)

    async def execute(self, query):
        # This is a simple mock that always returns None for email validation
        # You can customize this for more complex test scenarios
        from sqlalchemy.engine.result import ScalarResult

        class MockScalarResult:
            def scalar_one_or_none(self):
                return None

        return MockScalarResult()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            await self.rollback()
        await self.close()

logger = get_logger(__name__)

# --- Create ASYNC DB URL ---

def create_async_db_url() -> str:
    """
    Ensure config.DATABASE_URL starts with 'postgresql+asyncpg://'
    """
    # For testing, we should use the test database URL
    # Check if REPLICA_DATABASE_URL is available, otherwise fall back to DATABASE_URL
    if config.REPLICA_DATABASE_URL:
        db_url = config.REPLICA_DATABASE_URL
    else:
        db_url = config.DATABASE_URL

    if not db_url:
        raise ValueError("Test database URL is missing.")

    if not db_url.startswith("postgresql+asyncpg://"):
        raise ValueError("Database URL does not use asyncpg driver. Update config.")

    return db_url

try:
    async_test_engine = create_async_engine(create_async_db_url(), pool_pre_ping=True)
except Exception as e:
    logger.error(f"Failed to create test database engine: {str(e)}")
    raise

TestSessionLocal = sessionmaker(
    bind=async_test_engine,
    class_=MockAsyncSession,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False,
)

@pytest.fixture
async def session():
    # Create tables before each test
    async with async_test_engine.begin() as conn:
        # Drop all tables first to ensure clean state
        await conn.run_sync(Base.metadata.drop_all)
        logger.debug("Dropped tables.")
        # Create all tables defined in Base.metadata
        await conn.run_sync(Base.metadata.create_all)
        logger.debug("Created tables.")

    # Create a mock session for the test
    session = MockAsyncSession()
    yield session


@pytest.fixture
async def client(session):
    # Use the session fixture which already has tables created
    # Define an override for the get_db dependency
    async def override_get_db():
        # Return the session directly
        yield session

    # Override the get_db dependency
    app.dependency_overrides[get_db] = session

    # Create a test client
    with TestClient(app) as test_client:
        yield test_client

    # Clean up
    del app.dependency_overrides[get_db]
    logger.debug("Test client closed.")