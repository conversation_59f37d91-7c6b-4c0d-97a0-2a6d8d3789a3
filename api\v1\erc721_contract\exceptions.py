from sqlalchemy.orm import Session
from api.v1.websockets.models import Notification
import logging
from api.core.logging_config import get_logger
from fastapi import HTTPException, status
from api.v1.websockets.services import NotificationService


# Configure logging
logger = get_logger(__name__)

async def save_notification(user_id: int, message: str, notification_type: str):
    """Helper function to save notifications"""
    notification_service = NotificationService()
    await notification_service.publish_notification(user_id=user_id, notification=message,
                                                    notification_type=notification_type)



async def handle_contract_notification(user_id: int, contract_type: str, action: dict):
    """Handle contract-related notifications based on contract type"""
    messages = {
        "erc721": {
            # Transaction creation notifications
            "transaction_created": f"Created a {action.get('type')} transaction for token ID {action.get('token_id', '(new)')}.",
            
            # Existing operations
            "mint_nft": f"NFT with ID {action.get('token_id')} minted successfully to {action.get('wallet_address', 'your wallet')}.",
            "transfer_nft": f"NFT with ID {action.get('token_id')} transferred from {action.get('from_address', 'your wallet')} to {action.get('to_address')}.",
            "approve": f"Approval granted for NFT with ID {action.get('token_id')} from {action.get('owner_address', 'your wallet')} to {action.get('spender_address')}.",
            "approve_for_all": f"Approval for all NFTs granted from {action.get('owner_address', 'your wallet')} to operator {action.get('operator_address')}.",
            "renounce_ownership": f"Contract ownership renounced by {action.get('current_owner_address', 'you')}.",
            "transfer_ownership_to": f"Contract ownership transferred from {action.get('current_owner_address')} to you.",
            "transfer_ownership_from": f"Contract ownership transferred from you to {action.get('new_owner_address')}.",
            "transfer": f"NFT with ID {action.get('token_id')} transferred from {action.get('from_address', 'your wallet')} to {action.get('to_address')}.",
            
            # Additional status notifications
            "transaction_pending": f"Your {action.get('operation_type')} transaction for token ID {action.get('token_id', '(new)')} is pending confirmation.",
            "transaction_completed": f"Your {action.get('operation_type')} transaction for token ID {action.get('token_id', '(new)')} was completed successfully.",
            "transaction_failed": f"Your {action.get('operation_type')} transaction for token ID {action.get('token_id', '(new)')} failed: {action.get('reason', 'unknown error')}.",
            "metadata_update": f"Metadata for NFT with ID {action.get('token_id')} has been updated.",
            "safe_transfer": f"NFT with ID {action.get('token_id')} safely transferred from {action.get('from_address', 'your wallet')} to {action.get('to_address')}.",
            "safe_transfer_with_data": f"NFT with ID {action.get('token_id')} safely transferred with additional data from {action.get('from_address', 'your wallet')} to {action.get('to_address')}."
        },
    }
    
    if contract_type in messages and action["type"] in messages[contract_type]:
        message = messages[contract_type][action["type"]]
        await save_notification(
            user_id=user_id,
            message=message,
            notification_type=contract_type
        )
    else:
        logger.warning(f"Unknown action type for notification: {action['type']} in contract type {contract_type}")






















# Base exception class for ERC721 operations
class ERC721Error(Exception):
    """Base exception for ERC721-related errors"""
    pass

# Token-specific exceptions
class NFTMintError(ERC721Error):
    """Exception raised when NFT minting fails"""
    pass

class NFTTransferError(ERC721Error):
    """Exception raised when NFT transfer fails"""
    pass

class NFTApprovalError(ERC721Error):
    """Exception raised when NFT approval fails"""
    pass

class TokenURIError(ERC721Error):
    """Exception raised when there's an issue with token URI"""
    pass

class OwnershipError(ERC721Error):
    """Exception raised when there's an issue with ownership operations"""
    pass

class TokenNotFoundError(ERC721Error):
    """Exception raised when token ID doesn't exist"""
    pass

class NotTokenOwnerError(ERC721Error):
    """Exception raised when caller is not the token owner"""
    pass

class UnauthorizedOperatorError(ERC721Error):
    """Exception raised when operator is not authorized"""
    pass

class MetadataError(ERC721Error):
    """Exception raised when metadata processing fails"""
    pass

class IPFSUploadError(ERC721Error):
    """Exception raised when IPFS upload fails"""
    pass

class FileProcessingError(ERC721Error):
    """Exception raised when file processing fails"""
    pass

class GasEstimationError(ERC721Error):
    """Exception raised when gas estimation fails for NFT operations"""
    pass

class TransactionPendingError(ERC721Error):
    """Exception raised when a transaction is still pending"""
    pass

class TransactionFailedError(ERC721Error):
    """Exception raised when a transaction fails"""
    pass

class ContractInteractionError(ERC721Error):
    """Exception raised for general contract interaction issues"""
    pass

class InvalidTokenIdError(ERC721Error):
    """Exception raised for invalid token ID"""
    pass

class InvalidAddressError(ERC721Error):
    """Exception raised for invalid address"""
    pass

# Error handler function
def handle_erc721_error(error: Exception) -> None:
    """
    Convert ERC721 errors to appropriate HTTP exceptions
    """
    logger.error(f"ERC721 error: {str(error)}")
    
    if isinstance(error, HTTPException):
        # If it's already an HTTPException, re-raise it
        raise error
    
    elif isinstance(error, NFTMintError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"NFT minting failed: {str(error)}"
        )
    
    elif isinstance(error, NFTTransferError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"NFT transfer failed: {str(error)}"
        )
    
    elif isinstance(error, NFTApprovalError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"NFT approval failed: {str(error)}"
        )
    
    elif isinstance(error, TokenURIError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Token URI error: {str(error)}"
        )
    
    elif isinstance(error, OwnershipError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Ownership operation failed: {str(error)}"
        )
    
    elif isinstance(error, TokenNotFoundError):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Token not found: {str(error)}"
        )
    
    elif isinstance(error, NotTokenOwnerError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Not the token owner: {str(error)}"
        )
    
    elif isinstance(error, UnauthorizedOperatorError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Unauthorized operator: {str(error)}"
        )
    
    elif isinstance(error, MetadataError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Metadata error: {str(error)}"
        )
    
    elif isinstance(error, IPFSUploadError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"IPFS upload failed: {str(error)}"
        )
    
    elif isinstance(error, FileProcessingError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File processing failed: {str(error)}"
        )
    
    elif isinstance(error, GasEstimationError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Gas estimation failed: {str(error)}"
        )
    
    elif isinstance(error, TransactionPendingError):
        raise HTTPException(
            status_code=status.HTTP_102_PROCESSING,
            detail=f"Transaction is still pending: {str(error)}"
        )
    
    elif isinstance(error, TransactionFailedError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Transaction failed: {str(error)}"
        )
    
    elif isinstance(error, InvalidTokenIdError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid token ID: {str(error)}"
        )
    
    elif isinstance(error, InvalidAddressError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid address: {str(error)}"
        )
    
    elif isinstance(error, ContractInteractionError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Contract interaction error: {str(error)}"
        )
    
    elif isinstance(error, ValueError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(error)
        )
    
    elif isinstance(error, ConnectionError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Blockchain service connection failed"
        )
    
    elif isinstance(error, TimeoutError):
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Blockchain request timed out"
        )
    
    else:
        # Fallback for unexpected errors
        logger.error(f"Unexpected ERC721 error: {str(error)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during NFT operation"
        )
