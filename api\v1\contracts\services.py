from web3 import Web3, AsyncWeb3
from typing import List, Dict, Any, Optional
from config import config
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
import json
from api.core.blockchain_dep import class_exponential_backoff
from redis.asyncio import Redis
from .exceptions import logger

PROVIDER = config.PROVIDER
PRODUCTION = config.PRODUCTION
PLATFORM_WALLET = config.PLATFORM_WALLET
CACHE_TIME = 6000
CHUNCK_SIZE = 50




@class_exponential_backoff()
class ChainID:

    def __init__(self, user_id: int, web3: AsyncWeb3, redis: Optional[Redis] = None):
        self.user_id = user_id
        self.redis = redis
        self.cache = CACHE_TIME
        self.chunk_size = CHUNCK_SIZE
        self.web3 = web3
        
        
    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


    async def get_network_id(self) -> int:
        """Get network chain ID"""
        cache_key = f"chain_id:{self.user_id}"
        try:
            cached_id = await self.redis.get(cache_key)
            if cached_id:
                chain_id = json.loads(cached_id)
            else:
                chain_id = await self.web3.eth.chain_id
                await self.redis.set(
                    cache_key,
                    json.dumps(chain_id)
                )
                await self.redis.expire(cache_key, self.cache)
            return chain_id

        except Exception as e:
            logger.critical(f"Unexpected error in get_contract_events: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred"
            )
