from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON>ey, JSON, Text, Numeric, Integer, String, DateTime, BIGINT
from sqlalchemy.orm import relationship
from datetime import datetime, date, timezone
from api.db.database import Base
from datetime import datetime, timedelta, timezone



import json
from web3 import Web3
from decimal import Decimal

def make_json_serializable(obj):
    if isinstance(obj, bytes):
        return Web3.to_hex(obj)
    if isinstance(obj, Decimal):
        return str(obj)
    if hasattr(obj, '__dict__'):
        return obj.__dict__
    return str(obj)


class ToBeSigned(Base):
    __tablename__ = "tobesigned_contract"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('user.id'), index=True)
    nft_id = Column(Integer, ForeignKey('nfts.id'), nullable=True, index=True)

    transaction_data = Column(JSON, nullable=False)
    total_cost_wei = Column(Numeric(32), nullable=False)
    total_cost_eth = Column(Numeric(32, 18), nullable=False)
    status = Column(String(20), default='pending')
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    expires_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc) + timedelta(minutes=15))

    def __init__(self, **kwargs):
        if 'transaction_data' in kwargs:
            # Ensure transaction_data is JSON serializable
            kwargs['transaction_data'] = json.loads(
                json.dumps(kwargs['transaction_data'], default=make_json_serializable)
            )
        super().__init__(**kwargs)