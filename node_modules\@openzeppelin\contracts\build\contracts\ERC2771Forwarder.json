{"_format": "hh-sol-artifact-1", "contractName": "ERC2771<PERSON><PERSON><PERSON><PERSON>", "sourceName": "contracts/metatx/ERC2771Forwarder.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "uint48", "name": "deadline", "type": "uint48"}], "name": "ERC2771ForwarderExpiredRequest", "type": "error"}, {"inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}], "name": "ERC2771ForwarderInvalidSigner", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "requestedValue", "type": "uint256"}, {"internalType": "uint256", "name": "msgValue", "type": "uint256"}], "name": "ERC2771ForwarderMismatchedValue", "type": "error"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}, {"internalType": "address", "name": "forwarder", "type": "address"}], "name": "ERC2771UntrustfulTarget", "type": "error"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "currentNonce", "type": "uint256"}], "name": "InvalidAccountNonce", "type": "error"}, {"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}, {"anonymous": false, "inputs": [], "name": "EIP712DomainChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "signer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "nonce", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "success", "type": "bool"}], "name": "ExecutedForwardRequest", "type": "event"}, {"inputs": [], "name": "eip712Domain", "outputs": [{"internalType": "bytes1", "name": "fields", "type": "bytes1"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "uint256", "name": "chainId", "type": "uint256"}, {"internalType": "address", "name": "verifyingContract", "type": "address"}, {"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "uint256[]", "name": "extensions", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gas", "type": "uint256"}, {"internalType": "uint48", "name": "deadline", "type": "uint48"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "internalType": "struct ERC2771Forwarder.ForwardRequestData", "name": "request", "type": "tuple"}], "name": "execute", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gas", "type": "uint256"}, {"internalType": "uint48", "name": "deadline", "type": "uint48"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "internalType": "struct ERC2771Forwarder.ForwardRequestData[]", "name": "requests", "type": "tuple[]"}, {"internalType": "address payable", "name": "refundReceiver", "type": "address"}], "name": "executeBatch", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "gas", "type": "uint256"}, {"internalType": "uint48", "name": "deadline", "type": "uint48"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "internalType": "struct ERC2771Forwarder.ForwardRequestData", "name": "request", "type": "tuple"}], "name": "verify", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}