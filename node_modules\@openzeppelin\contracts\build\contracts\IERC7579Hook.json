{"_format": "hh-sol-artifact-1", "contractName": "IERC7579Hook", "sourceName": "contracts/interfaces/draft-IERC7579.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}], "name": "isModuleType", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onInstall", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onUninstall", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "hookData", "type": "bytes"}], "name": "postCheck", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "msgSender", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "msgData", "type": "bytes"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "bytes", "name": "hookData", "type": "bytes"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}