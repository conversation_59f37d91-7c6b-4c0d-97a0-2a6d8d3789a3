from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, Foreign<PERSON>ey, JSON, Text, Integer, String, DateTime, BIGINT
from sqlalchemy.orm import relationship
from datetime import datetime, date, timezone
from api.db.database import Base

class Token(Base):
    __tablename__ = 'tokens'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String)
    symbol = Column(String)
    contract_address = Column(String, unique=True)
    decimals = Column(Integer)

class TokenBalance(Base):
    __tablename__ = 'token_balances'
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer)  # ForeignKey(User.id)
    token_id = Column(Integer)  # ForeignKey(Token.id)
    balance = Column(String)  # Store as a string to handle large numbers

class Transaction(Base):
    __tablename__ = 'transactions'
    id = Column(Integer, primary_key=True, index=True)
    tx_hash = Column(String, unique=True, index=True)  # Transaction hash
    user_id = Column(Integer)  # ForeignKey(User.id)
    contract_address = Column(String)  # Related contract
    timestamp = Column(String)  # Store as ISO 8601 string
    method = Column(String)  # E.g., "mint", "transfer"
    status = Column(String, default="pending")  # Pending, success, failed








class NFT(Base):
    __tablename__ = 'nfts'
    id = Column(Integer, primary_key=True, index=True)
    created_by = Column(Integer, ForeignKey('user.id'), index=True)
    contract_id = Column(Integer, ForeignKey('contract_deployments.id'), index=True)
    metadata_id = Column(Integer, ForeignKey('nft_metadata.id'), nullable=True, index=True)
    token_id = Column(Integer, nullable=False)
    metadata_uri = Column(String, nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    status = Column(String, nullable=True, default="pending")

    # Relationships
    #owner = relationship("User", back_populates="nfts")
    #contract = relationship("ContractDeployment", back_populates="nfts")
    nft_metadata = relationship("NFTMetadata", back_populates="nft")  # Renamed attribute

class NFTMetadata(Base):
    __tablename__ = 'nft_metadata'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    image = Column(String, nullable=True)
    attributes = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    # Relationship
    nft = relationship("NFT", back_populates="nft_metadata")  # Renamed attribute

