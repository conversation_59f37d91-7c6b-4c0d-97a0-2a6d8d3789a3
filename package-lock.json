{"name": "atlas-api", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "atlas-api", "version": "1.0.0", "license": "ISC", "dependencies": {"@openzeppelin/contracts": "^5.2.0"}}, "node_modules/@openzeppelin/contracts": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/@openzeppelin/contracts/-/contracts-5.2.0.tgz", "integrity": "sha512-bxjNie5z89W1Ea0NZLZluFh8PrFNn9DH8DQlujEok2yjsOlraUPKID5p1Wk3qdNbf6XkQ1Os2RvfiHrrXLHWKA==", "license": "MIT"}}}