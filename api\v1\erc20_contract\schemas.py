from pydantic import BaseModel
from typing import Optional, Any
from decimal import Decimal
from enum import Enum


class TransferRequest(BaseModel):
    recipient_address: str
    amount: Decimal

class ApprovalRequest(BaseModel):
    spender_address: str
    amount: Decimal

class TransferFromRequest(BaseModel):
    sender_address: str
    recipient_address: str
    amount: Decimal

class IncreaseAllowanceRequest(BaseModel):
    spender_address: str
    added_value: Decimal

class DecreaseAllowanceRequest(BaseModel):
    spender_address: str
    subtracted_value: Decimal

class MintRequest(BaseModel):
    recipient_address: str
    amount: Decimal


class TransactionDetails(BaseModel):
    hash: str
    from_address: str
    to: Optional[str]
    value: int
    gas: int
    gas_price: int
    input_data: str
    transaction_index: int
    type: int

class ReceiptDetails(BaseModel):
    status: str
    contract_address: Optional[str]
    gas_used: int
    block_number: int
    logs: str

class TransactionResponse(BaseModel):
    transaction: TransactionDetails
    receipt: ReceiptDetails




class ERC20Write(str, Enum):
    TRANSFER = "transfer"
    APPROVE = "approve"
    TRANSFER_FROM = "transferFrom"
    INCREASE_ALLOWANCE = "increaseAllowance"
    DECREASE_ALLOWANCE = "decreaseAllowance"
    MINT = "mint"