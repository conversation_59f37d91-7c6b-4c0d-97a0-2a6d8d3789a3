from datetime import datetime, date, timezone
from api.db.database import Base
import passlib.hash as _hash
from enum import Enum
from sqlalchemy import Column, BIGINT, String, Text, Boolean, DateTime, ForeignKey, Integer, JSON, Enum as SQLEnum
from sqlalchemy.orm import relationship

# Payment-related enums and models
class PaymentProvider(str, Enum):
    PAYSTACK = "PAYSTACK"
    STRIPE = "STRIPE"
    MONNIFY = "MONNIFY"

class PaymentStatus(str, Enum):
    PENDING = "PENDING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

class Duration(str, Enum):
    MONTHLY = "MONTHLY"
    YEARLY = "YEARLY"

class Payment(Base):
    __tablename__ = "payments"

    id = Column(BIGINT, primary_key=True, index=True, autoincrement=True)
    user_id = Column(BIGINT, ForeignKey('user.id'), index=True, nullable=False)
    subscription_plan_id = Column(BIGINT, ForeignKey('subscription_plan.id'), index=True, nullable=False)
    amount = Column(BIGINT, nullable=False)
    provider = Column(SQLEnum(PaymentProvider), nullable=False)
    provider_reference = Column(String(255), nullable=True)
    status = Column(SQLEnum(PaymentStatus), default=PaymentStatus.PENDING)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    duration_days = Column(BIGINT, nullable=True)

