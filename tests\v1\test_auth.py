from api.core.logging_config import get_logger
import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta, timezone
from jose import jwt
from sqlalchemy import select

from main import app
from api.v1.user.models import User as UserModel
from api.v1.auth.services import Auth
from api.v1.user.services import UserService
from api.v1.auth.exceptions import handle_auth_error, InvalidCredentialsError, GoogleAuthError
from api.v1.auth import schemas as auth_schema
from api.v1.user import schemas as user_schema
from config import config
import httpx
from httpx import ASGITransport, AsyncClient
from tests.v1.conftest import get_unique_test_user_data

logger = get_logger(__name__)

# Constants for testing
TEST_USER_DATA = {
    "first_name": "Test",
    "last_name": "User",
    "email": "<EMAIL>",
    "password": "Password123"
}

TEST_LOGIN_DATA = {
    "email": "<EMAIL>",
    "password": "Password123"
}

# Helper functions
def create_test_user_model():
    """Create a test user model instance"""
    return UserModel(
        id=1,
        first_name=TEST_USER_DATA["first_name"],
        last_name=TEST_USER_DATA["last_name"],
        email=TEST_USER_DATA["email"],
        password=Auth.hash_password(TEST_USER_DATA["password"]),
        is_active=True,
        date_created=datetime.now(timezone.utc),
        last_updated=datetime.now(timezone.utc)
    )

def create_test_user_schema():
    """Create a test user schema instance"""
    return user_schema.ShowUser(
        id=1,
        first_name=TEST_USER_DATA["first_name"],
        last_name=TEST_USER_DATA["last_name"],
        email=TEST_USER_DATA["email"],
        is_active=True,
        is_deleted=False,
        date_created=datetime.now(timezone.utc),
        last_updated=datetime.now(timezone.utc)
    )

def create_access_token(user_id):
    """Create a test access token"""
    expires = datetime.now(timezone.utc) + timedelta(minutes=15)
    to_encode = {"id": user_id, "exp": expires}
    return jwt.encode(to_encode, config.JWT_SECRET, algorithm=config.ALGORITHM)



import pytest
from httpx import AsyncClient
from api.core.logging_config import get_logger

logger = get_logger(__name__)

# Generate a unique email for this specific test to avoid conflicts
import uuid

def get_unique_signup_data():
    unique_id = str(uuid.uuid4())[:8]
    return {
        "first_name": "Signup",
        "last_name": "Test",
        "email": f"signup.test.{unique_id}@example.com",
        "password": "Password123"
    }

@pytest.mark.asyncio
async def test_signup_success(client):
    """Test successful user signup"""
    signup_data = get_unique_signup_data()
    logger.info(f"Attempting signup for: {signup_data['email']}")

    with patch('api.v1.auth.services_email.EmailVerification.send_email') as mock_send_email:
        mock_send_email.return_value = True

        response = await client.post("/auth/signup", json=signup_data)

        if response.status_code != 201:
            logger.error(f"Signup failed. Status: {response.status_code}, Response: {response.text}")

        assert response.status_code == 201
        json_response = response.json()
        assert "message" in json_response
        assert json_response["message"] == "Email verification code sent!"




@pytest.mark.asyncio
async def test_signup_duplicate_email(client):
    """Test signup with duplicate email"""
    user_data = get_unique_test_user_data()

    with patch('api.v1.auth.services_email.EmailVerification.send_email') as mock_send_email:
        mock_send_email.return_value = True

        await client.post("/auth/signup", json=user_data)
        response = await client.post("/auth/signup", json=user_data)

        assert response.status_code == 403
        json_response = response.json()
        assert "detail" in json_response
        assert "This email is already in use." in json_response["detail"]



@pytest.mark.asyncio
async def test_verify_email_success(client, test_user):
    """Test successful email verification"""
    verification_data = {
        "email": test_user["email"],
        "verification_code": test_user["verification_code"]
    }

    response = await client.post("/auth/verify/confirm", json=verification_data)

    assert response.status_code == 200
    json_response = response.json()
    assert "access_token" in json_response
    assert "Email verified successfully" in json_response["message"]
    assert "subscription_plan" in json_response
    assert json_response["subscription_plan"] == "Basic plan"


@pytest.mark.asyncio
async def test_verify_email_invalid_code(client, test_user):
    """Test email verification with invalid code"""
    verification_data = {
        "email": test_user["email"],
        "verification_code": "invalid"
    }

    response = await client.post("/auth/verify/confirm", json=verification_data)

    assert response.status_code == 400
    json_response = response.json()
    assert "detail" in json_response
    assert "Invalid verification code" in json_response["detail"]


@pytest.mark.asyncio
async def test_login_success(client, test_user):
    """Test successful login using database user"""
    response = await client.post("/auth/login", data={
        "username": test_user["email"],
        "password": test_user["password"]
    })

    assert response.status_code == 200
    json_response = response.json()
    assert "access_token" in json_response
    assert "token_type" in json_response
    assert json_response["token_type"] == "bearer"
    assert json_response["data"]["email"] == test_user["email"]


@pytest.mark.asyncio
async def test_login_invalid_credentials(client, test_user):
    """Test login with invalid credentials"""
    response = await client.post("/auth/login", data={
        "username": test_user["email"],
        "password": "wrong_password"
    })


    assert response.status_code == 401
    json_response = response.json()
    assert "detail" in json_response


# Test password reset
@pytest.mark.asyncio
async def test_forgot_password_success(client, test_user):
    """Test successful password reset request"""
    # Mock the email sending functionality
    with patch('api.v1.auth.services_email.EmailVerification.send_email') as mock_send_email:
        # Configure the mock to return True
        mock_send_email.return_value = True

        reset_request = {
            "email": test_user["email"]
        }

        response = await client.post("/auth/forgot-password", json=reset_request)

        # Check the response
        assert response.status_code == 200
        json_response = response.json()
        assert "Password reset code sent" in json_response["detail"]


@pytest.mark.asyncio
async def test_reset_password_success(client, test_user, async_db):
    """Test successful password reset"""
    query = select(UserModel).where(UserModel.email == test_user["email"])
    result = await async_db.execute(query)
    user = result.scalar_one_or_none()

    reset_code = "123456"
    user.reset_token = reset_code
    user.reset_token_expiry = datetime.now(timezone.utc) + timedelta(hours=1)
    await async_db.commit()

    reset_data = {
        "email": test_user["email"],
        "code": reset_code,
        "new_password": "NewPassword123"
    }

    response = await client.post("/auth/reset-password", json=reset_data)

    assert response.status_code == 200
    json_response = response.json()
    assert "detail" in json_response
    assert "Password reset successful" in json_response["detail"]

    login_response = await client.post("/auth/login", data={
        "username": test_user["email"],
        "password": "NewPassword123"
    })

    assert login_response.status_code == 200
    assert "access_token" in login_response.json()


@pytest.mark.asyncio
async def test_authenticated_reset_password_success(client, test_verified_user):
    """Test successful password reset when authenticated"""

    reset_data = {
        "current_password": test_verified_user["password"],
        "new_password": "UpdatedPassword123",
        "confirm_password": "UpdatedPassword123"
    }

    response = await client.put(
        "/auth/reset-password",
        json=reset_data,
        headers={"accept": "application/json", "Authorization": f"Bearer {test_verified_user["access_token"]}"}
    )

    assert response.status_code == 200
    json_response = response.json()
    assert "message" in json_response
    assert "Password reset successfully" in json_response["message"]

    # Verify we can login with the new password
    new_login_response = await client.post("/auth/login", data={
        "username": test_verified_user["email"],
        "password": "UpdatedPassword123"
    })

    assert new_login_response.status_code == 200
    assert "access_token" in new_login_response.json()


@pytest.mark.asyncio
async def test_authenticated_reset_password_mismatch(client, test_verified_user):
    """Test password reset with mismatched passwords"""

    reset_data = {
        "current_password": test_verified_user["password"],
        "new_password": "UpdatedPassword123",
        "confirm_password": "DifferentPassword123"
    }

    response = await client.put(
        "/auth/reset-password",
        json=reset_data,
        headers={"accept": "application/json", "Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 400
    json_response = response.json()
    assert "detail" in json_response
    assert "Passwords do not match" in json_response["detail"]


@pytest.mark.asyncio
async def test_authenticated_reset_password_wrong_current(client, test_verified_user):
    """Test password reset with wrong current password"""

    reset_data = {
        "current_password": "WrongPassword123",
        "new_password": "UpdatedPassword123",
        "confirm_password": "UpdatedPassword123"
    }

    response = await client.put(
        "/auth/reset-password",
        json=reset_data,
        headers={"accept": "application/json", "Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 401
    json_response = response.json()
    assert "detail" in json_response
    assert "Invalid" in json_response["detail"]


@pytest.mark.asyncio
async def test_logout_success(client, test_verified_user):
    """Test successful logout"""

    response = await client.post(
        "/auth/logout",
        headers={"accept": "application/json", "Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    json_response = response.json()
    assert "message" in json_response
    assert "Logged out successfully" in json_response["message"]



@pytest.mark.asyncio
async def test_google_login_url(client):
    """Test that Google login URL is returned correctly"""
    response = await client.get("/auth/login/google")

    assert response.status_code == 200
    json_response = response.json()
    assert "url" in json_response
    assert "accounts.google.com" in json_response["url"]
    assert config.GOOGLE_CLIENT_ID in json_response["url"]
    assert config.GOOGLE_REDIRECT_URI in json_response["url"]


@pytest.mark.asyncio
async def test_google_callback_success(client, async_db):
    """Test successful Google authentication callback"""
    # Mock the requests.post and requests.get calls
    with patch('requests.post') as mock_post, patch('requests.get') as mock_get:
        # Configure the mock responses
        mock_post_response = MagicMock()
        mock_post_response.json.return_value = {"access_token": "fake_access_token"}
        mock_post.return_value = mock_post_response

        mock_get_response = MagicMock()
        mock_get_response.json.return_value = {
            "id": "12345",
            "email": "<EMAIL>",
            "verified_email": True,
            "name": "Google User",
            "given_name": "Google",
            "family_name": "User",
            "picture": "https://example.com/profile.jpg"
        }
        mock_get.return_value = mock_get_response

        response = await client.get("/auth/callback/google?code=fake_auth_code")

        assert response.status_code == 200
        json_response = response.json()
        assert "access_token" in json_response
        assert "data" in json_response
        assert json_response["data"]["email"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_google_callback_failure(client):
    """Test Google authentication callback with error"""
    # Mock the requests.post to raise an exception
    with patch('requests.post') as mock_post:
        mock_post.side_effect = Exception("Connection error")

        response = await client.get("/auth/callback/google?code=invalid_code")

        assert response.status_code == 400
        json_response = response.json()
        assert "detail" in json_response


@pytest.mark.asyncio
async def test_wallet_callback_success(client, async_db):
    """Test successful wallet authentication callback"""
    wallet_address = "******************************************"

    response = await client.get(f"/auth/callback/wallet?address={wallet_address}")

    print(response.json())

    assert response.status_code == 200
    json_response = response.json()
    assert "access_token" in json_response
    assert "data" in json_response

    # Verify the user was created in the database
    query = select(UserModel).where(UserModel.email == f"{wallet_address}@polygon.wallet")
    result = await async_db.execute(query)
    user = result.scalar_one_or_none()
    assert user is not None
