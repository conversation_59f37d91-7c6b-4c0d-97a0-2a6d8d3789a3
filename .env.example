# ── Primary DB ─────────────────────────────
DB_TYPE=postgresql
DB_USER=ozura_admin
DB_PASSWORD=mv2kXDDtwPLx24K
DB_HOST=ledgerdb-uat-postgres.postgres.database.azure.com
DB_PORT=5432
DB_NAME=atlas_api
DB_SSL=True
DATABASE_URL=postgresql+asyncpg://ozura_admin:<EMAIL>:5432/atlas_api

DB_POOL_SIZE=20
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800
DB_MAX_RETRIES=3
DB_RETRY_DELAY=5

# ── JWT / Auth ────────────────────────────
JWT_SECRET=4J8f9s8d7f6g5h4j3k2l1m0n9b8v7c6x5z4a3s2d1f0g9h8j7k6l5m4n3b2v1c0
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=480
JWT_REFRESH_EXPIRY=5

# ── Google OAuth ──────────────────────────
GOOGLE_CLIENT_ID=935539350702-pt0rfvrvjri7brm3p1p96kbssskfootm.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URI=http://localhost:3000/dashboard
TOKEN_URL=https://oauth2.googleapis.com/token
BASE_URL=https://api.server.stellus.com

# ── Blockchain / ERC Deployment ───────────
PRODUCTION=False
ALCHEMY_API_KEY=************************************
PROVIDER=https://polygon-amoy.g.alchemy.com/v2/************************************
PLATFORM_WALLET=******************************************
PLATFORM_PRIVATE_KEY=0xc9771e1a1eef172c3fd85ac4510b937bb768def9af60a092472b9c3e671598f6
PLATFORM_FEE_PERCENTAGE=10
PLATFORM_FLAT_FEE=0.000002

# ── Pinata ────────────────────────────────
PINATA_API_KEY=c3bf3724a733b5d2ec86
PINATA_SECRET_KEY=****************************************************************

# ── Email / SMTP ──────────────────────────
EMAIL_SENDER=<EMAIL>
EMAIL_PASSWORD=kiui rdtp vqau aktu
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
VERIFICATION_TOKEN_LENGTH=32
VERIFICATION_TOKEN_EXPIRY_HOURS=24

# ── Redis ─────────────────────────────────
REDIS_HOST=srv-captain--redis
REDIS_PORT=6379
REDIS_PASSWORD=atlas-redis
REDIS_URL=redis://:atlas-redis@srv-captain--redis:6379/0
