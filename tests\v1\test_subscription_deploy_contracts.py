import pytest
import pytest_asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from web3 import AsyncWeb3, Web3
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import UploadFile
import io
import json
import os

from api.v1.deploy_contracts.models import ContractDeployment, TransactionType, ToBeSigned
from api.v1.deploy_contracts.services import DeployErc20
from api.v1.subscription.models import SubscriptionPlan, SubscriptionTier, user_subscription
from api.core.logging_config import get_logger
from tests.v1.test_account import TEST_PROVIDER, TEST_ADDRESS, MockRedis

logger = get_logger(__name__)

# Test constants
TEST_CONTRACT_ADDRESS = "0x742d35Cc6634C0532925a3b844Bc454e4438f44e"
TEST_BLOCK_NUMBER = 1000000
TEST_TRANSACTION_HASH = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
TEST_PRIVATE_KEY = "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"

# Sample ABI and bytecode for mocking
SAMPLE_ABI = [
    {
        "inputs": [
            {"internalType": "string", "name": "name_", "type": "string"},
            {"internalType": "string", "name": "symbol_", "type": "string"},
            {"internalType": "address", "name": "_platformWallet", "type": "address"},
            {"internalType": "uint256", "name": "_platformFeePercentage", "type": "uint256"},
            {"internalType": "uint256", "name": "_flatFeeAmount", "type": "uint256"}
        ],
        "stateMutability": "nonpayable",
        "type": "constructor"
    },
    {
        "inputs": [],
        "name": "name",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "symbol",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    }
]

SAMPLE_BYTECODE = "0x608060405234801561001057600080fd5b50610771806100206000396000f3fe608060405234801561001057600080fd5b50600436106100415760003560e01c8063..."

# Mock compiled contract output
MOCK_COMPILED_CONTRACT = {
    "contracts": {
        "erc_twenty.sol": {
            "ERC20Token": {
                "abi": SAMPLE_ABI,
                "evm": {
                    "bytecode": {
                        "object": SAMPLE_BYTECODE
                    }
                }
            }
        }
    }
}

MOCK_COMPILED_CONTRACT_721 = {
    "contracts": {
        "erc_seventwoone.sol": {
            "ERC721Token": {
                "abi": SAMPLE_ABI,
                "evm": {
                    "bytecode": {
                        "object": SAMPLE_BYTECODE
                    }
                }
            }
        }
    }
}

@pytest_asyncio.fixture
async def test_rpc_web3():
    """Create a real Web3 instance connected to a test RPC"""
    # Use a local Ganache or other test RPC
    # For testing purposes, we'll use a mock but in a real scenario, 
    # you would connect to an actual test RPC like Ganache
    mock = AsyncMock()
    mock.eth = AsyncMock()
    mock.eth.chain_id = 1337  # Local test chain ID
    mock.eth.get_block_number = AsyncMock(return_value=TEST_BLOCK_NUMBER)
    mock.eth.get_transaction_count = AsyncMock(return_value=0)
    mock.eth.get_transaction = AsyncMock(return_value={
        'hash': TEST_TRANSACTION_HASH,
        'from': TEST_ADDRESS,
        'to': None,  # Contract creation
        'value': 0,
        'gas': 2000000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'blockHash': '0x...',
        'blockNumber': TEST_BLOCK_NUMBER,
        'transactionIndex': 0
    })
    mock.eth.get_transaction_receipt = AsyncMock(return_value={
        'transactionHash': TEST_TRANSACTION_HASH,
        'blockHash': '0x...',
        'blockNumber': TEST_BLOCK_NUMBER,
        'contractAddress': TEST_CONTRACT_ADDRESS,
        'cumulativeGasUsed': 1000000,
        'effectiveGasPrice': Web3.to_wei(50, 'gwei'),
        'gasUsed': 1000000,
        'logs': [],
        'logsBloom': '0x...',
        'status': 1,  # Success
        'transactionIndex': 0
    })
    
    # Mock contract instance
    mock_contract = AsyncMock()
    mock_contract.address = TEST_CONTRACT_ADDRESS
    mock_contract.functions = AsyncMock()
    mock_contract.functions.name = AsyncMock()
    mock_contract.functions.name.call = AsyncMock(return_value="Test Token")
    mock_contract.functions.symbol = AsyncMock()
    mock_contract.functions.symbol.call = AsyncMock(return_value="TST")
    
    # Mock contract creation
    mock.eth.contract = MagicMock(return_value=mock_contract)
    
    # Mock transaction sending
    mock.eth.send_raw_transaction = AsyncMock(return_value=TEST_TRANSACTION_HASH)
    mock.eth.wait_for_transaction_receipt = AsyncMock(return_value={
        'transactionHash': TEST_TRANSACTION_HASH,
        'blockHash': '0x...',
        'blockNumber': TEST_BLOCK_NUMBER,
        'contractAddress': TEST_CONTRACT_ADDRESS,
        'cumulativeGasUsed': 1000000,
        'effectiveGasPrice': Web3.to_wei(50, 'gwei'),
        'gasUsed': 1000000,
        'logs': [],
        'logsBloom': '0x...',
        'status': 1,  # Success
        'transactionIndex': 0
    })
    
    # Mock address conversion
    mock.to_checksum_address = MagicMock(lambda addr: addr)
    
    return mock

@pytest_asyncio.fixture
async def mock_redis():
    return MockRedis()

@pytest_asyncio.fixture
async def basic_subscription_plan(async_db):
    """Create a basic subscription plan for testing"""
    plan = SubscriptionPlan(
        tier=SubscriptionTier.BASIC,
        name="Basic Plan",
        description="Basic subscription plan for testing",
        price_monthly=999,
        price_yearly=9999,
        api_rate_limit=100,
        daily_request_limit=1000,
        monthly_request_limit=30000,
        allowed_endpoints=["*"],
        max_payload_size=1024 * 1024,  # 1MB
        has_priority_support=False,
        has_advanced_features=False,
        concurrent_requests=5,
        max_response_time=500
    )
    async_db.add(plan)
    await async_db.commit()
    await async_db.refresh(plan)
    return plan

@pytest_asyncio.fixture
async def professional_subscription_plan(async_db):
    """Create a professional subscription plan for testing"""
    plan = SubscriptionPlan(
        tier=SubscriptionTier.PROFESSIONAL,
        name="Professional Plan",
        description="Professional subscription plan for testing",
        price_monthly=2999,
        price_yearly=29999,
        api_rate_limit=500,
        daily_request_limit=5000,
        monthly_request_limit=150000,
        allowed_endpoints=["*"],
        max_payload_size=5 * 1024 * 1024,  # 5MB
        has_priority_support=True,
        has_advanced_features=True,
        concurrent_requests=20,
        max_response_time=200
    )
    async_db.add(plan)
    await async_db.commit()
    await async_db.refresh(plan)
    return plan

@pytest_asyncio.fixture
async def user_with_basic_subscription(async_db, test_verified_user, basic_subscription_plan):
    """Create a user with a basic subscription"""
    subscription = user_subscription(
        user_id=test_verified_user["id"],
        subscription_plan_id=basic_subscription_plan.id,
        start_date=datetime.now(timezone.utc),
        end_date=datetime.now(timezone.utc) + timedelta(days=30),
        status="active"
    )
    async_db.add(subscription)
    await async_db.commit()
    await async_db.refresh(subscription)
    
    # Return the user with subscription info
    return {
        **test_verified_user,
        "subscription_id": subscription.id,
        "subscription_plan_id": basic_subscription_plan.id,
        "subscription_tier": basic_subscription_plan.tier
    }

@pytest_asyncio.fixture
async def user_with_professional_subscription(async_db, test_verified_user, professional_subscription_plan):
    """Create a user with a professional subscription"""
    subscription = user_subscription(
        user_id=test_verified_user["id"],
        subscription_plan_id=professional_subscription_plan.id,
        start_date=datetime.now(timezone.utc),
        end_date=datetime.now(timezone.utc) + timedelta(days=30),
        status="active"
    )
    async_db.add(subscription)
    await async_db.commit()
    await async_db.refresh(subscription)
    
    # Return the user with subscription info
    return {
        **test_verified_user,
        "subscription_id": subscription.id,
        "subscription_plan_id": professional_subscription_plan.id,
        "subscription_tier": professional_subscription_plan.tier
    }

@pytest_asyncio.fixture
async def user_with_expired_subscription(async_db, test_verified_user, basic_subscription_plan):
    """Create a user with an expired subscription"""
    subscription = user_subscription(
        user_id=test_verified_user["id"],
        subscription_plan_id=basic_subscription_plan.id,
        start_date=datetime.now(timezone.utc) - timedelta(days=60),
        end_date=datetime.now(timezone.utc) - timedelta(days=30),
        status="expired"
    )
    async_db.add(subscription)
    await async_db.commit()
    await async_db.refresh(subscription)
    
    # Return the user with subscription info
    return {
        **test_verified_user,
        "subscription_id": subscription.id,
        "subscription_plan_id": basic_subscription_plan.id,
        "subscription_tier": basic_subscription_plan.tier,
        "subscription_status": "expired"
    }

@pytest.mark.asyncio
async def test_deploy_erc20_with_basic_subscription(client, user_with_basic_subscription, test_rpc_web3, mock_redis, async_db):
    """Test deploying ERC20 contract with a basic subscription using test RPC"""
    # Create a test file for upload
    test_file_content = b"test logo content"
    test_file = io.BytesIO(test_file_content)
    
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services.load_contract_artifacts') as mock_load_artifacts, \
         patch('api.v1.deploy_contracts.services.DeployErc20._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services.upload_image') as mock_upload_image, \
         patch('api.v1.deploy_contracts.services.ContractDeployer.constructor') as mock_constructor:
        
        mock_load_artifacts.return_value = (SAMPLE_ABI, SAMPLE_BYTECODE)
        mock_init_wallet.return_value = None
        mock_upload_image.return_value = ("test_logo.png", "/path/to/test_logo.png")
        
        # Mock the constructor method to return a signature ID
        mock_constructor.return_value = {
            "signature_id": 1,
            "message": "Contract deployment prepared. Please sign the transaction."
        }
        
        response = await client.post(
            "/erc20/deploy",
            data={
                "name": "Test Token",
                "symbol": "TST",
                "description": "Test token for unit tests",
                "default_wallet": "true"
            },
            files={"file": ("test_logo.png", test_file, "image/png")},
            headers={"Authorization": f"Bearer {user_with_basic_subscription['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data
        
        # Verify a ToBeSigned record was created
        result = await async_db.execute(
            select(ToBeSigned).where(
                ToBeSigned.user_id == user_with_basic_subscription["id"],
                ToBeSigned.transaction_type == TransactionType.ER20
            )
        )
        signature = result.scalar_one_or_none()
        assert signature is not None
        assert signature.transaction_type == TransactionType.ER20
        assert signature.transaction_data["name"] == "Test Token"
        assert signature.transaction_data["symbol"] == "TST"

@pytest.mark.asyncio
async def test_deploy_erc721_with_professional_subscription(client, user_with_professional_subscription, test_rpc_web3, mock_redis, async_db):
    """Test deploying ERC721 contract with a professional subscription using test RPC"""
    # Create a test file for upload
    test_file_content = b"test logo content"
    test_file = io.BytesIO(test_file_content)
    
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services.load_contract_artifacts') as mock_load_artifacts, \
         patch('api.v1.deploy_contracts.services.DeployErc20._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services.upload_image') as mock_upload_image, \
         patch('api.v1.deploy_contracts.services.ContractDeployer.constructor') as mock_constructor:
        
        mock_load_artifacts.return_value = (SAMPLE_ABI, SAMPLE_BYTECODE)
        mock_init_wallet.return_value = None
        mock_upload_image.return_value = ("test_logo.png", "/path/to/test_logo.png")
        
        # Mock the constructor method to return a signature ID
        mock_constructor.return_value = {
            "signature_id": 2,
            "message": "Contract deployment prepared. Please sign the transaction."
        }
        
        response = await client.post(
            "/erc721/deploy",
            data={
                "name": "Test NFT",
                "symbol": "TNFT",
                "description": "Test NFT collection for unit tests",
                "default_wallet": "true"
            },
            files={"file": ("test_logo.png", test_file, "image/png")},
            headers={"Authorization": f"Bearer {user_with_professional_subscription['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data
        
        # Verify a ToBeSigned record was created
        result = await async_db.execute(
            select(ToBeSigned).where(
                ToBeSigned.user_id == user_with_professional_subscription["id"],
                ToBeSigned.transaction_type == TransactionType.ERC721
            )
        )
        signature = result.scalar_one_or_none()
        assert signature is not None
        assert signature.transaction_type == TransactionType.ERC721
        assert signature.transaction_data["name"] == "Test NFT"
        assert signature.transaction_data["symbol"] == "TNFT"

@pytest.mark.asyncio
async def test_sign_and_deploy_erc20_transaction_with_subscription(client, user_with_basic_subscription, test_rpc_web3, mock_redis, async_db):
    """Test signing and deploying an ERC20 transaction with a subscription using test RPC"""
    # Create a test signature record
    signature = ToBeSigned(
        user_id=user_with_basic_subscription["id"],
        transaction_type=TransactionType.ER20,
        transaction_data={
            "name": "Test Token",
            "symbol": "TST",
            "description": "Test token for unit tests"
        },
        total_cost_wei=Web3.to_wei(0.01, 'ether'),
        total_cost_eth=0.01,
        status="pending",
        created_at=datetime.now(timezone.utc)
    )
    async_db.add(signature)
    await async_db.commit()
    await async_db.refresh(signature)
    
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services.load_contract_artifacts') as mock_load_artifacts, \
         patch('api.v1.deploy_contracts.services.DeployErc20._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services.ContractDeployer.sign_and_send_transaction') as mock_sign_and_send, \
         patch('api.v1.deploy_contracts.router.handle_deploy_notification') as mock_notification:
        
        mock_load_artifacts.return_value = (SAMPLE_ABI, SAMPLE_BYTECODE)
        mock_init_wallet.return_value = None
        
        # Mock successful transaction deployment
        mock_sign_and_send.return_value = {
            "contract_address": TEST_CONTRACT_ADDRESS,
            "transaction_hash": TEST_TRANSACTION_HASH,
            "block_number": TEST_BLOCK_NUMBER,
            "status": "success",
            "message": "Contract deployed successfully"
        }
        
        mock_notification.return_value = None
        
        response = await client.post(
            f"/sign/deploy/{signature.id}",
            params={"contract": "ER20"},
            headers={"Authorization": f"Bearer {user_with_basic_subscription['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "contract_address" in data
        assert data["contract_address"] == TEST_CONTRACT_ADDRESS
        assert "transaction_hash" in data
        assert data["transaction_hash"] == TEST_TRANSACTION_HASH
        assert "status" in data
        assert data["status"] == "success"
        
        # Verify a ContractDeployment record was created
        result = await async_db.execute(
            select(ContractDeployment).where(
                ContractDeployment.user_id == user_with_basic_subscription["id"],
                ContractDeployment.contract_address == TEST_CONTRACT_ADDRESS
            )
        )
        contract = result.scalar_one_or_none()
        assert contract is not None
        assert contract.contract_type == TransactionType.ER20
        assert contract.block_number == TEST_BLOCK_NUMBER

@pytest.mark.asyncio
async def test_get_all_contracts_with_subscription(client, user_with_professional_subscription, test_rpc_web3, mock_redis, async_db):
    """Test getting all contracts with a subscription using test RPC"""
    # Create test contract deployments
    erc20_contract = ContractDeployment(
        user_id=user_with_professional_subscription["id"],
        contract_address="******************************************",
        contract_type=TransactionType.ER20,
        block_number=TEST_BLOCK_NUMBER,
        name="Test ERC20",
        symbol="TST20",
        created_at=datetime.now(timezone.utc)
    )
    
    erc721_contract = ContractDeployment(
        user_id=user_with_professional_subscription["id"],
        contract_address="******************************************",
        contract_type=TransactionType.ERC721,
        block_number=TEST_BLOCK_NUMBER,
        name="Test ERC721",
        symbol="TST721",
        created_at=datetime.now(timezone.utc)
    )
    
    async_db.add(erc20_contract)
    async_db.add(erc721_contract)
    await async_db.commit()
    
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services.load_contract_artifacts') as mock_load_artifacts, \
         patch('api.v1.deploy_contracts.services.DeployErc20._initialize_wallet') as mock_init_wallet:
        
        mock_load_artifacts.return_value = (SAMPLE_ABI, SAMPLE_BYTECODE)
        mock_init_wallet.return_value = None
        
        response = await client.get(
            "/users/get_user_contracts",
            headers={"Authorization": f"Bearer {user_with_professional_subscription['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "contracts" in data
        assert len(data["contracts"]) >= 2
        
        # Check that both our test contracts are in the results
        contract_addresses = [c["contract_address"] for c in data["contracts"]]
        assert "******************************************" in contract_addresses
        assert "******************************************" in contract_addresses

@pytest.mark.asyncio
async def test_deploy_with_expired_subscription(client, user_with_expired_subscription, test_rpc_web3, mock_redis, async_db):
    """Test deploying a contract with an expired subscription using test RPC"""
    # Create a test file for upload
    test_file_content = b"test logo content"
    test_file = io.BytesIO(test_file_content)
    
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services.load_contract_artifacts') as mock_load_artifacts, \
         patch('api.v1.deploy_contracts.services.DeployErc20._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services.upload_image') as mock_upload_image, \
         patch('api.v1.deploy_contracts.services.ContractDeployer.constructor') as mock_constructor:
        
        mock_load_artifacts.return_value = (SAMPLE_ABI, SAMPLE_BYTECODE)
        mock_init_wallet.return_value = None
        mock_upload_image.return_value = ("test_logo.png", "/path/to/test_logo.png")
        
        # Mock the constructor method to return a signature ID
        mock_constructor.return_value = {
            "signature_id": 3,
            "message": "Contract deployment prepared. Please sign the transaction."
        }
        
        # The test should still pass because we're not enforcing subscription status in the current implementation
        # In a real implementation, this would likely return a 403 Forbidden or similar
        response = await client.post(
            "/erc20/deploy",
            data={
                "name": "Test Token",
                "symbol": "TST",
                "description": "Test token for unit tests",
                "default_wallet": "true"
            },
            files={"file": ("test_logo.png", test_file, "image/png")},
            headers={"Authorization": f"Bearer {user_with_expired_subscription['access_token']}"}
        )
        
        # Since the current implementation doesn't check subscription status, this should still succeed
        # In a real implementation with subscription enforcement, you would assert for a 403 or similar
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data
