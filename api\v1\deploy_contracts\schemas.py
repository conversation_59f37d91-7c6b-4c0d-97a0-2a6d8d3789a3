from pydantic import BaseModel
from api.v1.user.models import User
from pydantic import BaseModel, Field, ValidationError, validator
from typing import List, Optional
import re
from hexbytes import HexBytes

from datetime import datetime
from typing import Optional
from pydantic import BaseModel

class DeployedContractResponse(BaseModel):
    id: int
    name: str
    symbol: Optional[str] = None
    contract_type: Optional[str] = None
    contract_address: str
    created_at: datetime
    

    class Config:
        from_attributes = True
        populate_by_name = True


class Log(BaseModel):
    blockHash: str
    address: str
    logIndex: int
    data: str
    removed: bool
    topics: List[str]
    blockNumber: int
    transactionIndex: int
    transactionHash: str


class TransactionReceipt(BaseModel):
    transactionHash: str
    blockHash: str
    blockNumber: int
    logsBloom: str
    gasUsed: int
    contractAddress: Optional[str] = None
    cumulativeGasUsed: int
    transactionIndex: int
    from_: str = Field(alias='from')
    to: Optional[str] = None
    type: int
    effectiveGasPrice: int
    logs: List[Log]
    status: int

    class Config:
        populate_by_name = True  # Allows using 'from_' with alias 'from'







def transform_tx_receipt(tx_receipt):
    def hexbytes_to_str(value):
        if isinstance(value, HexBytes):
            return value.hex()
        return value

    # Transform the main receipt fields
    transformed = {
        'transactionHash': hexbytes_to_str(tx_receipt.get('transactionHash')),
        'blockHash': hexbytes_to_str(tx_receipt.get('blockHash')),
        'blockNumber': tx_receipt.get('blockNumber'),
        'logsBloom': hexbytes_to_str(tx_receipt.get('logsBloom')),
        'gasUsed': tx_receipt.get('gasUsed'),
        'contractAddress': tx_receipt.get('contractAddress'),
        'cumulativeGasUsed': tx_receipt.get('cumulativeGasUsed'),
        'transactionIndex': tx_receipt.get('transactionIndex'),
        'from': hexbytes_to_str(tx_receipt.get('from')),
        'to': hexbytes_to_str(tx_receipt.get('to')) if tx_receipt.get('to') else None,
        'type': tx_receipt.get('type'),
        'effectiveGasPrice': tx_receipt.get('effectiveGasPrice'),
        'logs': [],
        'status': tx_receipt.get('status')
    }

    # Transform each log entry
    for log in tx_receipt.get('logs', []):
        transformed_log = {
            'blockHash': hexbytes_to_str(log.get('blockHash')),
            'address': log.get('address'),
            'logIndex': log.get('logIndex'),
            'data': hexbytes_to_str(log.get('data')),
            'removed': log.get('removed'),
            'topics': [hexbytes_to_str(topic) for topic in log.get('topics', [])],
            'blockNumber': log.get('blockNumber'),
            'transactionIndex': log.get('transactionIndex'),
            'transactionHash': hexbytes_to_str(log.get('transactionHash')),
        }
        transformed['logs'].append(transformed_log)
    
    return transformed




















# Pydantic models for response
from pydantic import BaseModel
from datetime import datetime
from enum import Enum
from typing import Optional

class ContractStatus(str, Enum):
    DEPLOYED = "deployed"
    FAILED = "failed"

class ContractType(str, Enum):
    ERC20 = "erc20"
    ERC721 = "erc721"

class ContractResponse(BaseModel):
    address: str
    type: ContractType
    status: ContractStatus

    name: str
    symbol: Optional[str] = None
    description: Optional[str] = None
    image: Optional[str] = None
    


class TransactionResponse(BaseModel):
    hash: str
    from_: str
    to: Optional[str]
    status: int
    transactionIndex: int
    gasUsed: int
    effectiveGasPrice: int
    cumulativeGasUsed: int
    type: int

class BlockResponse(BaseModel):
    hash: str
    number: int

class ContractDeploymentResponse(BaseModel):
    id: int
    createdAt: datetime
    contract: ContractResponse
    transaction: TransactionResponse
    block: BlockResponse

    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }



class NameandSymbol(BaseModel):
    name: str
    symbol: str

    class Config:
        from_attributes = True
