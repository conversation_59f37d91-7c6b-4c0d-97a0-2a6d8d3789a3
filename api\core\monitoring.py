from typing import Dict, Any, Optional
import time
import psutil
import redis
from datetime import datetime
from api.core.logging_config import get_logger
from config import config

logger = get_logger(__name__)

class MonitoringSystem:
    """System for monitoring application performance and security events"""
    
    def __init__(self):
        self.redis_client = redis.Redis.from_url(config.REDIS_URL)
        self.metrics_prefix = "monitoring:"
        self.security_prefix = "security:"
        self.performance_prefix = "performance:"
        
    async def track_security_event(self, event_type: str, details: Dict[str, Any]) -> None:
        """Track security-related events"""
        try:
            event_data = {
                "timestamp": datetime.now().isoformat(),
                "event_type": event_type,
                "details": details
            }
            key = f"{self.security_prefix}{event_type}:{int(time.time())}"
            await self.redis_client.setex(key, 86400, str(event_data))  # Store for 24 hours
            logger.warning(f"Security event recorded: {event_type}")
        except Exception as e:
            logger.error(f"Failed to record security event: {str(e)}")
    
    async def track_performance_metric(self, metric_name: str, value: float) -> None:
        """Track performance metrics"""
        try:
            metric_data = {
                "timestamp": datetime.now().isoformat(),
                "value": value
            }
            key = f"{self.performance_prefix}{metric_name}:{int(time.time())}"
            await self.redis_client.setex(key, 3600, str(metric_data))  # Store for 1 hour
        except Exception as e:
            logger.error(f"Failed to record performance metric: {str(e)}")
    
    async def get_system_metrics(self) -> Dict[str, Any]:
        """Get current system metrics"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            metrics = {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.track_performance_metric("system_metrics", metrics)
            return metrics
        except Exception as e:
            logger.error(f"Failed to get system metrics: {str(e)}")
            return {}
    
    async def track_api_request(self, endpoint: str, method: str, status_code: int, 
                              response_time: float, user_id: Optional[str] = None) -> None:
        """Track API request metrics"""
        try:
            request_data = {
                "timestamp": datetime.now().isoformat(),
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response_time": response_time,
                "user_id": user_id
            }
            
            key = f"{self.metrics_prefix}api_requests:{int(time.time())}"
            await self.redis_client.setex(key, 3600, str(request_data))  # Store for 1 hour
            
            # Track suspicious patterns
            if status_code >= 400 or response_time > 5.0:  # 5 seconds threshold
                await self.track_security_event("suspicious_request", request_data)
                
        except Exception as e:
            logger.error(f"Failed to track API request: {str(e)}")
    
    async def track_contract_interaction(self, contract_address: str, function_name: str,
                                       status: str, gas_used: Optional[int] = None) -> None:
        """Track smart contract interactions"""
        try:
            interaction_data = {
                "timestamp": datetime.now().isoformat(),
                "contract_address": contract_address,
                "function_name": function_name,
                "status": status,
                "gas_used": gas_used
            }
            
            key = f"{self.metrics_prefix}contract_interactions:{int(time.time())}"
            await self.redis_client.setex(key, 3600, str(interaction_data))
            
            if status == "failed":
                await self.track_security_event("contract_interaction_failed", interaction_data)
                
        except Exception as e:
            logger.error(f"Failed to track contract interaction: {str(e)}")
    
    async def check_suspicious_activity(self, ip_address: str) -> bool:
        """Check for suspicious activity from an IP address"""
        try:
            # Check request frequency
            key = f"{self.security_prefix}ip_requests:{ip_address}"
            request_count = await self.redis_client.get(key)
            
            if request_count and int(request_count) > 100:  # More than 100 requests per minute
                await self.track_security_event("high_frequency_requests", {
                    "ip_address": ip_address,
                    "request_count": int(request_count)
                })
                return True
                
            return False
        except Exception as e:
            logger.error(f"Failed to check suspicious activity: {str(e)}")
            return False
    
    async def get_performance_report(self, time_range: int = 3600) -> Dict[str, Any]:
        """Generate performance report for the specified time range"""
        try:
            current_time = int(time.time())
            start_time = current_time - time_range
            
            # Get API request metrics
            api_requests = []
            for key in await self.redis_client.keys(f"{self.metrics_prefix}api_requests:*"):
                if int(key.split(":")[-1]) >= start_time:
                    data = await self.redis_client.get(key)
                    if data:
                        api_requests.append(eval(data))
            
            # Get system metrics
            system_metrics = []
            for key in await self.redis_client.keys(f"{self.performance_prefix}system_metrics:*"):
                if int(key.split(":")[-1]) >= start_time:
                    data = await self.redis_client.get(key)
                    if data:
                        system_metrics.append(eval(data))
            
            return {
                "api_requests": api_requests,
                "system_metrics": system_metrics,
                "time_range": time_range
            }
        except Exception as e:
            logger.error(f"Failed to generate performance report: {str(e)}")
            return {} 