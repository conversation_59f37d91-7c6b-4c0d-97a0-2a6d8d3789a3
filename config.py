from typing import Optional
import logging
import os


class Settings:

    SUPER_ADMIN_EMAILS = ["<EMAIL>", "<EMAIL>"]

    # Environment settings
    PYTHON_ENV: str = os.getenv("PYTHON_ENV", "dev")
    
    # Logging configuration
    LOG_LEVEL: str = "INFO" #os.getenv("LOG_LEVEL", "INFO" if PYTHON_ENV == "production" else "DEBUG")
    LOG_TO_FILE: bool = PYTHON_ENV == "production"
    LOG_TO_CONSOLE: bool = True
    
    DB_USER: str = "ozura_admin"
    DB_HOST: str = "ledgerdb-uat-postgres.postgres.database.azure.com"
    DB_NAME: str = "atlas_api"
    DB_PASSWORD: str = "mv2kXDDtwPLx24K"
    DB_PORT: int = 5432
    DB_SSL: bool = True
    MYSQL_DRIVER: str = "pymysql"
    DB_TYPE: str = "postgresql"


    # Database Pool Configuration
    DB_POOL_SIZE: int = int(os.getenv("DB_POOL_SIZE", "10"))
    DB_MAX_OVERFLOW: int = int(os.getenv("DB_MAX_OVERFLOW", "10"))
    DB_POOL_TIMEOUT: int = int(os.getenv("DB_POOL_TIMEOUT", "30"))
    DB_POOL_RECYCLE: int = int(os.getenv("DB_POOL_RECYCLE", "3600"))
    

    REPLICA_DB_USER: str = "ozura_admin"
    REPLICA_DB_HOST: str = "dpg-cvjjmfemcj7s73en2ehg-a.oregon-postgres.render.com"
    REPLICA_DB_NAME: str = "new_test_db"
    REPLICA_DB_PASSWORD: str = "qVA0Sy79J4lgqeTTFSariFjwMOZYXqmR"
    REPLICA_DB_PORT: str = 5432
    REPLICA_DB_SSL: bool = True
    MYSQL_DRIVER: str = "pymysql"
    REPLICA_DB_TYPE: str = "postgres"
    DB_SSL_CA: Optional[str] = os.getenv("DB_SSL_CA")

    USING_REPLICA: bool = False
    BACKUP_DIR: str = os.getenv("BACKUP_DIR", "backups")
    BACKUP_RETENTION_DAYS: int = int(os.getenv("BACKUP_RETENTION_DAYS", "30"))


    # Database Connection Retry Settings
    DB_MAX_RETRIES: int = int(os.getenv("DB_MAX_RETRIES", "3"))
    DB_RETRY_DELAY: int = int(os.getenv("DB_RETRY_DELAY", "5"))

    # Azure Storage Configuration
    AZURE_STORAGE_CONNECTION_STRING: str = os.getenv("AZURE_STORAGE_CONNECTION_STRING")
    AZURE_BACKUP_CONTAINER: str = os.getenv("AZURE_BACKUP_CONTAINER", "database-backups")
    BACKUP_TEMP_DIR: str = "/tmp/backups"
    BACKUP_ENCRYPTION_KEY: str = os.getenv("BACKUP_ENCRYPTION_KEY")


    @property
    def DATABASE_URL(self) -> str:
        """Construct Database URL with proper SSL settings"""
        if self.DB_TYPE == "postgresql":
            ssl_params = "?sslmode=verify-full" if self.DB_SSL and self.PYTHON_ENV == "production" else "?sslmode=prefer"
            if self.DB_SSL_CA:
                ssl_params += f"&sslcert={self.DB_SSL_CA}"
            return f"postgresql+asyncpg://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        return f"sqlite:///./database.db"

    @property
    def REPLICA_DATABASE_URL(self) -> str:
        """Construct Replica Database URL with SSL settings"""
        if not all([self.REPLICA_DB_HOST, self.REPLICA_DB_USER, self.REPLICA_DB_PASSWORD]):
            return None
        ssl_params = "?sslmode=verify-full" if self.REPLICA_DB_SSL and self.PYTHON_ENV == "production" else "?sslmode=prefer"
        return f"postgresql+asyncpg://{self.REPLICA_DB_USER}:{self.REPLICA_DB_PASSWORD}@{self.REPLICA_DB_HOST}:{self.REPLICA_DB_PORT}/{self.REPLICA_DB_NAME}"


    # Authentication Configuration
    JWT_SECRET: str = "4J8f9s8d7f6g5h4j3k2l1m0n9b8v7c6x5z4a3s2d1f0g9h8j7k6l5m4n3b2v1c0"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 480
    JWT_REFRESH_EXPIRY: int = 5

    # Google OAuth Configuration
    GOOGLE_CLIENT_ID: str = "935539350702-pt0rfvrvjri7brm3p1p96kbssskfootm.apps.googleusercontent.com"
    GOOGLE_CLIENT_SECRET: str = "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"
    #GOOGLE_REDIRECT_URI: str = "https://api.server.stellus.com/auth/callback/google"
    TOKEN_URL: str = "https://oauth2.googleapis.com/token"
    #GOOGLE_REDIRECT_URI: str = "http://127.0.0.1:8000/dashboard"
    GOOGLE_REDIRECT_URI: str = "http://localhost:3000/dashboard"
    
    #GOOGLE_REDIRECT_URI: str = "http://127.0.0.1:5500/dashboard.html"
    #GOOGLE_REDIRECT_URI: str = "https://efb8-197-211-59-180.ngrok-free.app/dashboard.html"
    
    #GOOGLE_REDIRECT_URI: str = "https://api.server.stellus.com

    BASE_URL = "https://api.server.stellus.com" 
    #BASE_URL = "http://127.0.0.1:8000" 

    # Frontend and App URLs
    FRONTEND_URL: Optional[str] = None
    APP_URL: Optional[str] = None

    # API Keys and Payment Configurations
    RESEND_API_KEY: Optional[str] = None

    # Paystack Configuration
    PAYSTACK_SECRET_KEY: str = "sk_test_b7f0855104fb294a3501ed975ac5cba4cd5bc65b"
    PAYSTACK_PUBLIC_KEY: str = "pk_test_a4977dcb9ee2e524a8a58b93c3ae426d34b94379"
    PAYSTACK_CALLBACK_URL: str = "http://127.0.0.1:8000/subscriptions/verify-payment/"

    # Stripe Configuration
    STRIPE_SECRET_KEY: str = "sk_test_51QSbyxHIghVppUgCLmKlBpq5TFnbZAe3bTZRLReiBS23HLJ1n0HJxj2FcvDynNLOcbgw69ts9sr3KHResIKVDDtR004gPN1Xd1"
    STRIPE_WEBHOOK_SECRET: str = "your_webhook_secret"
    STRIPE_SUCCESS_URL: str = "your_success_url"
    STRIPE_CANCEL_URL: str = "your_cancel_url"

    # Monnify Configuration
    MONNIFY_API_KEY: str = "your_monnify_api_key"
    MONNIFY_SECRET_KEY: str = "your_monnify_secret_key"
    MONNIFY_BASE_URL: str = "https://api.monnify.com"
    MONNIFY_CONTRACT_CODE: str = "your_contract_code"
    MONNIFY_REDIRECT_URL: str = "your_redirect_url"



    #ERC deployment configurations
    PRODUCTION: bool = False

    ALCHEMY_API_KEY = "************************************"
    PROVIDER: str = f"https://polygon-amoy.g.alchemy.com/v2/{ALCHEMY_API_KEY}"

    #PROVIDER: str = f"https://polished-distinguished-arm.matic-amoy.quiknode.pro/91386aae2ed884f3953aafd00d41b5a3c97351e3/"


    PLATFORM_WALLET = "******************************************"
    PLATFORM_PRIVATE_KEY = "0x" + "c9771e1a1eef172c3fd85ac4510b937bb768def9af60a092472b9c3e671598f6"

    #PLATFORM_WALLET = "******************************************"
    #PLATFORM_PRIVATE_KEY = "0x" + "682d70b616cfce1b158560d862b1a94e9ef0c4d6573f88fb7309d2d6520cc685"

    PLATFORM_FEE_PERCENTAGE = 10

    PLATFORM_FLAT_FEE = 0.000002


    #PINATA SETUP!
    PINATA_API_KEY = "c3bf3724a733b5d2ec86"
    PINATA_SECRET_KEY = "****************************************************************"


    #EMAIL SENDING 
    EMAIL_SENDER = "<EMAIL>"
    EMAIL_PASSWORD = "kiui rdtp vqau aktu"
    SMTP_SERVER = "smtp.gmail.com"
    SMTP_PORT = 587
    VERIFICATION_TOKEN_LENGTH = 32
    VERIFICATION_TOKEN_EXPIRY_HOURS = 24

    """
    REDIS_HOST = "srv-captain--new-redis"
    REDIS_PORT = 6379
    REDIS_PASSWORD = "atlas-redis"
    REDIS_URL = "redis://:<EMAIL>:6379/0"

    @property
    def REDIS_URL(self):
        return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/0"

    """

    REDIS_HOST = "redis"
    REDIS_PORT = 6379
    REDIS_PASSWORD: Optional[str] = None
    
    REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}"
    #"""
        

    BACKUP_DIR = "backups"
    BACKUP_RETENTION_DAYS = 30

config = Settings()


# Optional: Helper function for getting config
def get_config():
    return config


#FOR PRODUCITON:

"""
# config.py
from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict
import os


class Settings(BaseSettings):
    # Database Configuration
    DB_USER: str
    DB_HOST: str
    DB_NAME: str
    DB_PASSWORD: str
    DB_PORT: int = 5432
    DB_SSL: bool = True
    MYSQL_DRIVER: str = "pymysql"
    DB_TYPE: str = "postgresql"

    # Database URL (constructed or direct)
    DATABASE_URL: str

    # Authentication Configuration
    JWT_SECRET: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 480
    JWT_REFRESH_EXPIRY: int = 5
    PYTHON_ENV: str = "dev"

    # Google OAuth Configuration
    GOOGLE_CLIENT_ID: str
    GOOGLE_CLIENT_SECRET: str
    TOKEN_URL: str = "https://oauth2.googleapis.com/token"
    GOOGLE_REDIRECT_URI: str

    # Base URL
    BASE_URL: str

    # Frontend and App URLs
    FRONTEND_URL: Optional[str] = None
    APP_URL: Optional[str] = None

    # API Keys and Payment Configurations
    RESEND_API_KEY: Optional[str] = None

    # Paystack Configuration
    PAYSTACK_SECRET_KEY: str
    PAYSTACK_PUBLIC_KEY: str
    PAYSTACK_CALLBACK_URL: str

    # Stripe Configuration
    STRIPE_SECRET_KEY: str
    STRIPE_WEBHOOK_SECRET: str
    STRIPE_SUCCESS_URL: str
    STRIPE_CANCEL_URL: str

    # Monnify Configuration
    MONNIFY_API_KEY: str
    MONNIFY_SECRET_KEY: str
    MONNIFY_BASE_URL: str
    MONNIFY_CONTRACT_CODE: str
    MONNIFY_REDIRECT_URL: str

    # ERC deployment configurations
    PRODUCTION: bool = False
    ALCHEMY_API_KEY: str
    PROVIDER: str
    PLATFORM_WALLET: str
    PLATFORM_PRIVATE_KEY: str
    PLATFORM_FEE_PERCENTAGE: int = 10
    PLATFORM_FLAT_FEE: float = 0.000002

    # PINATA SETUP!
    PINATA_API_KEY: str
    PINATA_SECRET_KEY: str

    # Redis Configuration
    REDIS_HOST: str
    REDIS_PORT: int = 6379
    REDIS_PASSWORD: str
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    LOCAL_REDIS_PASSWORD: Optional[str] = None

    model_config = SettingsConfigDict(env_file=".env", extra="ignore")

    @property
    def REDIS_URL(self):
        return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/0"


# Create a singleton instance
config = Settings()


# Optional: Helper function for getting config
def get_config():
    return config

"""

"""
import smtplib
from email.mime.text import MIMEText

sender_email = "<EMAIL>"
sender_password = "your_app_password"  # Use your App Password!
receiver_email = "<EMAIL>"

message = MIMEText("This is the email body.")
message["Subject"] = "Email Subject"
message["From"] = sender_email
message["To"] = receiver_email

try:
    with smtplib.SMTP("smtp.gmail.com", 587) as server:
        server.starttls()
        server.login(sender_email, sender_password)
        server.sendmail(sender_email, receiver_email, message.as_string())
    print("Email sent successfully!")
except Exception as e:
    print(f"Error sending email: {e}")"
"""