from fastapi import Depends, HTTPException, status, UploadFile, File, Query
from typing import Dict, List, Optional, Any, Tuple
from web3 import Web3
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func
from sqlalchemy.future import select
from api.v1.account.models import Account as AccountWallet
from api.v1.account.services import Vault
from pydantic import ValidationError
from web3 import Web3
import os
import json
import math
from config import config
from api.core.general_dep import upload_image
from .models import ContractDeployment, TransactionType, ContractStatus, TransactionStatus, ToBeSigned, make_json_serializable
from .schemas import DeployedContractResponse
from decimal import Decimal
from datetime import datetime, timedelta, timezone
from api.core.blockchain_dep import class_exponential_backoff, load_contract_artifacts, CONTRACT_CONFIG
from api.v1.contracts.schemas import Order
import pandas as pd
import numpy as np
import time
import json
from pathlib import Path
from .exceptions import (logger, CompilationError, handle_deployment_error, InvalidContractTypeError,
                         ContractNotFoundError, FileUploadError, BlockchainConnectionError, GasEstimationError,
                         DatabaseError, InvalidParameterError, TransactionTimeoutError, InsufficientFundsError,
                         SignatureError, DataProcessingError, CacheError, BuildError)
from redis.asyncio import Redis
from web3 import AsyncWeb3
import asyncio
import aiofiles

# Configuration constants setup_metrics
PLATFORM_WALLET = config.PLATFORM_WALLET
PLATFORM_PRIVATE_KEY = config.PLATFORM_PRIVATE_KEY
PLATFORM_FEE_PERCENTAGE = config.PLATFORM_FEE_PERCENTAGE
PLATFORM_FLAT_FEE = config.PLATFORM_FLAT_FEE
PROVIDER = config.PROVIDER
PRODUCTION = config.PRODUCTION
GAS_BUFFER_PERCENT = 15  # config.GAS_BUFFER_PERCENT
BASE_URL = config.BASE_URL
CACHE_TIME = 600

CONTRACT_LOGO = "./uploads/contract_logo"
#os.makedirs(CONTRACT_LOGO, exist_ok=True)



class FeeCalculator:
    """Helper class to manage fee calculations"""

    def __init__(self):
        self.platform_fee_percentage = PLATFORM_FEE_PERCENTAGE

    def calculate_platform_fee(self, transaction_amount: int) -> dict:
        """Calculate platform fee based on transaction amount"""
        platform_fee = (Decimal(transaction_amount) * Decimal(self.platform_fee_percentage)) / Decimal(100)
        return {
            'fee_percentage': self.platform_fee_percentage,
            'fee_amount': platform_fee
        }



class BlockchainBase:
    """Base class for blockchain interactions"""

    def __init__(self, db: AsyncSession, user_id: int, web3: AsyncWeb3, abi, bytecode,
                 transaction_type: TransactionType = None, redis: Optional[Redis] = None):
        self.db = db
        self.user_id = user_id
        self.redis = redis
        self.w3 = web3
        self.transaction_type = transaction_type
        self.general_cache_time = CACHE_TIME
        self.abi = abi
        self.bytecode = bytecode


    @classmethod
    async def create(cls, db: AsyncSession, user_id: int, web3: AsyncWeb3,
                    transaction_type: Optional[TransactionType] = None, redis: Optional[Redis] = None):
        """Factory method to create and initialize a BlockchainBase instance"""
        try:
            # Initialize ABI and bytecode if transaction type is provided
            if transaction_type is not None:
                abi, bytecode = await load_contract_artifacts(transaction_type)
            else:
                abi, bytecode, transaction_type = None, None, None
            
            instance = cls(db, user_id, web3, abi, bytecode, transaction_type, redis)
            
            # Then initialize wallet on the instance
            await instance._initialize_wallet()
            
            return instance

        except Exception as e:
            logger.error(f"Failed to create BlockchainBase instance: {str(e)}")
            raise        


    async def _initialize_wallet(self) -> None:
        """Initialize wallet based on production setting"""
        if PRODUCTION:
            result = await self.db.execute(
                select(AccountWallet).where(AccountWallet.user_id == self.user_id)
            )
            account = result.scalar_one_or_none()

            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User does not have a vault"
                )

            self.wallet = account.address
            self.private_key = await Vault()._get_decrypted_private_key(self.user_id, self.db)
        else:
            self.wallet = PLATFORM_WALLET
            self.private_key = PLATFORM_PRIVATE_KEY


    async def _estimate_gas_with_buffer(self, construct, constructor_args: Dict) -> int:
        """Estimate gas with a buffer percentage"""
        try:
            gas_estimate = await construct.estimate_gas(constructor_args)
            gas_buffer = 1 + (GAS_BUFFER_PERCENT / 100)  # buffer (e.g., 15%)
            return int(gas_estimate * gas_buffer)
        except Exception as gas_error:
            raise GasEstimationError(f"Failed to estimate gas: {str(gas_error)}")

    async def _check_wallet_balance(self, required_wei: int) -> None:
        """Check if wallet has sufficient balance"""
        try:
            balance = await self.w3.eth.get_balance(self.wallet)
            if balance < required_wei:
                raise InsufficientFundsError(
                    f"Insufficient funds. Needed: {required_wei} wei, Available: {balance} wei"
                )
        except InsufficientFundsError:
            raise
        except Exception as balance_error:
            raise BlockchainConnectionError(f"Failed to check wallet balance: {str(balance_error)}")

    async def _sign_and_send_transaction(self, unsigned_tx: Dict) -> str:
        """Sign and send a transaction"""
        try:
            #signed_tx = self.w3.eth.account.sign_transaction(unsigned_tx, private_key=self.private_key)
            signed_tx = await asyncio.to_thread(
                self.w3.eth.account.sign_transaction,
                unsigned_tx,
                private_key=self.private_key
            )

            tx_hash = await self.w3.eth.send_raw_transaction(signed_tx.raw_transaction)
            return tx_hash
        except Exception as sign_error:
            logger.exception(f"Failed to sign or send transaction: {sign_error}")
            raise SignatureError(f"Failed to sign or send transaction: {str(sign_error)}")

    async def _get_transaction_receipt(self, tx_hash, timeout: int = 120):
        """Get transaction receipt with timeout"""
        try:
            return await self.w3.eth.wait_for_transaction_receipt(tx_hash, timeout=timeout)
        except Exception as timeout_error:
            raise TransactionTimeoutError(f"Transaction timed out: {str(timeout_error)}")



    async def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache with error handling"""
        if not self.redis:
            return None
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Cache data corruption detected: {str(e)}")
            await self.redis.delete(cache_key)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval failed: {str(e)}")
            return None

    async def _cache_data(self, cache_key: str, data: Any, cache_time: int) -> None:
        """Cache data with error handling"""
        if not self.redis:
            return None
        try:
            await self.redis.set(cache_key, json.dumps(data))
            await self.redis.expire(cache_key, cache_time)
        except Exception as e:
            logger.warning(f"Failed to cache data: {str(e)}")




class ContractManager(BlockchainBase):
    """Handles contract querying and management"""

    async def get_contract(self, contract_id: int) -> Dict:
        """Get a specific contract by ID"""

        try:
            cache_key = f"contracts:{self.user_id}:{contract_id}"

            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            result = await self.db.execute(
                select(ContractDeployment).where(
                    ContractDeployment.user_id == self.user_id,
                    ContractDeployment.id == contract_id
                )
            )
            contract = result.scalar_one_or_none()
            if not contract:
                raise ContractNotFoundError("This contract does not exist")

            sendcontract = contract.to_response().model_dump()
            sendcontract['createdAt'] = contract.created_at.isoformat()
            await self._cache_data(cache_key, sendcontract, self.general_cache_time)

            return sendcontract
        except Exception as e:
            raise

    async def get_all_contracts(self, page: int = 1, page_size: int = 10, order: Optional[Order] = "desc") -> Dict:
        return await self._get_contracts_by_type(
            None, page, page_size, order,
            "You have no contract deployed"
        )

    async def get_all_erc20_contracts(self, page: int = 1, page_size: int = 10, order: Optional[Order] = "desc") -> Dict:
        return await self._get_contracts_by_type(
            TransactionType.ER20, page, page_size, order,
            "You have no erc20 contract deployed"
        )

    async def get_all_erc721_contracts(self, page: int = 1, page_size: int = 10, order: Optional[Order] = "desc") -> Dict:
        return await self._get_contracts_by_type(
            TransactionType.ERC721, page, page_size, order,
            "You have no erc721 contract deployed"
        )

    async def _get_contracts_by_type(self, contract_type: Optional[TransactionType],
                              page: int, page_size: int, order: Order,
                              empty_message: str) -> Dict:
        try:
            cache_key = f"contracts:{self.user_id}:{contract_type}:{page}:{page_size}:{order}"

            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            order_by = ContractDeployment.id.desc() if order == "desc" else ContractDeployment.id.asc()

            # Build the query
            query = select(ContractDeployment).where(ContractDeployment.user_id == self.user_id)
            if contract_type is not None:
                query = query.where(ContractDeployment.contract_type == contract_type)

            query = query.order_by(order_by)

            # Count total contracts
            count_query = select(func.count()).select_from(ContractDeployment).where(
                ContractDeployment.user_id == self.user_id
            )
            if contract_type is not None:
                count_query = count_query.where(ContractDeployment.contract_type == contract_type)

            count_result = await self.db.execute(count_query)
            total_contracts = count_result.scalar_one()

            if total_contracts == 0:
                raise ContractNotFoundError(empty_message)

            start = (page - 1) * page_size
            total_pages = (total_contracts + page_size - 1) // page_size

            if page > total_pages:
                raise ContractNotFoundError(f"Page {page} does not exist. Total pages: {total_pages}")

            # Get paginated results
            paginated_query = query.offset(start).limit(page_size)
            result = await self.db.execute(paginated_query)
            contracts = result.scalars().all()


            contract_responses = [DeployedContractResponse.model_validate(contract).model_dump() for contract in contracts]

            for contract_response in contract_responses:
                contract_response['created_at'] = contract_response['created_at'].isoformat() if contract_response['created_at'] else None

            response_data = {
                "contracts": contract_responses,
                "pagination": {
                    "total_contracts": total_contracts,
                    "total_pages": total_pages,
                    "current_page": page,
                    "page_size": page_size,
                    "has_next": page < total_pages,
                    "has_previous": page > 1,
                }
            }
            await self._cache_data(cache_key, response_data, self.general_cache_time)
            return response_data

        except Exception as e:
            raise

    async def get_contract_code(self, contract_type: TransactionType) -> str:
        """Get the contract source code"""
        try:
            if contract_type not in CONTRACT_CONFIG:
                raise InvalidContractTypeError("Invalid contract type")
            
            config_data = CONTRACT_CONFIG[contract_type]
            contract_path = config_data.get("contract_path")

            if not contract_path:
                 raise FileNotFoundError(f"Contract path not configured correctly for type {contract_type}")
            
            async with aiofiles.open(contract_path, mode='r', encoding='utf-8') as file:
                contract_code = await file.read()

            if not contract_code.strip():
                raise ValidationError(f"Contract source file is empty")
            """
            try:
                with open(contract_path, 'r') as file:
                    contract_code = file.read()

            except json.JSONDecodeError:
                raise ValidationError(f"Invalid JSON in file: {contract_path}")
            """
            return contract_code
        except FileNotFoundError:
            logger.error(f"Contract source file not found at path: {contract_path}")
            raise ContractNotFoundError(f"Contract file not found: {contract_path}")
        except Exception as e:
            logger.exception(f"Failed to get contract code for type {contract_type}: {e}")
            raise



class ContractDeployer(BlockchainBase):
    """Handles contract deployment"""
    
    def __init__(self, db: AsyncSession, user_id: int, web3: AsyncWeb3, abi, bytecode,
                 transaction_type: TransactionType, redis: Optional[Redis] = None):
        
        super().__init__(db, user_id, 
                         web3, abi, bytecode, 
                         transaction_type=transaction_type, 
                         redis=redis
                         )
        self.name = None
        self.symbol = None
        self.description = None
        self.file_path = None
        self.contract_instance = None

    async def deploy(self, name: str, symbol: str, description: Optional[str] = None, 
                     file: Optional[UploadFile] = File(None), default_wallet: Optional[bool] = True) -> Dict:
        """Deploy a new contract"""
        try:
            self.name = name
            self.symbol = symbol if symbol is not None else None
            self.description = description if description is not None else None

            if file:
                try:
                    unique_filename, full_file_path = await upload_image(file, CONTRACT_LOGO)
                    self.file_path = f"{BASE_URL}/contract_logo/{unique_filename}"
                except Exception as upload_error:
                    raise FileUploadError(f"Failed to upload contract logo: {str(upload_error)}")

            self.contract_instance = self.w3.eth.contract(abi=self.abi, bytecode=self.bytecode)
            return await self.constructor(default_wallet)
        except Exception as e:
            raise

    async def constructor(self, default_wallet: Optional[bool] = True) -> Dict:
        """Deploy contract with constructor arguments"""
        try:
            platform_fee_eth = Web3.to_wei(PLATFORM_FLAT_FEE, 'ether')

            construct = self.contract_instance.constructor(
                self.name, self.symbol, PLATFORM_WALLET,
                PLATFORM_FEE_PERCENTAGE, platform_fee_eth
            )

            if not default_wallet:
                try:
                    chain_id = await self.w3.eth.chain_id
                    gas_price = hex(await self.w3.eth.gas_price)

                    constructor_args = {
                        'chainId': chain_id,
                        'gasPrice': gas_price,
                    }
                except Exception as chain_error:
                    logger.error(f"Failed to get chain information: {str(chain_error)}")
                    raise BlockchainConnectionError("Failed to get chain information")
            else:
                try:
                    constructor_args = {
                        'chainId': await self.w3.eth.chain_id,
                        'from': self.wallet,
                        'gasPrice': await self.w3.eth.gas_price,
                        'nonce': await self.w3.eth.get_transaction_count(self.wallet),
                    }
                except Exception as chain_error:
                    logger.error(f"Failed to get chain information: {str(chain_error)}")
                    raise BlockchainConnectionError("Failed to get chain information")
            try:                    
                unsigned_tx = await construct.build_transaction(constructor_args)
                buffered_gas = await self._estimate_gas_with_buffer(construct, constructor_args)
                
            except Exception as e:
                raise BuildError(f"Failed to build contract: {str(e)}")

            if not default_wallet:
                total_cost_wei = buffered_gas * int(unsigned_tx['gasPrice'], 16)
            else:
                total_cost_wei = buffered_gas * unsigned_tx['gasPrice']

            total_cost_eth = self.w3.from_wei(total_cost_wei, 'ether')
            transaction_data = json.dumps(unsigned_tx, default=make_json_serializable)

            # Create pending transaction record
            pending_tx = await self._create_pending_transaction(
                transaction_data, total_cost_wei, total_cost_eth
            )

            if not default_wallet:
                unsigned_tx['gas'] = hex(unsigned_tx['gas'])
                tx_details = {
                    "status": "pending",
                    "transaction_data": unsigned_tx,
                    "expires_at": pending_tx.expires_at.isoformat(),
                    "transaction_cost": {
                        "wei": str(total_cost_wei),
                        "eth": float(total_cost_eth)
                    },
                    "signature_id": pending_tx.id
                }
            else:
                tx_details = {
                    "status": "pending",
                    "expires_at": pending_tx.expires_at.isoformat(),
                    "transaction_cost": {
                        "wei": str(total_cost_wei),
                        "eth": float(total_cost_eth)
                    },
                    "signature_id": pending_tx.id
                }

            return tx_details
        except ValidationError as e:
            raise InvalidParameterError(str(e))
        except Exception as e:
            raise

    async def _create_pending_transaction(self, transaction_data: str,
                                         total_cost_wei: int,
                                         total_cost_eth: float) -> ToBeSigned:
        """Create a pending transaction record"""
        try:
            pending_tx = ToBeSigned(
                user_id=self.user_id,
                transaction_data=transaction_data,
                total_cost_wei=total_cost_wei,
                total_cost_eth=total_cost_eth,

                name=self.name,
                symbol=self.symbol,
                description=self.description,
                image=self.file_path,

                expires_at=datetime.now(timezone.utc) + timedelta(minutes=15)
            )

            self.db.add(pending_tx)
            await self.db.commit()
            await self.db.refresh(pending_tx)

            return pending_tx
        except Exception as db_error:
            raise DatabaseError(f"Failed to save transaction: {str(db_error)}")

    async def sign_and_send_transaction(self, transaction_id: int, trx_hash: Optional[str] = None) -> Dict:
        """Sign and send a pending transaction"""
        try:
            stmt = select(ToBeSigned).where(
                ToBeSigned.user_id == self.user_id,
                ToBeSigned.id == transaction_id,
                ToBeSigned.status == 'pending'
            ).with_for_update()
            result = await self.db.execute(stmt)
            pending_tx = result.scalar_one_or_none()

            if not pending_tx:
                raise ContractNotFoundError("Transaction not found or already processed")

            tx_hash = trx_hash

            if tx_hash is None:
                unsigned_tx = pending_tx.transaction_data
                if isinstance(unsigned_tx, str):
                    unsigned_tx = json.loads(unsigned_tx)

                if 'to' in unsigned_tx and unsigned_tx['to'] == '0x':
                    unsigned_tx['to'] = None

                # Check if transaction expired
                if datetime.now(timezone.utc) > pending_tx.expires_at.replace(tzinfo=timezone.utc):

                    pending_tx.status = 'expired'
                    await self.db.commit()
                    raise TransactionTimeoutError("Transaction expired")

                try:
                    unsigned_tx["nonce"] = await self.w3.eth.get_transaction_count(self.wallet)
                    await self.db.commit()
                except Exception as blockchain_error:
                    raise BlockchainConnectionError(f"Failed to get nonce: {str(blockchain_error)}")

                await self._check_wallet_balance(pending_tx.total_cost_wei)
                tx_hash = await self._sign_and_send_transaction(unsigned_tx)

            receipt_model = await self._get_transaction_receipt(tx_hash)

            # Update transaction status
            pending_tx.status = 'signed'
            await self.db.commit()

            deployment = await self._create_deployment_record(pending_tx, receipt_model)

            #return deployment.to_response()
            return deployment.to_response().model_dump() if hasattr(deployment.to_response(), 'model_dump') else deployment.to_response()

        except ValidationError as e:
            self.db.rollback()
            raise InvalidParameterError(str(e))
        except Exception as e:
            self.db.rollback()
            raise

    async def _create_deployment_record(self, pending_tx: ToBeSigned, receipt_model: Dict) -> ContractDeployment:
        """Create contract deployment record from transaction receipt"""
        try:
            deployment = ContractDeployment(
                user_id=self.user_id,
                contract_address=receipt_model['contractAddress'],
                contract_type=self.transaction_type,
                contract_status=ContractStatus.DEPLOYED if receipt_model['status'] == 1 else ContractStatus.FAILED,

                name=pending_tx.name,
                symbol=pending_tx.symbol,
                description=pending_tx.description,
                image=pending_tx.image,

                transaction_hash=receipt_model['transactionHash'].hex(),
                transaction_from=receipt_model['from'],
                transaction_to=receipt_model['to'],
                transaction_status=TransactionStatus.SUCCESS if receipt_model['status'] == 1 else TransactionStatus.FAILURE,
                transaction_index=receipt_model['transactionIndex'],
                gas_used=receipt_model['gasUsed'],
                effective_gas_price=receipt_model['effectiveGasPrice'],
                cumulative_gas_used=receipt_model['cumulativeGasUsed'],
                transaction_type=receipt_model['type'],
                block_hash=receipt_model['blockHash'].hex(),
                block_number=receipt_model['blockNumber']
            )

            self.db.add(deployment)
            await self.db.commit()
            await self.db.refresh(deployment)

            return deployment
        except Exception as save_error:
            raise DatabaseError(f"Failed to save deployment record: {str(save_error)}")


@class_exponential_backoff()
class DeployErc20(ContractDeployer, ContractManager):
    """Main class for ERC20 contract deployment and management"""
    
    def __init__(self, db: AsyncSession, user_id: int, web3: AsyncWeb3, abi, bytecode,
                 transaction_type: TransactionType, redis: Optional[Redis] = None):
        
        super().__init__(db, user_id, 
                         web3, abi, bytecode, 
                         transaction_type=transaction_type, 
                         redis=redis
                         )
        
        from api.core.metrics import MetricsManager
        self.metrics = MetricsManager.get_instance().get_metrics()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

    async def deploy(self, name: str, symbol: str, description: Optional[str] = None,
                     file: Optional[UploadFile] = File(None), default_wallet: Optional[bool] = True) -> Dict:
        """Deploy a new contract"""
        deployment_start = time.time()
        try:
            # Record deployment attempt
            self.metrics['blockchain_ops'].labels(
                operation_type='contract_deployment',
                status='started'
            ).inc()

            result = await super().deploy(name, symbol, description, file, default_wallet)

            # Record deployment metrics
            deployment_time = time.time() - deployment_start
            self.metrics['deployment_time'].labels(
                contract_type=self.transaction_type.value
            ).observe(deployment_time)

            self.metrics['blockchain_ops'].labels(
                operation_type='contract_deployment',
                status='success'
            ).inc()

            logger.info(
                "Contract deployment successful",
                extra={
                    "contract_type": self.transaction_type.value,
                    "deployment_time": deployment_time,
                    "contract_name": name,
                    "contract_symbol": symbol
                }
            )

            return result

        except Exception as e:
            # Record failed deployment
            self.metrics['blockchain_ops'].labels(
                operation_type='contract_deployment',
                status='failed'
            ).inc()

            logger.error(
                "Contract deployment failed",
                extra={
                    "error": str(e),
                    "contract_type": self.transaction_type.value,
                    "contract_name": name,
                    "contract_symbol": symbol
                }
            )
            raise

    async def sign_and_send_transaction(self, transaction_id: int, trx_hash: Optional[str] = None) -> Dict:
        """Sign and send a pending transaction"""
        start_time = time.time()
        try:
            # Track gas usage with Prometheus
            result = await super().sign_and_send_transaction(transaction_id, trx_hash)

            if isinstance(result, dict) and 'transaction' in result:
                gas_used = result['transaction'].get('gasUsed', 0)
                self.metrics['gas_usage'].labels(
                    operation_type='contract_deployment'
                ).observe(gas_used)

            # Record successful transaction
            self.metrics['blockchain_ops'].labels(
                operation_type='transaction_sign',
                status='success'
            ).inc()

            logger.info(
                "Transaction signed and sent successfully",
                extra={
                    "transaction_id": transaction_id,
                    "duration": time.time() - start_time
                }
            )

            return result

        except Exception as e:
            # Record failed transaction
            self.metrics['blockchain_ops'].labels(
                operation_type='transaction_sign',
                status='failed'
            ).inc()

            logger.error(
                "Transaction signing failed",
                extra={
                    "error": str(e),
                    "transaction_id": transaction_id,
                    "duration": time.time() - start_time
                }
            )
            raise

    def handle_infinite_values(self, value):
        """Helper function to handle infinite values"""
        if isinstance(value, float) and (math.isinf(value) or math.isnan(value)):
            return 0
        return value
    

    async def get_contract_charts(self, freq: str) -> Dict:
        """Generate contract deployment charts by type"""
        try:
            start_time = time.time()
            cache_key = f"deployment_charts:{self.user_id}:{freq}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                logger.info("Returning cached deployment charts data")
                return cached_data

            order_by = ContractDeployment.id.desc()
            contract_query = await self.db.execute(
                select(ContractDeployment.created_at, ContractDeployment.contract_type)
                .filter_by(user_id=self.user_id)
                .order_by(order_by)
            )

            contract_data = contract_query.all()

            if not contract_data:
                raise ContractNotFoundError("No deployment data found for charts")

            deployErc20 = [row.created_at for row in contract_data if row.contract_type == TransactionType.ER20]
            deployErc721 = [row.created_at for row in contract_data if row.contract_type == TransactionType.ERC721]
            all_deployments = [row.created_at for row in contract_data]

            """
            deployErc20 = []
            deployErc721 = []
            all_deployments = []
            for created_at, contract_type in contract_query:
                all_deployments.append(created_at)
                if contract_type == TransactionType.ER20:
                    deployErc20.append(created_at)
                else:
                    deployErc721.append(created_at)
            """
            processed_data = await asyncio.to_thread(
                self._process_chart_data_sync,
                all_deployments,
                deployErc20,
                deployErc721,
                freq
            )
            response_data = {
                'status': 'success',
                'data': processed_data['data'],
                'overall_totals': {
                    'erc20': processed_data['total_deployed_erc20'],
                    'erc721': processed_data['total_deployed_erc721'],
                    'total': processed_data['total_deployed_erc20'] + processed_data['total_deployed_erc721'],
                    'latest_weekly_change': round(processed_data['latest_change'], 2)
                }
            }
            await self._cache_data(cache_key, response_data, self.general_cache_time)
            logger.info(f"Chart generation completed in {time.time() - start_time:.2f} seconds")
            return response_data

        except Exception as e:
            raise

    def _process_chart_data_sync(self, all_deployments, deployErc20, deployErc721, freq: str) -> Dict:

        try:
            if not all_deployments:
                raise DataProcessingError("No deployment data available for processing.")
            
            try:
                df_all = pd.DataFrame({'timestamp': pd.to_datetime(all_deployments)}).set_index('timestamp')
                df20 = pd.DataFrame({'timestamp': pd.to_datetime(deployErc20)}).set_index('timestamp')
                df721 = pd.DataFrame({'timestamp': pd.to_datetime(deployErc721)}).set_index('timestamp')
                """
                weekly_stats_all = pd.DataFrame({
                    'total_deployments': df_all.resample('W').size().fillna(0).astype('int32'),
                    'erc20_deployment': df20.resample('W').size().fillna(0).astype('int32'),
                    'erc721_deployment': df721.resample('W').size().fillna(0).astype('int32')
                })
                """
                stats = pd.DataFrame({
                    'erc20_deployment': df20.resample(freq).size(),
                    'erc721_deployment': df721.resample(freq).size()
                }).fillna(0).astype('int32')

                weekly_stats_all = pd.DataFrame({
                    'total_deployments': df_all.resample('W').size(),
                }).fillna(0).astype('int32')

                weekly_changes = pd.DataFrame({
                    'total_rate_change': weekly_stats_all['total_deployments']
                        .pct_change()
                        .fillna(0)
                        .replace([np.inf, -np.inf], 0)
                        .mul(100)
                })

            except Exception as data_error:
                raise DataProcessingError(f"Failed to process chart data: {str(data_error)}")

            total_deployed_erc20 = len(deployErc20)
            total_deployed_erc721 = len(deployErc721)

            # Format data for response
            try:
                """
                data = stats.apply(
                    lambda row: {
                        'timestamp': row.name.isoformat(),
                        'erc20_deployment': self.handle_infinite_values(row['erc20_deployment']),
                        'erc721_deployment': self.handle_infinite_values(row['erc721_deployment'])
                    }, axis=1
                ).tolist()
                """
                data = [
                    {
                        'timestamp': index.isoformat(),
                        # No need for handle_infinite_values if fillna(0) and replace(inf) done above
                        'erc20_deployment': int(row['erc20_deployment']),
                        'erc721_deployment': int(row['erc721_deployment'])
                    }
                    for index, row in stats.iterrows()
                ]
                """
                latest_change = (
                    float(weekly_changes['total_rate_change'].iloc[-1])
                    if not weekly_changes.empty and not math.isnan(weekly_changes['total_rate_change'].iloc[-1])
                    else None
                )"
                """

                latest_change = 0.0
                if not weekly_changes.empty:
                    raw_change = weekly_changes['total_rate_change'].iloc[-1]
                    if pd.notna(raw_change):
                        latest_change = float(raw_change)

            except Exception as format_error:
                raise DataProcessingError(f"Failed to format chart data: {str(format_error)}")
            
            return {
                'data': data,
                'total_deployed_erc20': total_deployed_erc20,
                'total_deployed_erc721': total_deployed_erc721,
                'latest_change': latest_change,
            }

        except Exception as e:
            logger.exception(f"Pandas processing failed: {e}") # Log exception
            raise
