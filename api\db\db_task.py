import asyncio
from celery import shared_task
from .backup import Backup<PERSON>anager
from datetime import datetime, timezone
from celery.utils.log import get_task_logger
from asgiref.sync import async_to_sync

task_logger = get_task_logger(__name__)


def run_async(coro):
    
    try:
        return asyncio.run(coro)
    except RuntimeError as e:
        if "Event loop is closed" in str(e) or "There is no current event loop" in str(e):
            task_logger.warning(f"Event loop issue: {e}")
            # Create a new loop manually as a fallback
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(coro)
            finally:
                loop.close()
        else:
            raise

import threading
import asyncio

def better_run_async(coro):
    # Thread-local storage for nested calls
    thread_local = threading.local()
    
    try:
        # Check if we're already in an event loop
        loop = asyncio.get_running_loop()
        
        if loop.is_running():
            raise RuntimeError("Cannot run async code in running event loop")
            
        return loop.run_until_complete(coro)
        
    except RuntimeError:
        # No running loop found, create new one
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
            asyncio.set_event_loop(None)

@shared_task(name="create_database_backup")
def create_database_backup():
    try:
        async def _backup():
            backup_manager = BackupManager()
            backup_file = await backup_manager.create_database_backup()
            if backup_file:
                await backup_manager.cleanup_old_backups()
                task_logger.info(f"Database backup created: {backup_file}")
                return {"status": "success", "backup_file": backup_file}
            return {"status": "failed", "error": "Backup creation failed"}

        return asyncio.run(_backup())
        #return async_to_sync(_backup)()

    except Exception as e:
        task_logger.error(f"Backup task failed: {str(e)}")
        return {"status": "failed", "error": str(e)}

@shared_task(name="cleanup_old_backups_task")
def cleanup_old_backups_task():
    try:
        async def _cleanup():
            backup_manager = BackupManager()
            await backup_manager.cleanup_old_backups()
            task_logger.info("Old backups cleaned up")
            return {"status": "success"}

        return asyncio.run(_cleanup())
        #return async_to_sync(_cleanup)()
    except Exception as e:
        task_logger.error(f"Cleanup task failed: {str(e)}")
        return {"status": "failed", "error": str(e)}

@shared_task(name="tasks.vacuum_analyze")
def scheduled_vacuum_analyze():
    try:
        async def _vacuum():
            current_hour = datetime.now(timezone.utc).hour
            if not (2 <= current_hour <= 4):
                return "Skipped - Outside maintenance window"
            backup_manager = BackupManager()
            try:
                health = await backup_manager.check_database_health()
                if health.get("connections", 0) > 10:
                    return "Skipped - High database activity"
                success = await backup_manager.vacuum_analyze()
                return "Success" if success else "Failed"
            except Exception as e:
                return f"Error: {str(e)}"

        return asyncio.run(_vacuum())
        #return async_to_sync(_vacuum)()

    except Exception as e:
        task_logger.error(f"Vacuum analyze task failed: {e}")
        return f"Error: {str(e)}"

@shared_task(name="tasks.monitor_database_health")
def monitor_database_health():
    try:
        async def check_health_and_recover():
            backup_manager = BackupManager()
            health = await backup_manager.check_database_health()

            if health.get("status") == "unhealthy":
                task_logger.warning("Database status unhealthy, attempting auto-recovery.")
                try:
                    recovery_success = await backup_manager.auto_recover()
                    if not recovery_success:
                        task_logger.error("Auto-recovery failed. Consider manual intervention.")
                    else:
                        task_logger.info("Auto-recovery successful.")
                except Exception as recovery_err:
                    task_logger.error(f"Error during auto_recover call: {recovery_err}", exc_info=True)
            return health

        health = asyncio.run(check_health_and_recover())
        #health = async_to_sync(check_health_and_recover)()
        task_logger.info(f"Database health: {health}")
        return health

    except Exception as e:
        task_logger.error(f"Health monitor failed: {e}")
        return {"status": "error", "error": str(e), "timestamp": datetime.now(timezone.utc).isoformat()}
