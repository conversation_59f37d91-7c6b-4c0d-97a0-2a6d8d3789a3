from api.v1.account.models import Account as AccountWallet
from api.v1.account import schemas as AccountSchemas
from sqlalchemy.ext.asyncio import AsyncSession
from eth_account import Account
from fastapi import HTTPException, status
from .models import Account<PERSON>alan<PERSON>History
from datetime import datetime, timedelta, timezone
from .schemas import AccountBalance
from config import config
from api.core.blockchain_dep import class_exponential_backoff
from .exceptions import (logger, handle_chart_error, DataProcessingError,
                         BlockchainError)
import json
from typing import Any, Optional
import time
from redis.asyncio import Redis
from web3 import AsyncWeb3
from sqlalchemy.future import select
from api.core.wallet_security import WalletSecurity
import asyncio

PLATFORM_WALLET = config.PLATFORM_WALLET
PLATFORM_PRIVATE_KEY= config.PLATFORM_PRIVATE_KEY
PROVIDER = config.PROVIDER
PRODUCTION = config.PRODUCTION
CACHE_TIME = 600
CHUNCK_SIZE = 50
LONG_CHUNCK_SIZE = 3600

@class_exponential_backoff()
class Vault:

    def __init__(self, web3: AsyncWeb3, redis: Optional[Redis] = None):
        self.redis = redis
        self.web3 = web3
        self.cache_time = CACHE_TIME
        self.wallet_security = WalletSecurity()

    def _handle_chart_error(self, error: Exception) -> None:
        """Handle errors in event processing"""
        handle_chart_error(error)


    async def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache with error handling"""
        if not self.redis:
            return None

        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Cache data corruption detected: {str(e)}")
            await self.redis.delete(cache_key)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval failed: {str(e)}")
            return None

    async def _cache_data(self, cache_key: str, data: Any, cache_time: int) -> None:
        """Cache data with error handling"""
        if not self.redis:
            return None

        try:
            await self.redis.set(cache_key, json.dumps(data))
            await self.redis.expire(cache_key, cache_time)
        except Exception as e:
            logger.warning(f"Failed to cache data: {str(e)}")




    async def _get_account(self, user_id: int, db: AsyncSession) -> AccountWallet:
        """Helper to get the account, raising an exception if it doesn't exist."""
        result = await db.execute(select(AccountWallet).filter(AccountWallet.user_id == user_id))
        account = result.scalar_one_or_none()
        if not account:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User does not have a vault")
        return account


    async def create_wallet(self, user_id: int,  db: AsyncSession):
        try:
            result = await db.execute(select(AccountWallet).filter(AccountWallet.user_id == user_id))
            account = result.scalar_one_or_none()
            if account:
                raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="User already has a vault")
            try:
                #eth_account = Account.create()
                eth_account = await asyncio.to_thread(Account.create)
                raw_private_key_hex = eth_account._private_key.hex() # Get the raw key temporarily
            except Exception as e:
                logger.error(f"Failed to fetch blockchain data: {str(e)}")
                raise BlockchainError(f"Failed to fetch blockchain data: {str(e)}")

            try:
                encrypted_package = await asyncio.to_thread(
                    self.wallet_security.encrypt_data_with_master_key,
                    raw_private_key_hex
                )
                logger.info(f"Private key encrypted successfully for user {user_id}")
            except Exception as e:
                logger.exception(f"CRITICAL: Failed to encrypt private key for user {user_id}: {e}")
                raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to secure vault credentials.")

            create_vault = AccountWallet(
                user_id = user_id,
                address = eth_account.address,
                # Store the encrypted components
                encrypted_private_key=encrypted_package["ciphertext"],
                encryption_salt=encrypted_package["salt"],
                encryption_nonce=encrypted_package["nonce"]
            )

            db.add(create_vault)
            await db.commit()
            return AccountSchemas.AccountCreate(
                id =  create_vault.id,
                address = create_vault.address,
                created_at = create_vault.created_at,
                )

        except Exception as e:
            self._handle_chart_error(e)


    async def _get_decrypted_private_key(self, user_id: int, db: AsyncSession) -> str:
        """
        INTERNAL USE ONLY. Retrieves and decrypts the private key.
        """
        logger.warning(f"Attempting to retrieve and decrypt private key for user {user_id}. This should be infrequent.")
        try:
            account = await self._get_account(user_id, db)

            if not account.encrypted_private_key or not account.encryption_salt or not account.encryption_nonce:
                 logger.error(f"Missing encrypted key components for user {user_id} in database.")
                 raise ValueError("Stored key data is incomplete.")

            encrypted_package = {
                "salt": account.encryption_salt,
                "nonce": account.encryption_nonce,
                "ciphertext": account.encrypted_private_key
            }

            decrypted_key = await asyncio.to_thread(
                self.wallet_security.decrypt_data_with_master_key,
                encrypted_package
            )
            logger.info(f"Successfully decrypted private key for user {user_id}.")
            return decrypted_key

        except ValueError as e:
            logger.exception(f"CRITICAL: Failed to decrypt private key for user {user_id}: {e}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to access secure credentials.")
        except HTTPException:
            raise
        except Exception as e:
            logger.exception(f"Unexpected error decrypting key for user {user_id}: {e}")
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal server error during secure operation.")
    


    async def get_vault(self, user_id: int, db: AsyncSession):

        start_time = time.time()
        cache_key = f"vault_data:{user_id}"
        try:
            vault_data = await self._get_cached_data(cache_key)
            if vault_data:
                vault_data = vault_data
            else:
                account = await self._get_account(user_id, db)

                vault_data = {
                    'id' :  account.id,
                    'address' : account.address,
                    'created_at' : account.created_at.isoformat(),
                    #'private_key' : self._get_decrypted_private_key(user_id, db)
                }

                await self._cache_data(
                    cache_key,
                    vault_data,
                    self.cache_time
                )

            logger.info(f"get vault retrival completed in {time.time() - start_time:.2f} seconds")
            return vault_data

        except Exception as e:
            self._handle_chart_error(e)



    async def check_balance(self, user_id: int, db: AsyncSession):
        start_time = time.time()
        cache_key = f"user_balance:{user_id}"
        try:
            user_balance = await self._get_cached_data(cache_key)
            if user_balance:
                user_balance = AccountBalance(**user_balance)
            else:

                account = await self._get_account(user_id, db)

                if not PRODUCTION:
                    address = PLATFORM_WALLET
                else:
                    address = account.address

                balance_wei = await self.web3.eth.get_balance(address)
                balance_eth = float(self.web3.from_wei(balance_wei, 'ether'))

                # Store the current balance in history
                new_history = AccountBalanceHistory(
                    user_id=user_id,
                    account_id=account.id,
                    balance=balance_eth,
                    created_at=datetime.now(timezone.utc)
                )
                db.add(new_history)
                await db.commit()
                await db.refresh(new_history)


                weekly_rate_change, previous_week_balance = await self._calculate_weekly_balance_stats(user_id, balance_eth, db)


                user_balance =  AccountBalance(
                    id=account.id,
                    address=account.address,
                    balance=balance_eth,
                    weekly_rate_change=round(weekly_rate_change, 2),
                    previous_week_balance=round(previous_week_balance, 6),
                    current_week_balance=round(balance_eth, 6)
                )
                await self._cache_data(
                    cache_key,
                    user_balance.model_dump(mode='json'),
                    self.cache_time
                )
            logger.info(f"get vault retrival completed in {time.time() - start_time:.2f} seconds")
            return user_balance
        except HTTPException:
            raise
        except Exception as e:
            self._handle_chart_error(e)


    async def _calculate_weekly_balance_stats(self, user_id: int, balance_eth: float, db: AsyncSession) -> tuple[float, float]:
        """Helper to calculate weekly balance statistics."""
        try:

            one_week_ago = datetime.now(timezone.utc) - timedelta(days=7)
            two_weeks_ago = datetime.now(timezone.utc) - timedelta(days=14)

            result = await db.execute(
                select(AccountBalanceHistory)
                .filter(
                    AccountBalanceHistory.user_id == user_id,
                    AccountBalanceHistory.created_at >= two_weeks_ago,
                    AccountBalanceHistory.created_at < one_week_ago
                )
            )
            previous_week_balances = result.scalars().all()

            previous_week_balance = (
                float(previous_week_balances[-1].balance) if previous_week_balances else balance_eth
            )
            weekly_rate_change = (
                ((balance_eth - previous_week_balance) / previous_week_balance * 100)
                if previous_week_balance > 0
                else 0.0
            )
            return weekly_rate_change, previous_week_balance

        except Exception as e:
            logger.error(f"Failed to process statistics: {str(e)}")
            raise DataProcessingError(f"Failed to process statistics: {str(e)}")
