{"_format": "hh-sol-artifact-1", "contractName": "VestingWallet", "sourceName": "contracts/finance/VestingWallet.sol", "abi": [{"inputs": [{"internalType": "address", "name": "beneficiary", "type": "address"}, {"internalType": "uint64", "name": "startTimestamp", "type": "uint64"}, {"internalType": "uint64", "name": "durationSeconds", "type": "uint64"}], "stateMutability": "payable", "type": "constructor"}, {"inputs": [], "name": "FailedCall", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}, {"internalType": "uint256", "name": "needed", "type": "uint256"}], "name": "InsufficientBalance", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "OwnableInvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "OwnableUnauthorizedAccount", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ERC20Released", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "EtherReleased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "duration", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "end", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "releasable", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "releasable", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "release", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "release", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "released", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "released", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "start", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint64", "name": "timestamp", "type": "uint64"}], "name": "vestedAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint64", "name": "timestamp", "type": "uint64"}], "name": "vestedAmount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "0x6080604052600436106100dc575f3560e01c8063961325211161007c578063be9a655511610057578063be9a65551461024a578063efbe1c1c1461025e578063f2fde38b14610272578063fbccedae14610291575f80fd5b806396132521146101e35780639852595c146101f7578063a3f8eace1461022b575f80fd5b8063715018a6116100b7578063715018a614610176578063810ec23b1461018a57806386d1a69f146101a95780638da5cb5b146101bd575f80fd5b80630a17b06b146100e75780630fb5a6b4146101195780631916558714610155575f80fd5b366100e357005b5f80fd5b3480156100f2575f80fd5b50610106610101366004610827565b6102a5565b6040519081526020015b60405180910390f35b348015610124575f80fd5b507f000000000000000000000000000000000000000000000000000000000000000067ffffffffffffffff16610106565b348015610160575f80fd5b5061017461016f366004610856565b6102c8565b005b348015610181575f80fd5b50610174610360565b348015610195575f80fd5b506101066101a436600461086f565b610373565b3480156101b4575f80fd5b50610174610406565b3480156101c8575f80fd5b505f546040516001600160a01b039091168152602001610110565b3480156101ee575f80fd5b50600154610106565b348015610202575f80fd5b50610106610211366004610856565b6001600160a01b03165f9081526002602052604090205490565b348015610236575f80fd5b50610106610245366004610856565b610477565b348015610255575f80fd5b506101066104a3565b348015610269575f80fd5b506101066104d1565b34801561027d575f80fd5b5061017461028c366004610856565b610514565b34801561029c575f80fd5b50610106610553565b5f6102c26102b260015490565b6102bc90476108b4565b83610570565b92915050565b5f6102d282610477565b6001600160a01b0383165f908152600260205260408120805492935083929091906102fe9084906108b4565b90915550506040518181526001600160a01b038316907fc0e523490dd523c33b1878c9eb14ff46991e3f5b2cd33710918618f2a39cba1b9060200160405180910390a261035c826103565f546001600160a01b031690565b83610612565b5050565b610368610669565b6103715f610695565b565b6001600160a01b0382165f908152600260205260408120546103ff906040516370a0823160e01b81523060048201526001600160a01b038616906370a0823190602401602060405180830381865afa1580156103d1573d5f803e3d5ffd5b505050506040513d601f19601f820116820180604052508101906103f591906108c7565b6102bc91906108b4565b9392505050565b5f61040f610553565b90508060015f82825461042291906108b4565b90915550506040518181527fda9d4e5f101b8b9b1c5b76d0c5a9f7923571acfc02376aa076b75a8c080c956b9060200160405180910390a161047461046e5f546001600160a01b031690565b826106e4565b50565b6001600160a01b0381165f908152600260205260408120546104998342610373565b6102c291906108de565b67ffffffffffffffff7f00000000000000000000000000000000000000000000000000000000000000001690565b5f7f000000000000000000000000000000000000000000000000000000000000000067ffffffffffffffff166105056104a3565b61050f91906108b4565b905090565b61051c610669565b6001600160a01b03811661054a57604051631e4fbdf760e01b81525f60048201526024015b60405180910390fd5b61047481610695565b5f61055d60015490565b610566426102a5565b61050f91906108de565b5f6105796104a3565b8267ffffffffffffffff16101561059157505f6102c2565b6105996104d1565b8267ffffffffffffffff16106105b05750816102c2565b7f000000000000000000000000000000000000000000000000000000000000000067ffffffffffffffff166105e36104a3565b6105f79067ffffffffffffffff85166108de565b61060190856108f1565b61060b9190610908565b90506102c2565b604080516001600160a01b038416602482015260448082018490528251808303909101815260649091019091526020810180516001600160e01b031663a9059cbb60e01b179052610664908490610776565b505050565b5f546001600160a01b031633146103715760405163118cdaa760e01b8152336004820152602401610541565b5f80546001600160a01b038381166001600160a01b0319831681178455604051919092169283917f8be0079c531659141344cd1fd0a4f28419497f9722a3daafe3b4186f6b6457e09190a35050565b8047101561070e5760405163cf47918160e01b815247600482015260248101829052604401610541565b5f80836001600160a01b0316836040515f6040518083038185875af1925050503d805f8114610758576040519150601f19603f3d011682016040523d82523d5f602084013e61075d565b606091505b50915091508161077057610770816107e2565b50505050565b5f8060205f8451602086015f885af180610795576040513d5f823e3d81fd5b50505f513d915081156107ac5780600114156107b9565b6001600160a01b0384163b155b1561077057604051635274afe760e01b81526001600160a01b0385166004820152602401610541565b8051156107f25780518082602001fd5b60405163d6bda27560e01b815260040160405180910390fd5b803567ffffffffffffffff81168114610822575f80fd5b919050565b5f60208284031215610837575f80fd5b6103ff8261080b565b80356001600160a01b0381168114610822575f80fd5b5f60208284031215610866575f80fd5b6103ff82610840565b5f8060408385031215610880575f80fd5b61088983610840565b91506108976020840161080b565b90509250929050565b634e487b7160e01b5f52601160045260245ffd5b808201808211156102c2576102c26108a0565b5f602082840312156108d7575f80fd5b5051919050565b818103818111156102c2576102c26108a0565b80820281158282048414176102c2576102c26108a0565b5f8261092257634e487b7160e01b5f52601260045260245ffd5b50049056fea2646970667358221220a0a7ef13249a06443e2bad3b466e7d9850ee030d0ed4b7ae9f6f78d8b6cf6ae664736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}