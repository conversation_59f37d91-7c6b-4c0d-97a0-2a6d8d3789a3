// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

/**
 * @title Minimal ERC721 Implementation with Platform Fees
 * @dev A production-ready version of ERC721 without OpenZeppelin dependencies.
 *      Includes platform fee logic, ownership, and core ERC721 methods.
 *
 *      This contract implements the following functions:
 *        - mint
 *        - approve
 *        - renounceOwnership
 *        - safeTransferFrom (two variants)
 *        - setApprovalForAll
 *        - transferFrom
 *        - transferOwnership
 *
 *      And includes the view methods:
 *        - name
 *        - symbol
 *        - owner
 *        - ownerOf
 *        - balanceOf
 *        - isApprovedForAll
 *        - getApproved
 *        - tokenURI
 *        - totalSupply (common extension)
 *        - supportsInterface (basic ERC165)
 *        - getCurrentTokenId (custom)
 */
contract NFTContract {
    //=============================================
    // Events
    //=============================================

    event Mint(address indexed to, uint256 indexed tokenId, string tokenURI);

    /// @dev ERC-4906: Emit for full collection updates
    event BatchMetadataUpdate(uint256 _fromTokenId, uint256 _toTokenId);

    /// @dev ERC-4906 Standard Metadata Update event
    event MetadataUpdate(uint256 _tokenId);

    /// @dev Emitted when a token is transferred.
    event Transfer(address indexed from, address indexed to, uint256 indexed tokenId);

    /// @dev Emitted when `owner` enables `approved` to manage the `tokenId` token.
    event Approval(address indexed owner, address indexed approved, uint256 indexed tokenId);

    /// @dev Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets.
    event ApprovalForAll(address indexed owner, address indexed operator, bool approved);

    /// @dev Emitted when ownership of the contract changes.
    event OwnershipTransferred(address indexed previousOwner, address indexed newOwner);

    /// @dev Emitted when platform fees are collected.
    event PlatformFeeCollected(address indexed payer, uint256 flatFee, uint256 percentageFee, uint256 totalFee);

    //=============================================
    // Mappings and Variables
    //=============================================

    // Token name
    string private _name;

    // Token symbol
    string private _symbol;

    // Contract owner address
    address private _owner;

    // Mapping from token ID to owner
    mapping(uint256 => address) private _owners;

    // Mapping owner address to token count
    mapping(address => uint256) private _balances;

    // Mapping from token ID to approved address
    mapping(uint256 => address) private _tokenApprovals;

    // Mapping from owner to operator approvals
    mapping(address => mapping(address => bool)) private _operatorApprovals;

    // Next token ID to mint
    uint256 private _nextTokenId;

    // Optional mapping for token URIs
    mapping(uint256 => string) private _tokenURIs;

    //=============================================
    // Platform Fee Variables
    //=============================================

    // Address where fees are collected
    address public platformWallet;

    // Fee percentage in basis points (e.g. 100 = 1%, 1000 = 10%)
    uint256 public platformFeePercentage;

    // Flat fee charged per transaction
    uint256 public flatFeeAmount;

    //=============================================
    // Constructor
    //=============================================

    /**
     * @dev Deploy the contract with a given `name_`, `symbol_`, and platform fee parameters.
     * @param name_ Name of the NFT collection.
     * @param symbol_ Symbol of the NFT collection.
     * @param _platformWallet Address where platform fees are sent.
     * @param _platformFeePercentage Fee percentage in basis points (e.g. 500 = 5%).
     * @param _flatFeeAmount Fixed fee in wei.
     */
    constructor(
        string memory name_,
        string memory symbol_,
        address _platformWallet,
        uint256 _platformFeePercentage,
        uint256 _flatFeeAmount
    ) {
        require(_platformWallet != address(0), "Invalid platform wallet");

        _name = name_;
        _symbol = symbol_;
        _owner = msg.sender;

        platformWallet = _platformWallet;
        platformFeePercentage = _platformFeePercentage;
        flatFeeAmount = _flatFeeAmount;
        _nextTokenId = 0;

        emit OwnershipTransferred(address(0), _owner);
    }

    //=============================================
    // Modifiers
    //=============================================

    modifier onlyOwner() {
        require(msg.sender == _owner, "Caller is not the owner");
        _;
    }

    /**
     * @dev Collects the required fee (flatFee + percentageFee) from msg.value.
     * @param amount A reference value for percentage calculation.
     */
    modifier collectFullPlatformFee(uint256 amount) {
        uint256 percentageFee = (amount * platformFeePercentage) / 10000; // e.g. 500 => 5%
        uint256 totalFee = flatFeeAmount + percentageFee;
        require(msg.value >= totalFee, "Insufficient platform fee");

        (bool success, ) = platformWallet.call{value: totalFee}("");
        require(success, "Platform fee transfer failed");

        emit PlatformFeeCollected(msg.sender, flatFeeAmount, percentageFee, totalFee);
        _;
    }



    //Modifier fee for nono payment transactions (flat fee)
    modifier collectFlatFee(){
        require(msg.value >= flatFeeAmount, "Insufficient flat fee");
        (bool success, ) = platformWallet.call{value: flatFeeAmount}("");
        require(success, "Platform fee transfer failed");

        emit  PlatformFeeCollected(msg.sender, flatFeeAmount, 0, flatFeeAmount);
        _;
    }

    //=============================================
    // ERC721 Core Logic
    //=============================================

    /**
     * @dev Returns the token collection name.
     */
    function name() external view returns (string memory) {
        return _name;
    }

    /**
     * @dev Returns the token collection symbol.
     */
    function symbol() external view returns (string memory) {
        return _symbol;
    }

    /**
     * @dev Returns the owner of the `tokenId` token.
     */
    function ownerOf(uint256 tokenId) public view returns (address) {
        address tokenOwner = _owners[tokenId];
        require(tokenOwner != address(0), "Token does not exist");
        return tokenOwner;
    }

    /**
     * @dev Returns the number of tokens in `owner`'s account.
     */
    function balanceOf(address owner) public view returns (uint256) {
        require(owner != address(0), "Zero address not valid");
        return _balances[owner];
    }

    /**
     * @dev Basic totalSupply: returns the number of tokens minted so far.
     *      This does not account for burns (if any implemented).
     */
    function totalSupply() external view returns (uint256) {
        return _nextTokenId;
    }

    /**
     * @dev Returns the next token ID that will be assigned to the newly minted NFT.
     *      The last minted NFT ID is `_nextTokenId - 1`.
     */
    function getCurrentTokenId() external view returns (uint256) {
        return _nextTokenId;
    }

    /**
     * @dev Implementation of ERC165 for detecting standard interfaces.
     *      This contract claims support for ERC721 (0x80ac58cd),
     *      ERC721Metadata (0x5b5e139f), and ERC165 (0x01ffc9a7).
     */
    function supportsInterface(bytes4 interfaceId) external pure returns (bool) {
        return (
            interfaceId == 0x80ac58cd || // ERC721
            interfaceId == 0x5b5e139f || // ERC721Metadata
            interfaceId == 0x49064906 || // ERC-4906 MetadataUpdate
            interfaceId == 0x01ffc9a7    // ERC165
        );
    }

    /// @dev Call this for collection-wide metadata updates
    function refreshCollectionMetadata() external onlyOwner {
        emit BatchMetadataUpdate(0, _nextTokenId - 1);
    }

    /**
     * @dev See EIP-721 for ERC721 transferFrom.
     *      We make it payable if you want to integrate platform fees.
     * @param from The address which currently owns the token.
     * @param to The address to receive the ownership of the given token ID.
     * @param tokenId The token ID to transfer.
     */
    function transferFrom(
        address from,
        address to,
        uint256 tokenId
    ) public payable collectFlatFee(){
        require(_isApprovedOrOwner(msg.sender, tokenId), "Not owner nor approved");
        _transfer(from, to, tokenId);
    }

    /**
     * @dev Safely transfers `tokenId` token from `from` to `to`.
     *      If `to` is a contract, it must implement IERC721Receiver.
     */
    function safeTransferFrom(
        address from,
        address to,
        uint256 tokenId
    ) public payable collectFlatFee(){
        safeTransferFrom(from, to, tokenId, "");
    }

    /**
     * @dev Overloaded safeTransferFrom.
     * @param from The address which currently owns the token.
     * @param to The address to receive the ownership of the given token ID.
     * @param tokenId The token ID to transfer.
     * @param _data Extra data to send along to a receiver contract.
     */
    function safeTransferFrom(
        address from,
        address to,
        uint256 tokenId,
        bytes memory _data
    ) public payable collectFlatFee(){
        require(_isApprovedOrOwner(msg.sender, tokenId), "Not owner nor approved");
        _safeTransfer(from, to, tokenId, _data);
    }

    /**
     * @dev Gives permission to `to` to transfer `tokenId` token.
     *      The approval is cleared when the token is transferred.
     */
    function approve(address to, uint256 tokenId) external payable collectFlatFee(){
        address owner_ = ownerOf(tokenId);
        require(to != owner_, "Approve to current owner");
        require(
            msg.sender == owner_ || isApprovedForAll(owner_, msg.sender),
            "Not owner nor approved for all"
        );

        _approve(to, tokenId);
    }

    /**
     * @dev Approve or remove `operator` as an operator for the caller.
     *      Operators can call transferFrom or safeTransferFrom for any token owned by the caller.
     */
    function setApprovalForAll(address operator, bool approved) external payable collectFlatFee(){
        require(operator != msg.sender, "Can't set approval for self");
        _operatorApprovals[msg.sender][operator] = approved;
        emit ApprovalForAll(msg.sender, operator, approved);
    }

    /**
     * @dev Checks if `operator` is allowed to manage all of the assets of `owner_`.
     */
    function isApprovedForAll(address owner_, address operator) public view returns (bool) {
        return _operatorApprovals[owner_][operator];
    }

    /**
     * @dev Returns the account approved for `tokenId` token.
     */
    function getApproved(uint256 tokenId) external view returns (address) {
        require(_owners[tokenId] != address(0), "Token does not exist");
        return _tokenApprovals[tokenId];
    }

    /**
     * @dev Creates a new token and transfers it to `to`.
     *      Uses the `collectFullPlatformFee` modifier to enforce platform fees if desired.
     * @param to The address that will own the minted token.
     * @param tokenURI_ The token metadata URI.
     */
    function mint(address to, string memory tokenURI_) external payable collectFullPlatformFee(1) {
        require(to != address(0), "Mint to zero address");
        uint256 tokenId = _nextTokenId;
        _nextTokenId++;
        _mint(to, tokenId);
        _setTokenURI(tokenId, tokenURI_);
        
        // Emit custom mint event
        emit Mint(to, tokenId, tokenURI_);
    }

    //=============================================
    // Internal Functions
    //=============================================

    function _isApprovedOrOwner(address spender, uint256 tokenId) internal view returns (bool) {
        address owner_ = ownerOf(tokenId);
        return (spender == owner_ ||
            _tokenApprovals[tokenId] == spender ||
            _operatorApprovals[owner_][spender]
        );
    }

    function _transfer(
        address from,
        address to,
        uint256 tokenId
    ) internal {
        require(ownerOf(tokenId) == from, "From not owner");
        require(to != address(0), "Transfer to zero address");

        // Clear approvals from the previous owner
        _approve(address(0), tokenId);

        _balances[from] -= 1;
        _balances[to] += 1;
        _owners[tokenId] = to;

        emit Transfer(from, to, tokenId);
    }

    function _safeTransfer(
        address from,
        address to,
        uint256 tokenId,
        bytes memory _data
    ) internal {
        _transfer(from, to, tokenId);
        require(
            _checkOnERC721Received(from, to, tokenId, _data),
            "Transfer to non ERC721Receiver"
        );
    }

    function _mint(address to, uint256 tokenId) internal {
        require(_owners[tokenId] == address(0), "Token already minted");
        _owners[tokenId] = to;
        _balances[to] += 1;
        emit Transfer(address(0), to, tokenId);
    }

    function _approve(address to, uint256 tokenId) internal {
        _tokenApprovals[tokenId] = to;
        emit Approval(ownerOf(tokenId), to, tokenId);
    }

    function _setTokenURI(uint256 tokenId, string memory _tokenURI) internal {
        require(_owners[tokenId] != address(0), "Token does not exist");
        _tokenURIs[tokenId] = _tokenURI;
        
        // Emit standard metadata update event
        emit MetadataUpdate(tokenId);
    }

    function _checkOnERC721Received(
        address from,
        address to,
        uint256 tokenId,
        bytes memory _data
    ) private returns (bool) {
        // If `to` is not a contract, there's no need to check.
        if (to.code.length == 0) {
            return true;
        }
        // Attempt to call `onERC721Received` on `to`.
        // An ERC721Receiver implementing contract should return the function signature.
        (bool success, bytes memory returndata) = to.call(
            abi.encodeWithSelector(
                bytes4(keccak256("onERC721Received(address,address,uint256,bytes)")),
                msg.sender,
                from,
                tokenId,
                _data
            )
        );
        if (!success) {
            return false;
        }
        bytes4 retval = abi.decode(returndata, (bytes4));
        return (retval == bytes4(keccak256("onERC721Received(address,address,uint256,bytes)")));
    }

    //=============================================
    // Ownership Management
    //=============================================

    /**
     * @dev Returns the address of the current owner.
     */
    function owner() public view returns (address) {
        return _owner;
    }

    /**
     * @dev Leaves the contract without owner.
     *      It will not be possible to call `onlyOwner` functions anymore.
     */
    function renounceOwnership() external onlyOwner {
        emit OwnershipTransferred(_owner, address(0));
        _owner = address(0);
    }

    /**
     * @dev Transfers ownership of the contract to a new account (`newOwner`).
     * @param newOwner The address that becomes the new owner.
     */
    function transferOwnership(address newOwner) external onlyOwner {
        require(newOwner != address(0), "New owner is zero");
        emit OwnershipTransferred(_owner, newOwner);
        _owner = newOwner;
    }

    //=============================================
    // Token URI
    //=============================================

    /**
     * @dev Returns the token URI for a given token ID.
     */
    function tokenURI(uint256 tokenId) external view returns (string memory) {
        require(_owners[tokenId] != address(0), "Token does not exist");
        return _tokenURIs[tokenId];
    }

    //=============================================
    // Platform Fee Updates
    //=============================================

    /**
     * @dev Updates the platform wallet.
     */
    function setPlatformWallet(address _newWallet) external onlyOwner {
        require(_newWallet != address(0), "Invalid wallet");
        platformWallet = _newWallet;
    }

    /**
     * @dev Updates the platform fee percentage (in basis points).
     */
    function setPlatformFee(uint256 _newFee) external onlyOwner {
        require(_newFee <= 10000, "Fee too high"); // Up to 100%
        platformFeePercentage = _newFee;
    }

    /**
     * @dev Updates the flat fee amount.
     */
    function setFlatFee(uint256 _newFee) external onlyOwner {
        require(_newFee <= 10 ether, "Fee too high");
        flatFeeAmount = _newFee;
    }

    //=============================================
    // Fallbacks
    //=============================================

    /**
     * @dev Fallback function to accept ETH.
     */
    fallback() external payable {}

    /**
     * @dev Receive function to accept ETH.
     */
    receive() external payable {}
}
