from pydantic import BaseModel, Field, model_validator, EmailStr, ValidationError, HttpUrl, ConfigDict
from typing import Optional
from datetime import datetime, timezone
from api.v1.user.models import User as UserModel
from fastapi import HTTPException, status
from api.core import responses
from api.db.database import SessionLocal
from pydantic import BaseModel, model_validator, EmailStr, ValidationError, HttpUrl


class UserBase(BaseModel):
    first_name: str
    last_name: str
    email: EmailStr
    #wallet_id: Optional[str] = None
    #is_active: bool = True
    #date_created: Optional[datetime] = datetime.now()
    #last_updated: Optional[datetime] = datetime.now()

    class Config:
        from_attributes = True


class ResetPassword(BaseModel):
    current_password: str
    new_password: str
    confirm_password: str


class PasswordResetRequest(BaseModel):
    email: EmailStr

"""
class PasswordReset(BaseModel):
    data: str
    new_password: str

    @model_validator(mode='before')
    @classmethod
    def validate_password(cls, values):
        password = values.get("new_password")
        if not password:
            raise ValueError("Password is required")
        if len(password) < 5:
            raise ValueError("Password must be at least 5 characters long")
        if not any(char.isdigit() for char in password):
            raise ValueError("Password must contain at least one digit")
        if not any(char.isalpha() for char in password):
            raise ValueError("Password must contain at least one letter")
        return values
"""


class PasswordReset(BaseModel):
    email: EmailStr
    code: str
    new_password: str

    @model_validator(mode='before')
    @classmethod
    def validate_password(cls, values):
        password = values.get("new_password")
        if not password:
            raise ValueError("Password is required")
        if len(password) < 5:
            raise ValueError("Password must be at least 5 characters long")
        if not any(char.isdigit() for char in password):
            raise ValueError("Password must contain at least one digit")
        if not any(char.isalpha() for char in password):
            raise ValueError("Password must contain at least one letter")
        return values



class EmailVerificationRequest(BaseModel):
    email: EmailStr
    verification_code: str



class CreateUser(UserBase):
    password: str

    class Config:
        from_attributes = True

    @model_validator(mode='before')
    @classmethod
    def validate_password(cls, values):
        password = values.get("password")
        if not password:
            raise ValueError("Password is required")
        if len(password) < 5:
            raise ValueError("Password must be at least 5 characters long")
        if not any(char.isdigit() for char in password):
            raise ValueError("Password must contain at least one digit")
        if not any(char.isalpha() for char in password):
            raise ValueError("Password must contain at least one letter")
        return values

    """
    @model_validator(mode='before')
    @classmethod
    def validate_email(cls, values):
        email = values.get("email")
        if not email:
            raise ValidationError("Email is required", model=cls)

        with SessionLocal() as db:
            user_email = db.query(UserModel).filter(UserModel.email == email).first()
            if user_email:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=responses.EMAIL_IN_USE
                )
        return values
    """

class UpdateUser(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None
    is_active: Optional[bool] = None

    class Config:
        from_attributes=True




class ShowUser(UserBase):
    id: int
    date_created: Optional[datetime] = datetime.now(timezone.utc)
    last_updated: Optional[datetime] = datetime.now(timezone.utc)
    is_active: Optional[bool] = None
    is_deleted: Optional[bool]

    model_config = ConfigDict(from_attributes=True)

class User(UserBase):
    id: int
    is_deleted: Optional[bool]

    model_config = ConfigDict(from_attributes=True)



class AvatarCreate(BaseModel):
    user_id: int
    avatar_url: HttpUrl



class GoogleUserInfo(BaseModel):
    id: Optional[str] = None
    email: Optional[str] = None
    verified_email: Optional[bool] = None
    name: Optional[str] = None
    given_name: Optional[str] = None
    family_name: Optional[str] = None
    picture: Optional[str] = None


class UserAvater(BaseModel):
    avatar: str

    model_config = ConfigDict(from_attributes=True)

class UserEmail(BaseModel):
    email: EmailStr

class UserResetPassword(BaseModel):
    password: str
    token: str

class UserResetPasswordEmail(BaseModel):
    email: EmailStr

class UserResetPasswordToken(BaseModel):
    token: str

class UserResetPasswordTokenVerify(BaseModel):
    token: str
    email: EmailStr

class UserResetPasswordTokenVerifyResponse(BaseModel):
    message: str
    token: str
    email: EmailStr





class NotificationSettings(BaseModel):
    email_notification: bool = True
    sms_notification: bool = True
    push_notification: bool = True

    model_config = ConfigDict(from_attributes=True)

class NotificationSettingsUpdate(BaseModel):
    email_notification: Optional[bool] = None
    sms_notification: Optional[bool] = None
    push_notification: Optional[bool] = None
