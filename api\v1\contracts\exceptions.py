import logging
from api.core.logging_config import get_logger
from fastapi import HTTPException, status
logger = get_logger(__name__)



class ContractError(Exception):
    """Base exception for contract-related errors"""
    pass

class DataProcessingError(ContractError):
    """Exception raised for errors during data processing"""
    pass

class BlockchainError(ContractError):
    """Exception raised for blockchain interaction errors"""
    pass

class CacheError(ContractError):
    """Exception raised for caching-related errors"""
    pass




def handle_chart_error(error: Exception) -> None:
    """Handle errors in event processing"""
    #logger.error(f"Error processing events: {str(error)}")
    if isinstance(error, HTTPException):
        raise error
    elif isinstance(error, ValueError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(error)
        )
    elif isinstance(error, CacheError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service temporarily unavailable"
        )
    elif isinstance(error, BlockchainError):
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail="Failed to interact with blockchain"
        )
    elif isinstance(error, DataProcessingError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process contract data"
        )
    else:
        raise
