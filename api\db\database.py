import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import OperationalError, DatabaseError
from sqlalchemy import text
from config import config
from api.core.logging_config import get_logger
import tenacity

logger = get_logger(__name__)

# --- Create ASYNC DB URL ---
def create_async_db_url() -> str:
    """
    Ensure config.DATABASE_URL starts with 'postgresql+asyncpg://'
    """
    if not config.DATABASE_URL:
        logger.critical("Primary DATABASE_URL is not configured.")
        raise ValueError("Primary database URL is missing.")

    if not config.DATABASE_URL.startswith("postgresql+asyncpg://"):
        logger.warning("DATABASE_URL does not use asyncpg driver. Update config.")

    return config.DATABASE_URL

# --- Engine Arguments ---
def get_async_engine_args():
    return {
        "pool_size": config.DB_POOL_SIZE,
        "max_overflow": config.DB_MAX_OVERFLOW,
        "pool_timeout": config.DB_POOL_TIMEOUT,
        "pool_recycle": config.DB_POOL_RECYCLE,
        "pool_pre_ping": True,
        "connect_args": {
            "timeout": 60,
            "command_timeout": 60,
            "server_settings": {}
        }
    }

# --- Create Engine with Retry ---
@tenacity.retry(
    stop=tenacity.stop_after_attempt(config.DB_MAX_RETRIES),
    wait=tenacity.wait_exponential(multiplier=config.DB_RETRY_DELAY),
    retry=tenacity.retry_if_exception_type(OperationalError),
    before_sleep=lambda rs: logger.warning(
        f"DB connection attempt {rs.attempt_number} failed. Retrying..."
    )
)
def create_async_engine_with_retry():
    db_url = create_async_db_url()
    return create_async_engine(db_url, **get_async_engine_args())

try:
    async_db_engine = create_async_engine_with_retry()
    logger.info("Primary async DB engine created successfully.")
except Exception as e:
    logger.critical(f"Failed to initialize primary async DB engine: {str(e)}")
    async_db_engine = None



# --- Declarative Base ---
Base = declarative_base()

# --- Create Tables ---
async def create_database():
    if not async_db_engine:
        logger.error("Cannot create tables. DB engine not initialized.")
        return
    try:
        async with async_db_engine.begin() as conn:
            logger.info("Creating database tables...")
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Tables created successfully.")
    except OperationalError as e:
        logger.critical(f"Operational error during table creation: {str(e)}")
        raise
    except DatabaseError as e:
        logger.critical(f"Database error during table creation: {str(e)}")
        raise
    except Exception as e:
        logger.critical(f"Unexpected error during table creation: {str(e)}")
        raise

SessionLocal = sessionmaker(
    bind=async_db_engine,
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False,
)


# --- Dependency Injector for FastAPI ---
async def get_db():
    if not async_db_engine:
        raise RuntimeError("Database engine not available.")

    async with SessionLocal() as session:
        try:
            yield session
        except OperationalError as e:
            logger.error(f"Operational error: {str(e)}")
            await session.rollback()
            raise
        except DatabaseError as e:
            logger.error(f"Database error: {str(e)}")
            await session.rollback()
            raise
        except Exception as e:
            await session.rollback()
            raise
        finally:
            await session.close()
            logger.debug("Session closed.")