{"_format": "hh-sol-artifact-1", "contractName": "IAuthority", "sourceName": "contracts/access/manager/IAuthority.sol", "abi": [{"inputs": [{"internalType": "address", "name": "caller", "type": "address"}, {"internalType": "address", "name": "target", "type": "address"}, {"internalType": "bytes4", "name": "selector", "type": "bytes4"}], "name": "canCall", "outputs": [{"internalType": "bool", "name": "allowed", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}