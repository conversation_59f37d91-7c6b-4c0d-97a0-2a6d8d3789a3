from web3 import Web3, AsyncWeb3
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
from datetime import datetime
import time
import math
import asyncio
from config import config
from fastapi import HTTPException, status
from api.v1.deploy_contracts.models import ContractDeployment, TransactionType
from sqlalchemy.ext.asyncio import AsyncSession
import json
from eth_utils import event_abi_to_log_topic
from .schemas import EventModel, ContractMetrics
from web3.contract import Contract
from api.core.blockchain_dep import (Web3Provider, class_exponential_backoff,
                                     load_contract_artifacts)
from .exceptions import (logger, handle_chart_error, ContractError, DataProcessingError,
                         BlockchainError, CacheError)
from redis.asyncio import Redis
from sqlalchemy.future import select

PROVIDER = config.PROVIDER
PRODUCTION = config.PRODUCTION
PLATFORM_WALLET = config.PLATFORM_WALLET
CACHE_TIME = 600
CHUNCK_SIZE = 50
LOGS_CHUNK_SIZE = 10000
MAX_CONCURRENT_REQUESTS = 25  # Adjust based on API rate limits contract

class BlockEventBase:
    """Base class for blockchain event handling"""


    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                contract_address: str, earliest_block: int, contract_type: TransactionType,
                redis: Optional[Redis] = None):

        self.db = db
        self.user_id = user_id
        self.contract_id = contract_id
        self.redis = redis
        self.contract_address = contract_address
        self.earliest_block = earliest_block
        self.contract_type = contract_type
        self.cache = CACHE_TIME
        self.chunk_size = CHUNCK_SIZE
        self.web3: Optional[AsyncWeb3] = None
        self.contract_instance: Optional[Contract] =None
        self.events: Optional[List[EventModel]] = None
        self.abi: Optional[Any] = None
        self.bytecode: Optional[Any] = None



    @classmethod
    async def create(cls, db: AsyncSession, user_id: int, contract_id: int,
                     redis: Optional[Redis] = None):

        """Initialize contract details"""
        try:
            result = await db.execute(select(ContractDeployment.contract_address, 
                                             ContractDeployment.block_number, 
                                             ContractDeployment.contract_type).filter(
                ContractDeployment.user_id == user_id,
                ContractDeployment.id == contract_id
            ))
            contract_data = result.one_or_none()


            contract_address, earliest_block, contract_type = contract_data
            if not contract_address or earliest_block is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Contract ID {contract_id} has incomplete data (address/block)."
                )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Database query failed: {str(e)}")
            raise ContractError(f"Failed to initialize contract: {str(e)}")
    
        return cls(db, user_id, contract_id, contract_address, earliest_block, contract_type, redis)



    @classmethod
    async def _load_contract_artifacts(cls, contract_type: TransactionType) -> Tuple[Any, Any]:
        try:
            abi, bytecode = await load_contract_artifacts(contract_type)
            if not abi or not bytecode:
                raise ContractError("Invalid contract artifacts")
            return abi, bytecode
        except Exception as e:
            logger.error(f"Failed to load contract artifacts: {str(e)}")
            raise ContractError(f"Failed to load contract artifacts: {str(e)}")


    async def _init_web3(self) -> None:
        """Initialize Web3 instance and contract"""
        try:
            if self.web3 is None:
                self.web3 = await Web3Provider.get_instance()

            if self.contract_instance is None:
                if not self.abi:
                    raise BlockchainError("Contract ABI not loaded")
                self.contract_instance = self.web3.eth.contract(
                    abi=self.abi,
                    address=self.contract_address
                )
        except Exception as e:
            logger.error(f"Web3 initialization failed: {str(e)}")
            raise BlockchainError(f"Web3 initialization failed: {str(e)}")



    async def _get_blockchain_logs(self) -> Tuple[List[Dict]]:
        """
        Fetch logs from blockchain
        """
        try:
            start_block = self.earliest_block or (await self.web3.eth.get_block ('earliest'))['number']
            end_block = await self.web3.eth.get_block_number()
            if start_block > end_block:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Contract has no activity"
                )
            async def fetch_chunk(chunk_start: int, chunk_end: int) -> List[Any]:
                filter_params = {
                    'fromBlock': chunk_start,
                    'toBlock': chunk_end,
                    'address': self.contract_address
                }
                try:
                    return await asyncio.wait_for(
                        self.web3.eth.get_logs(filter_params),
                        timeout=60.0 # Timeout for a single get_logs call
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout fetching logs chunk {chunk_start}-{chunk_end}")
                    return []                
                except Exception as e:
                    logger.warning(f"Error fetching logs for blocks {chunk_start}-{chunk_end}: {str(e)}")
                    return []


            chunks = []
            for chunk_start in range(start_block, end_block + 1, LOGS_CHUNK_SIZE):
                chunk_end = min(chunk_start + LOGS_CHUNK_SIZE - 1, end_block)
                chunks.append((chunk_start, chunk_end))

            """
            async with asyncio.Semaphore(MAX_CONCURRENT_REQUESTS):
                all_logs = []
                for i in range(0, len(chunks), MAX_CONCURRENT_REQUESTS):
                    batch = chunks[i:i + MAX_CONCURRENT_REQUESTS]
                    batch_result = await asyncio.gather(
                        *[fetch_chunk(start, end) for start, end in batch],
                        return_exceptions=True
                    )

                    for result in batch_result:
                        if isinstance(result, list):
                            all_logs.extend(result)
            """
            
            fetch_semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
            all_raw_logs = []
            tasks = []

            async def fetch_with_semaphore(start, end):
                async with fetch_semaphore:
                    return await fetch_chunk(start, end)

            tasks = [fetch_with_semaphore(start, end) for start, end in chunks]
            #for start, end in chunks:
                #tasks.append(fetch_with_semaphore(start, end))

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Flatten results and filter out errors
            for result in results:
                if isinstance(result, list):
                    all_raw_logs.extend(result)
                elif isinstance(result, Exception):
                    logger.error(f"Exception during concurrent log fetching: {result}")

            if not all_raw_logs:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Contract has no activity"
                )
            return all_raw_logs

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to fetch blockchain data: {str(e)}")
            raise BlockchainError(f"Failed to fetch blockchain data: {str(e)}")



    def _handle_chart_error(self, error: Exception) -> None:
        """Handle errors in event processing"""
        handle_chart_error(error)

    def _handle_cache_error(self, error: Exception, cache_key: str) -> None:
        """Handle cache-related errors"""
        if isinstance(error, json.JSONDecodeError):
            logger.error(f"Cache data corruption detected: {str(error)}")
            asyncio.create_task(self.redis.delete(cache_key))
            raise CacheError("Corrupted cache data detected and cleared")
        raise BlockchainError(f"Failed to retrieve events data: {str(error)}")




class EventProcessor(BlockEventBase):
    """Handles event processing and caching"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 contract_address: str, earliest_block: int, contract_type: TransactionType,
                 redis: Optional[Redis] = None):
        try:
            super().__init__(
                db=db,
                user_id=user_id,
                contract_id=contract_id,
                contract_address=contract_address,
                earliest_block=earliest_block,
                contract_type=contract_type,
                redis=redis
            )
            self._event_signatures = {}
        except Exception as e:
            logger.error(f"Failed to initialize MetricsProcessor: {str(e)}")
            raise


    async def get_contract_events(self, order: str = "asc", page: int = 1, page_size: int = 10) -> Dict:
        """Get paginated contract events with caching"""
        start_time = time.time()
        try:
            if self.events is None:
                events_data = await self._get_cached_or_fetch_events()
                self.events = [EventModel(**event) for event in events_data]

            return self._paginate_events(order, page, page_size, start_time)
        except Exception as e:
            self._handle_chart_error(e)

    def _paginate_events(self, order: str, page: int, page_size: int, start_time: float) -> Dict:
        """Handle event pagination"""
        if page < 1:
            raise ValueError("Page number must be positive")

        total_events = len(self.events)
        total_pages = (total_events + page_size - 1) // page_size
        start_idx = (page - 1) * page_size
        end_idx = min(start_idx + page_size, total_events)

        if start_idx >= total_events:
            raise ValueError("Page number exceeds available pages")

        events = self.events[start_idx:end_idx]
        if order.lower() == "desc":
            events = events[::-1]

        logger.info(
            f"Contract events retrieved successfully. "
            f"Execution time: {time.time() - start_time:.2f} seconds"
        )

        return {
            "events": events,
            "pagination": self._create_pagination_info(page, total_events, total_pages, page_size)
        }


    @staticmethod
    def _create_pagination_info(page: int, total_events: int, total_pages: int, page_size: int) -> Dict:
        """Create pagination information dictionary"""
        return {
            "total_events": total_events,
            "total_pages": total_pages,
            "current_page": page,
            "page_size": page_size,
            "has_next": page < total_pages,
            "has_previous": page > 1
        }





    async def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache with error handling"""
        if not self.redis:
            return None
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Cache data corruption detected: {str(e)}")
            await self.redis.delete(cache_key)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval failed: {str(e)}")
            return None

    async def _cache_data(self, cache_key: str, data: Any, cache_time: int) -> None:
        """Cache data with error handling"""
        if not self.redis:
            return None
        try:
            await self.redis.set(cache_key, json.dumps(data))
            await self.redis.expire(cache_key, cache_time)
        except Exception as e:
            logger.warning(f"Failed to cache data: {str(e)}")




    async def _get_cached_or_fetch_events(self) -> List[Dict]:
        """Retrieve events from cache or blockchain"""
        cache_key = f"contract:{self.contract_address}:events"
        try:
            cached_events = await self._get_cached_data(cache_key)
            if cached_events:
                return cached_events

            return await self._fetch_and_cache_events(cache_key)
        except HTTPException:
            raise
        except Exception as e:
            self._handle_cache_error(e, cache_key)


    async def _fetch_and_cache_events(self, cache_key: str) -> List[Dict]:
        """Fetch events from blockchain and cache them"""
        self.abi, self.bytecode = await self._load_contract_artifacts(self.contract_type)
        await self._init_web3()

        events = await self._fetch_and_process_events()
        events_data = [event.model_dump() for event in events]
        await self._cache_data(cache_key, events_data, CACHE_TIME)

        return events_data


    async def _fetch_and_process_events(self) -> List[EventModel]:
        """Fetch and process contract events using semaphore for concurrency control"""
        try:
            logs = await self._get_blockchain_logs()
            self._event_signatures = self._build_event_signatures()

            events = []
            exceptions_count = 0
            semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)

            async def process_with_semaphore(log):
                nonlocal exceptions_count
                try:
                    async with semaphore:
                        result = await self._process_log(log)
                        if result is not None:
                            return result
                        else:
                            return None
                except Exception as e:
                    logger.warning(
                        f"Error processing log {log.transactionHash if log.transactionHash else log.blockNumber}: {e}"
                    )
                    exceptions_count += 1
                    return e
                
            results = await asyncio.gather(
                *[process_with_semaphore(log) for log in logs],
                return_exceptions=True
            )
            events = [
                result for result in results
                if result is not None and not isinstance(result, Exception)
            ]

            if exceptions_count > 0:
                logger.warning(
                    f"Log processing completed with {exceptions_count} failures "
                    f"out of {len(logs)} logs for contract {self.contract_id}."
                )

            return events
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to fetch and process events: {str(e)}")
            raise BlockchainError(f"Failed to fetch and process events: {str(e)}")


    def _build_event_signatures(self) -> Dict[str, str]:
        """Build event signatures mapping"""
        signatures = {}
        for entry in self.abi:
            if entry["type"] == "event":
                try:
                    signatures[entry["name"]] = event_abi_to_log_topic(entry)
                except Exception as e:
                    logger.warning(f"Error processing ABI for event {entry['name']}: {e}")
        return signatures


    async def _process_log(self, log: Dict) -> Optional[EventModel]:
        """Process individual log entry"""
        try:
            block, tx = await asyncio.gather(
                self.web3.eth.get_block(log['blockNumber']),
                self.web3.eth.get_transaction(log['transactionHash'])
            )

            event_data = await self._decode_event(log)
            if not event_data:
                return None

            method = await self._get_transaction_method(tx)

            timestamp = block['timestamp']
            formatted_timestamp = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            age = int(time.time() - timestamp)

            return EventModel(
                event=event_data["event"],
                method=method,
                age=self._format_age(age),
                timestamp=formatted_timestamp,
                args=event_data["args"],
                logIndex=log["logIndex"],
                blockNumber=log["blockNumber"],
                transactionHash=log["transactionHash"].hex(),
                topics=[topic.hex() for topic in log['topics']]
            )

        except Exception as e:
            logger.error(f"Error processing log: {e}")
            return None

    async def _get_transaction_method(self, tx: Dict) -> Optional[str]:
        """Get transaction method from input data"""
        try:
            method_selector = self.web3.to_hex(tx["input"])[:10][2:]

            for entry in self.abi:
                if entry["type"] == "function":
                    function_signature = f"{entry['name']}({','.join(i['type'] for i in entry['inputs'])})"
                    function_selector = Web3.keccak(text=function_signature)[:4].hex()

                    if method_selector == function_selector:
                        parameter_types = ",".join([input["type"] for input in entry["inputs"]])
                        return f"***{entry['name']}({parameter_types})"
            return None
        except Exception as e:
            logger.error(f"Error getting transaction method: {e}")
            return None

    async def _decode_event(self, log: Dict) -> Optional[Dict]:
        """Decode event data from log"""
        try:
            for event_name, event_signature in self._event_signatures.items():
                log_topic_hex = self.web3.to_hex(event_signature)[2:]

                if log["topics"][0].hex() == log_topic_hex:
                    event_processor = getattr(self.contract_instance.events, event_name, None)
                    if event_processor:
                        decoded_event = event_processor().process_log(log)
                        return {
                            "event": event_name,
                            "args": decoded_event["args"]
                        }
            return None
        except Exception as e:
            logger.error(f"Error decoding event: {e}")
            return None

    @staticmethod
    def _format_age(age_in_seconds: int) -> str:
        """Format age in a human-readable format"""
        if age_in_seconds < 60:
            return f"{age_in_seconds} secs ago"
        elif age_in_seconds < 3600:
            return f"{age_in_seconds // 60} mins ago"
        elif age_in_seconds < 86400:
            return f"{age_in_seconds // 3600} hrs ago"
        else:
            return f"{age_in_seconds // 86400} days ago"




class MetricsProcessor(BlockEventBase):
    """Handles contract metrics processing"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 contract_address: str, earliest_block: int, contract_type: TransactionType,
                 redis: Optional[Redis] = None):
        try:
            super().__init__(
                db=db,
                user_id=user_id,
                contract_id=contract_id,
                contract_address=contract_address,
                earliest_block=earliest_block,
                contract_type=contract_type,
                redis=redis
            )

        except Exception as e:
            logger.error(f"Failed to initialize MetricsProcessor: {str(e)}")
            raise


    async def get_contract_metrics(self) -> ContractMetrics:
        """Get contract metrics with caching"""
        start_time = time.time()
        cache_key = f"advanced_metrics:{self.contract_address}"
        try:
            if self.redis:
                cached = await self.redis.get(cache_key)
                if cached:
                    try:
                        return ContractMetrics(**json.loads(cached))
                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse cached metrics: {str(e)}")
                        #self._handle_cache_error(e, cache_key)
            metrics = await self._calculate_metrics()
            if self.redis:
                try:
                    metrics_dict = metrics.model_dump(mode='json')
                    await self.redis.set(cache_key, json.dumps(metrics_dict))
                    #await self.redis.set(cache_key, json.dumps(metrics.__dict__))
                    await self.redis.expire(cache_key, CACHE_TIME)
                except Exception as e:
                    logger.warning(f"Failed to cache events: {str(e)}")

            logger.info(f"Metrics calculation time: {time.time() - start_time:.2f} seconds")
            return metrics

        except Exception as e:
            self._handle_chart_error(e)


    async def _calculate_metrics(self) -> ContractMetrics:
        """Calculate contract metrics"""
        try:
            self.abi, self.bytecode = await self._load_contract_artifacts(self.contract_type)
            await self._init_web3()

            end_block = await self.web3.eth.get_block_number()
            start_block = self.earliest_block or (await self.web3.eth.get_block('earliest'))['number']

            holders, tx_data = await asyncio.gather(
                self._get_holders(start_block, end_block),
                self._get_transaction_data(start_block, end_block)
            )

            return await self._process_metrics(tx_data, holders)

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Metrics calculation failed: {str(e)}")
            raise DataProcessingError(f"Failed to calculate metrics: {str(e)}")

    async def _get_holders(self, start_block: int, end_block: int) -> set:
        """Get unique token holders"""
        try:
            transfer_filter = await self.contract_instance.events.Transfer.create_filter(
                from_block=start_block,
                to_block=end_block
            )
            events = await transfer_filter.get_all_entries()

            holders = set()
            zero_address = "******************************************"

            for event in events:
                try:
                    sender = event['args']['from']
                    receiver = event['args']['to']
                    if sender != zero_address:
                        holders.add(sender)
                    if receiver != zero_address:
                        holders.add(receiver)
                except KeyError as e:
                    logger.warning(f"Malformed event data: {str(e)}")
                    continue

            return holders

        except Exception as e:
            logger.error(f"Failed to fetch holders: {str(e)}")
            raise BlockchainError(f"Failed to fetch holders: {str(e)}")

    async def _get_transaction_data(self, start_block: int, end_block: int) -> List[Tuple]:
        """Get transaction data for the contract"""
        try:
            logs = await self._get_blockchain_logs()

            semaphore = asyncio.Semaphore(5)
            async def process_log(log):
                try:
                    async with semaphore:
                        block, tx, receipt = await asyncio.gather(
                            self.web3.eth.get_block(log.blockNumber),
                            self.web3.eth.get_transaction(log.transactionHash),
                            self.web3.eth.get_transaction_receipt(log.transactionHash),
                        )
                        return receipt, tx, block
                except Exception as e:
                    logger.warning(f"Failed to process log: {str(e)}")
                    return None

            results = await asyncio.gather(
                *[process_log(log) for log in logs],
                return_exceptions=True
            )
            valid_results = [
                result for result in results
                if result and not isinstance(result, Exception)
            ]
            if not valid_results:
                raise BlockchainError("Failed to process any transaction data")

            return valid_results

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to fetch transaction data: {str(e)}")
            raise BlockchainError(f"Failed to fetch transaction data: {str(e)}")

    async def _process_metrics(self, tx_data: List[Tuple], holders: set) -> ContractMetrics:
        """Process transaction data and calculate metrics"""
        start_time = time.time()

        try:
            transactions = set()
            total_gas_spent = 0
            current_time = datetime.now()
            start_of_month = datetime(current_time.year, current_time.month, 1)
            start_of_month_block = None

            for receipt, tx, block in tx_data:
                try:
                    transactions.add(tx['hash'])
                    gas_fee = receipt.gasUsed * tx.gasPrice
                    total_gas_spent += float(self.web3.from_wei(gas_fee, 'ether'))

                    block_time = datetime.fromtimestamp(block['timestamp'])
                    if block_time >= start_of_month and start_of_month_block is None:
                        start_of_month_block = block['number']
                except Exception as e:
                    logger.warning(f"Error processing transaction: {str(e)}")
                    continue

            total_supply, month_supply = await self._get_supply_metrics(start_of_month_block)
            growth_rate = self._calculate_growth_rate(total_supply, month_supply)
            per_holder_metrics = self._calculate_per_holder_metrics(total_supply, len(holders))

            logger.info(
                f"Contract events retrieved successfully. "
                f"Execution time: {time.time() - start_time:.2f} seconds"
            )
            return ContractMetrics(
                total_net_worth=total_supply,
                total_transactions=len(transactions),
                total_gas_spent=total_gas_spent,
                average_holding=per_holder_metrics['average_holding'],
                per_user_value=per_holder_metrics['per_user_value'],
                monthly_growth_rate=growth_rate
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to process metrics: {str(e)}")
            raise DataProcessingError(f"Failed to process metrics: {str(e)}")

    async def _get_supply_metrics(self, start_of_month_block: Optional[int]) -> Tuple[float, float]:
        """Get total supply metrics"""
        try:
            if start_of_month_block is not None:
                total_supply, month_supply = await asyncio.gather(
                    self.contract_instance.functions.totalSupply().call(),
                    self.contract_instance.functions.totalSupply().call(
                        block_identifier=start_of_month_block
                    )
                )
            else:
                total_supply = await self.contract_instance.functions.totalSupply().call()
                month_supply = 0

            # Convert to ETH if needed
            if self.contract_type == TransactionType.ERC721:
                return float(total_supply), float(month_supply)
            else:
                return (
                    float(self.web3.from_wei(total_supply, 'ether')),
                    float(self.web3.from_wei(month_supply, 'ether'))
                )

        except Exception as e:
            logger.error(f"Failed to get supply metrics: {str(e)}")
            raise BlockchainError(f"Failed to get supply metrics: {str(e)}")

    def _calculate_growth_rate(self, total_supply: float, month_supply: float) -> float:
        """Calculate monthly growth rate"""
        try:
            if month_supply > 0:
                return ((total_supply - month_supply) / month_supply) * 100
            return 0
        except Exception as e:
            logger.error(f"Failed to calculate growth rate: {str(e)}")
            raise DataProcessingError(f"Failed to calculate growth rate: {str(e)}")

    def _calculate_per_holder_metrics(self, total_supply: float, num_holders: int) -> Dict[str, float]:
        """Calculate per-holder metrics"""
        try:
            if num_holders == 0:
                return {
                    'average_holding': 0,
                    'per_user_value': 0
                }

            per_holder = total_supply / num_holders
            return {
                'average_holding': per_holder,
                'per_user_value': per_holder
            }
        except Exception as e:
            logger.error(f"Failed to calculate per-holder metrics: {str(e)}")
            raise DataProcessingError(f"Failed to calculate per-holder metrics: {str(e)}")


@class_exponential_backoff()
class BlockEvents(EventProcessor, MetricsProcessor):
    """Main class combining event and metrics processing capabilities"""

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass