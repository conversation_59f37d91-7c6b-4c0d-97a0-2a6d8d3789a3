from pymongo import MongoClient
from decouple import config

MONGO_URI = config('MONGO_URI', default="mongodb://localhost:27017/")
DB_NAME = config('MONGO_DB_NAME', default="atlas")

# Create MongoDB client
client = MongoClient(MONGO_URI)
db = client[DB_NAME]

def get_mongo_db():
    """
    Get MongoDB database instance
    """
    try:
        # Ping the server to check the connection
        client.admin.command('ping')
        return db
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        raise

def close_mongo_connection():
    """
    Close MongoDB connection
    """
    try:
        client.close()
    except Exception as e:
        print(f"Error closing MongoDB connection: {e}")
        raise 