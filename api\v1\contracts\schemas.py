from pydantic import BaseModel, Field, field_validator, ConfigDict
from api.v1.user.models import User
from enum import Enum
from datetime import datetime
from typing import List, Dict, Any, Optional
from web3.datastructures import AttributeDict


class Order(str, Enum):
    DESC = "desc"
    ASC = "asc"


class TimeData(str, Enum):
    DAY = "D"        # Day
    WEEK = "W"       # Week
    MONTH = "ME"      # Month
    SIX_MONTH = "6ME" # 6 Months
    YEAR = "YE"       # Year



class ContractDeployment(BaseModel):
    contractAddress: str = Field(..., alias="contractAddress")
    createdAt: datetime = Field(..., alias="createdAt")

class ContractAddresses(BaseModel):
    contract_addresses: List[ContractDeployment]


class ContractMetrics(BaseModel):
    """Data class for contract metrics"""
    total_net_worth: float
    total_transactions: int
    total_gas_spent: float
    average_holding: float
    per_user_value: float
    monthly_growth_rate: float


class ProcessedLogData(BaseModel):
    """Structure for processed log data"""
    timestamp: str
    from_address: str
    transaction_hash: Optional[str] = None
    transaction_type: Optional[int] = None
    block_number: Optional[int] = None

class EventModel(BaseModel):
    event: Optional[str] = None
    method: Optional[str] = None
    age: Optional[str] = None
    timestamp: Optional[str] = None
    args: Optional[Dict[str, Any]] = None
    logIndex: Optional[int] = None
    blockNumber: Optional[int] = None
    transactionHash: Optional[str] = None
    topics: Optional[List[str]] = None

    @field_validator('args', mode='before')
    def convert_attibute_to_dict(cls, v):
        if isinstance(v, AttributeDict):
            return dict(v)
        return v

    model_config = ConfigDict(arbitrary_types_allowed=True)



class HistoryModel(BaseModel):
    timestamp: Optional[str] = None
    transactionHash: Optional[str] = None

    @field_validator("transactionHash", mode='before')
    def hexify_transactionHash(cls, v):
        if hasattr(v, "hex"):
            return v.hex()
        return v

    model_config = ConfigDict(arbitrary_types_allowed=True)



class LogModel(BaseModel):
    address: Optional[str] = None
    blockHash: Optional[str] = None
    blockNumber: Optional[int] = None
    data: Optional[str] = None
    logIndex: Optional[int] = None
    removed: Optional[bool] = None
    topics: Optional[List[str]] = None
    transactionHash: Optional[str] = None
    transactionIndex: Optional[int] = None

    # Convert the blockHash to a hex string if needed.
    @field_validator("blockHash", mode='before')
    def hexify_blockHash(cls, v):
        if hasattr(v, "hex"):
            return v.hex()
        return v

    # Convert the data field to a hex string if needed.
    @field_validator("data", mode='before')
    def hexify_data(cls, v):
        if hasattr(v, "hex"):
            return v.hex()
        return v

    # Convert each topic to a hex string if needed.
    #@validator("topics", pre=True)
    #def hexify_topics(cls, v):
        #return [item.hex() if hasattr(item, "hex") else item for item in v]

    # Convert the transactionHash to a hex string if needed.
    @field_validator("transactionHash", mode='before')
    def hexify_transactionHash(cls, v):
        if hasattr(v, "hex"):
            return v.hex()
        return v

    model_config = ConfigDict(arbitrary_types_allowed=True)