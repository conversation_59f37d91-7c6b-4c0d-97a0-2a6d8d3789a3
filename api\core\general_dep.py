from fastapi import Depends, HTTPException, status, UploadFile, File
import os
import uuid
import logging
from api.core.logging_config import get_logger
from typing import <PERSON>ple, List
from redis.asyncio import Redis
from typing import Optional, AsyncGenerator
import asyncio
from config import config
import aiofiles
from pathlib import Path

ALLOWED_EXTENSIONS = {".png", ".jpg", ".jpeg", ".gif", ".webp"}
MAX_FILE_SIZE = 5 * 1024 * 1024  # 5
#MAX_FILE_SIZE = 1_000_000  # 1 MB
CHUNK_SIZE = 4096 # 4KB chunks for streaming

logger = get_logger(__name__)

# Constants
#ALLOWED_EXTENSIONS = [".jpg", ".jpeg", ".png", ".gif"]



class FileUploadError(Exception): pass
class InvalidFileTypeError(FileUploadError): pass
class FileSizeExceededError(FileUploadError): pass
class FileOperationError(FileUploadError): pass

# Make the function async
async def upload_image(file: UploadFile, folder: str) -> Tuple[str, str]:
    """
    Asynchronously upload an image file via streaming to the specified folder.
    Raises specific custom exceptions on failure.
    """
    if not file or not file.filename:
        raise InvalidFileTypeError("No file or filename provided.")

    folder_path = Path(folder) 
    try:
        folder_exists = await asyncio.to_thread(folder_path.exists)
        if not folder_exists:
            await asyncio.to_thread(folder_path.mkdir, parents=True, exist_ok=True)
            logger.info(f"Created directory: {folder_path}")

        # Option 2: EAFP (Easier to Ask Forgiveness than Permission) - Often preferred in async
        # await asyncio.to_thread(folder_path.mkdir, parents=True, exist_ok=True) # Attempt creation
        # This avoids the os.path.exists check but relies on catching errors during write if needed.
        # However, mkdir itself handles exist_ok=True well.

    except OSError as e:
        logger.error(f"Failed to create directory {folder_path}: {e}")
        raise FileOperationError(f"Failed to create upload directory: {e}") from e

    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in ALLOWED_EXTENSIONS:
        raise InvalidFileTypeError(f"File type '{file_extension}' not allowed. Allowed: {', '.join(ALLOWED_EXTENSIONS)}")

    if hasattr(file, 'size') and file.size is not None:
        if file.size > MAX_FILE_SIZE:
             raise FileSizeExceededError(f"File size ({file.size} bytes) exceeds limit of {MAX_FILE_SIZE} bytes.")
        size_checked_early = True
    else:
        size_checked_early = False
        logger.warning("Content-Length header missing or invalid; file size will be checked during upload.")


    unique_filename = f"{uuid.uuid4()}{file_extension}"
    full_file_path = folder_path / unique_filename
    bytes_written = 0

    try:
        async with aiofiles.open(full_file_path, "wb") as buffer:
            while True:
                chunk = await file.read(CHUNK_SIZE)
                if not chunk:
                    break

                bytes_written += len(chunk)

                # Check size during stream if not checked earlier
                if not size_checked_early and bytes_written > MAX_FILE_SIZE:
                    try:
                         await buffer.close()
                         await asyncio.to_thread(os.remove, full_file_path)
                         logger.warning(f"Removed partially written large file: {full_file_path}")
                    except Exception as cleanup_err:
                         logger.error(f"Error removing partially written large file {full_file_path}: {cleanup_err}")
                    raise FileSizeExceededError(f"File size exceeds limit of {MAX_FILE_SIZE} bytes (checked during upload).")

                await buffer.write(chunk)

        if not size_checked_early and bytes_written > MAX_FILE_SIZE:
             raise FileSizeExceededError(f"File size ({bytes_written} bytes) exceeds limit of {MAX_FILE_SIZE} bytes.")


        logger.info(f"File saved successfully via streaming: {full_file_path} ({bytes_written} bytes)")
        try:
             await file.seek(0)
        except Exception:
             logger.warning(f"Could not seek(0) on uploaded file stream for {file.filename}")


        return unique_filename, str(full_file_path)

    except FileSizeExceededError: 
        raise
    except IOError as e:
        logger.error(f"Failed to write file to disk {full_file_path}: {e}")
        raise FileOperationError(f"Failed to save file to disk: {e}") from e
    except Exception as e:
        logger.exception(f"Unexpected error during file upload stream for {file.filename}: {e}")
        if full_file_path.exists():
             try:
                 await asyncio.to_thread(os.remove, full_file_path)
                 logger.warning(f"Removed file due to unexpected error during upload: {full_file_path}")
             except Exception as cleanup_err:
                 logger.error(f"Error removing file after unexpected upload error {full_file_path}: {cleanup_err}")
        raise FileUploadError(f"An unexpected error occurred during file upload: {e}") from e


import asyncio
import os
import uuid
import logging
from pathlib import Path
from typing import Tuple
from fastapi import UploadFile # Assuming you're using FastAPI's UploadFile

# --- Azure SDK Imports ---
from azure.storage.blob.aio import BlobServiceClient, BlobClient
from azure.core.exceptions import AzureError
# --- End Azure SDK Imports ---

# --- Your Custom Exceptions (ensure these are defined elsewhere) ---
class InvalidFileTypeError(ValueError): pass
class FileSizeExceededError(ValueError): pass
class FileOperationError(IOError): pass
class FileUploadError(Exception): pass
# --- End Custom Exceptions ---


logger = logging.getLogger(__name__) # Configure your logger as needed

# --- Configuration (Ideally from environment variables or config file) ---
# Set these environment variables in your deployment environment
AZURE_STORAGE_CONNECTION_STRING = os.environ.get("AZURE_STORAGE_CONNECTION_STRING")
AZURE_CONTAINER_NAME = os.environ.get("AZURE_CONTAINER_NAME")
# --- End Configuration ---

# --- Constants (Keep these) ---
ALLOWED_EXTENSIONS = {".png", ".jpg", ".jpeg", ".gif", ".webp"}
MAX_FILE_SIZE = 5 * 1024 * 1024  # Example: 5 MB
CHUNK_SIZE = 4 * 1024 * 1024 # Example: 4MB chunks for reading (can be adjusted)
# --- End Constants ---


async def upload_image_to_azure(file: UploadFile, folder: str) -> Tuple[str, str]:
    """
    Asynchronously uploads an image file via streaming directly to Azure Blob Storage.

    Args:
        file: The file uploaded via FastAPI (or similar async framework).
        folder: The virtual folder path within the Azure container.

    Returns:
        A tuple containing the unique blob name (including folder path)
        and the full URL of the uploaded blob.

    Raises:
        InvalidFileTypeError: If the file type is not allowed or no file provided.
        FileSizeExceededError: If the file size exceeds the MAX_FILE_SIZE limit.
        FileUploadError: For general Azure connection or upload errors.
        ValueError: If Azure configuration (connection string, container name) is missing.
    """
    if not AZURE_STORAGE_CONNECTION_STRING or not AZURE_CONTAINER_NAME:
        logger.error("Azure Storage configuration (connection string or container name) is missing.")
        raise ValueError("Server configuration error: Azure Storage details not set.")

    if not file or not file.filename:
        raise InvalidFileTypeError("No file or filename provided.")

    # --- File Validation (Similar to original) ---
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in ALLOWED_EXTENSIONS:
        raise InvalidFileTypeError(f"File type '{file_extension}' not allowed. Allowed: {', '.join(ALLOWED_EXTENSIONS)}")

    # Check size early if possible (Content-Length header)
    if hasattr(file, 'size') and file.size is not None:
        if file.size > MAX_FILE_SIZE:
            raise FileSizeExceededError(f"File size ({file.size} bytes) exceeds limit of {MAX_FILE_SIZE} bytes (reported by client).")
        size_checked_early = True
        logger.debug(f"File size reported by client: {file.size} bytes.")
    else:
        size_checked_early = False
        logger.warning("Content-Length header missing or invalid; file size will be checked during upload.")
    # --- End File Validation ---

    # --- Generate unique blob name ---
    # Use folder as a prefix in the blob name for virtual directory structure
    unique_suffix = f"{uuid.uuid4()}{file_extension}"
    # Ensure folder doesn't start/end with / and join properly
    clean_folder = folder.strip('/')
    blob_name = f"{clean_folder}/{unique_suffix}" if clean_folder else unique_suffix
    # --- End Generate blob name ---

    blob_url = ""
    bytes_uploaded = 0

    try:
        # --- Connect to Azure Blob Storage ---
        # NOTE: For high-throughput applications, consider creating the BlobServiceClient
        # once at application startup instead of per-request for better performance/connection reuse.
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        async with blob_service_client: # Ensures client is properly closed
            blob_client: BlobClient = blob_service_client.get_blob_client(
                container=AZURE_CONTAINER_NAME,
                blob=blob_name
            )
            blob_url = blob_client.url # Get the URL before potential upload failure

            logger.info(f"Attempting to upload to Azure Blob: container='{AZURE_CONTAINER_NAME}', blob='{blob_name}'")

            # --- Stream Upload to Azure ---
            # The SDK's upload_blob can often handle the stream directly.
            # We pass the file object itself.
            # We add length_limit if size check wasn't done early.

            # Option 1: Stream directly if size check passed or not possible early
            # if size_checked_early or not MAX_FILE_SIZE: # Allow upload if size ok or no limit
            #     await blob_client.upload_blob(file, overwrite=True)
            #     # If size wasn't checked early, we have to trust Azure limits or lack thereof
            #     # We can't easily get the final size here without another call
            #     bytes_uploaded = file.size if hasattr(file, 'size') and file.size else -1 # Indicate unknown if size wasn't available
            # else:
            # Option 2: Read in chunks to enforce size limit during upload (more robust if Content-Length is unreliable)
            # This requires reading the file manually again, similar to the original file write logic.

            # Let's refine Option 2 - more closely match original intent for size check during stream
            try:
                # Reset stream position just in case it was read partially before
                await file.seek(0)
            except Exception:
                logger.warning(f"Could not seek(0) on uploaded file stream for {file.filename} before Azure upload. Proceeding anyway.")

            bytes_read = 0
            stream = b'' # Accumulate initial chunk if needed, or use advanced streaming upload
            async for chunk in file.chunks(CHUNK_SIZE): # Use chunks method if available (FastAPI UploadFile often has it) or fallback
                 if not chunk:
                     break # Should not happen with async for, but good practice

                 bytes_read += len(chunk)

                 if not size_checked_early and bytes_read > MAX_FILE_SIZE:
                     # Abort: We don't need manual cleanup like local files, Azure handles incomplete uploads.
                      raise FileSizeExceededError(f"File size exceeds limit of {MAX_FILE_SIZE} bytes (checked during upload).")

                 # Note: For very large files (>~256MB), look into blob_client.stage_block and blob_client.commit_block_list
                 # For simpler cases up to typical web limits, upload_blob is often sufficient.
                 # However, upload_blob expects the full data source. Let's read it fully if size ok.
                 # This is a trade-off: simpler SDK call vs memory usage for large files.

            # After checking the size by reading, reset and upload if OK.
            if not size_checked_early and bytes_read > MAX_FILE_SIZE:
                 # This condition should have been caught inside the loop, but double-check
                 raise FileSizeExceededError(f"File size ({bytes_read} bytes) exceeds limit of {MAX_FILE_SIZE} bytes (checked during upload).")

            logger.debug(f"File size checked during stream: {bytes_read} bytes. Proceeding with upload.")
            bytes_uploaded = bytes_read

            # Reset stream position *again* before the actual upload call
            await file.seek(0)

            # Perform the actual upload
            # Pass length=bytes_uploaded for optimization if known
            await blob_client.upload_blob(file, overwrite=True, length=bytes_uploaded if bytes_uploaded > 0 else None)

        logger.info(f"File uploaded successfully to Azure Blob: {blob_url} ({bytes_uploaded} bytes)")

        # Return the blob name (with folder) and its full URL
        return blob_name, blob_url

    except FileSizeExceededError:
        logger.warning(f"Upload aborted for {file.filename} due to size limit ({MAX_FILE_SIZE} bytes).")
        # Attempt to delete if partially staged? Generally not needed with upload_blob.
        raise # Re-raise the specific error

    except AzureError as e:
        logger.error(f"Azure Blob Storage error during upload for {file.filename}: {e}")
        # You might want more specific Azure error handling here
        raise FileUploadError(f"Failed to upload file to cloud storage: {e}") from e

    except Exception as e:
        # Catch any other unexpected errors during the process
        logger.exception(f"Unexpected error during Azure file upload for {file.filename}: {e}")
        # Attempt to clean up the blob if an unexpected error occurred mid-upload?
        # Generally, Azure handles incomplete standard uploads well, but for production,
        # you might add blob deletion logic here if necessary, using the blob_client.
        # Example (needs blob_client defined outside try/except or careful handling):
        # try:
        #     async with blob_service_client:
        #         blob_client_cleanup = blob_service_client.get_blob_client(AZURE_CONTAINER_NAME, blob_name)
        #         await blob_client_cleanup.delete_blob(delete_snapshots="include")
        #         logger.warning(f"Deleted potentially incomplete blob {blob_name} due to error.")
        # except Exception as cleanup_err:
        #      logger.error(f"Failed to delete blob {blob_name} after error: {cleanup_err}")
        raise FileUploadError(f"An unexpected error occurred during file upload: {e}") from e

    finally:
         # Ensure the underlying file stream is closed if FastAPI doesn't do it automatically
         await file.close()


"""
def upload_image(file: UploadFile, folder: str) -> Tuple[str, str]:
    
    #Upload an image file to the specified folder with proper error handling.
    
    try:
        if not file:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No file provided"
            )
            
        if not os.path.exists(folder):
            try:
                os.makedirs(folder, exist_ok=True)
                logger.info(f"Created directory: {folder}")
            except OSError as e:
                logger.error(f"Failed to create directory {folder}: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to create upload directory"
                )
        
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                detail=f"File type not allowed. Allowed types: {', '.join(ALLOWED_EXTENSIONS)}"
            )
        
        try:
            file_content = file.file.read()
            file_size = len(file_content)
        except Exception as e:
            logger.error(f"Failed to read file: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to read uploaded file"
            )
            
        if file_size > MAX_FILE_SIZE:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File size too large. Maximum size allowed is {MAX_FILE_SIZE/1_000_000} MB"
            )
            
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        full_file_path = os.path.join(folder, unique_filename)
        
        try:
            with open(full_file_path, "wb") as buffer:
                buffer.write(file_content)
            logger.info(f"File saved successfully: {full_file_path}")
        except IOError as e:
            logger.error(f"Failed to write file to disk: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to save file to disk"
            )
            
        file.file.seek(0)
        
        return unique_filename, full_file_path
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in upload_image: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during file upload"
        )
"""



class RedisManager:
    _instance: Optional[Redis] = None
    _lock = asyncio.Lock()

    @classmethod
    async def get_client(cls) -> Optional[Redis]:
        if not cls._instance:
            async with cls._lock:
                if not cls._instance:
                    try:                            
                        cls._instance = Redis(
                            host=config.REDIS_HOST,
                            port=config.REDIS_PORT,
                            password=config.REDIS_PASSWORD,
                            decode_responses=True,
                            encoding="utf-8",
                            socket_timeout=5,
                            socket_connect_timeout=5,
                            socket_keepalive=True,
                            health_check_interval=30,
                            retry_on_timeout=True,
                            max_connections=10
                        )
                        await cls._instance.ping()
                        logger.info("Successfully connected to Redis")

                    except TimeoutError:
                        logger.error("Redis connection timeout")
                        """raise HTTPException(
                            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
                            detail="Cache service unavailable"
                        )"""
                        return None

                    except Exception as e:
                        logger.error("Error connecting to Redis: {e}")
                        """raise HTTPException(
                            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                            detail="Cache service unavailable"
                        )"""
                        return None

        return cls._instance

    @classmethod
    async def close(cls):
        if cls._instance:
            await cls._instance.close()
            cls._instance = None


async def get_redis() -> AsyncGenerator[Redis | None, None]:

    """FastAPI dependency for Redis"""
    redis = await RedisManager.get_client()
    yield redis




class AsyncTaskManager:
    def __init__(self):
        self._tasks = set()

    async def create_task(self, coro):
        task = asyncio.create_task(coro)
        self._tasks.add(task)
        task.add_done_callback(self._tasks.discard)
        return task

    async def wait_all(self):
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
        self._tasks.clear()