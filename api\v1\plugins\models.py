from sqlalchemy import <PERSON><PERSON><PERSON>, Column, ForeignKey, Text, Integer, String, DateTime, Float, Enum
from sqlalchemy.orm import relationship
from datetime import datetime, date, timezone
from api.db.database import Base
import enum

class RWAType(enum.Enum):
    REAL_ESTATE = "real_estate"
    ARTWORK = "artwork"
    COMMODITY = "commodity"
    INTELLECTUAL_PROPERTY = "intellectual_property"
    VEHICLE = "vehicle"
    OTHER = "other"


class TokenStandard(enum.Enum):
    ERC_721 = "ERC-721"
    ERC_1155 = "ERC-1155"
    ERC_20 = "ERC-20"


class RWAStatus(enum.Enum):
    DRAFT = "draft"
    PENDING_VERIFICATION = "pending_verification"
    VERIFIED = "verified"
    TOKENIZED = "tokenized"
    LISTED = "listed"
    SOLD = "sold"

class RealWorldAsset(Base):
    __tablename__ = "real_world_assets"

    id = Column(Integer, primary_key=True, index=True)

    # Asset Basic Information
    name = Column(String(255), nullable=False)
    description = Column(String(1000), nullable=True)
    asset_type = Column(Enum(RWAType), nullable=False)

    # Ownership and Valuation
    owner_id = Column(Integer, ForeignKey('user.id'), nullable=False)
    initial_value = Column(Float, nullable=False)
    current_value = Column(Float, nullable=True)

    # Blockchain Specific Details
    blockchain_contract_address = Column(String(255), nullable=True)
    token_standard = Column(Enum(TokenStandard), nullable=True)  # e.g., ERC-721, ERC-1155

    # Asset Verification and Status
    status = Column(Enum(RWAStatus), default=RWAStatus.DRAFT)
    verification_documents = Column(String(500), nullable=True)

    # Tokenization Details
    total_tokens = Column(Integer, nullable=True)
    tokens_available = Column(Integer, nullable=True)
    token_price = Column(Float, nullable=True)

    # Metadata and Tracking
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    #owner = relationship("User", back_populates="real_world_assets")
    document_verifications = relationship("AssetVerification", back_populates="asset")

class AssetVerification(Base):
    __tablename__ = "asset_verifications"

    id = Column(Integer, primary_key=True, index=True)
    asset_id = Column(Integer, ForeignKey('real_world_assets.id'), nullable=False)
    verifier_id = Column(Integer, ForeignKey('user.id'), nullable=False)

    verification_type = Column(String(100), nullable=False)  # e.g., 'legal', 'property_deed', 'appraisal'
    verification_document_url = Column(String(500), nullable=False)
    verification_status = Column(String(50), default='pending')

    verified_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    asset = relationship("RealWorldAsset", back_populates="document_verifications")
    #verifier = relationship("User")