from fastapi import Depends, <PERSON>ie, HTT<PERSON><PERSON>xception, APIRouter, Depends, status, Response, Request, BackgroundTasks
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import timed<PERSON><PERSON>
from typing import Union
#from decouple import config
from api.v1.user import schemas as user_schema
from api.v1.auth import schemas as auth_schema
from api.db.database import get_db
from api.v1.auth.services import Auth, APIkey #, schedule_subscription_task
from api.v1.user.services import UserService
from api.v1.user.models import User as UserModel
from api.core import responses
from api.core.dependencies import is_authenticated, not_authenticated
import requests
from config import config
from .exceptions import handle_auth_notification
from fastapi import Form
from .exceptions import handle_auth_error, GoogleAuthError
from .services_email import EmailVerification
#ACCESS_TOKEN_EXPIRE_MINUTES = int(config('ACCESS_TOKEN_EXPIRE_MINUTES'))
#JWT_REFRESH_EXPIRY = int(config('JWT_REFRESH_EXPIRY'))
#IS_REFRESH_TOKEN_SECURE = True if config('PYTHON_ENV') == "production" else False

ACCESS_TOKEN_EXPIRE_MINUTES = config.ACCESS_TOKEN_EXPIRE_MINUTES
JWT_REFRESH_EXPIRY = config.JWT_REFRESH_EXPIRY
IS_REFRESH_TOKEN_SECURE = True if config.PYTHON_ENV == "production" else False


GOOGLE_CLIENT_ID=config.GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=config.GOOGLE_CLIENT_SECRET
GOOGLE_REDIRECT_URI=config.GOOGLE_REDIRECT_URI


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

app = APIRouter(tags=["Auth"])





@app.get("/")
async def root(request: Request):
    return {"message": "Hello World"}



@app.post("/auth/signup", status_code=status.HTTP_201_CREATED)
async def signup(
    user:user_schema.CreateUser,
    db:AsyncSession = Depends(get_db)
):
    """
    Endpoint to create a user
    Returns: Created User.
    """
    try:
        userService = UserService()
        #created_user = userService.create(user=user, db=db)
        #await handle_auth_notification(db=db, user_id=created_user.id, action="signup")
        return await userService.create(user=user, db=db)
        
    except Exception as e:
        handle_auth_error(e)




@app.post("/auth/verify", status_code=status.HTTP_201_CREATED)
async def verify(
    user: user_schema.CreateUser = Depends(not_authenticated),
    db:AsyncSession = Depends(get_db)
):
    """
    Endpoint to verify a user
    Returns: Verified User, If not verified on signup.
    """
    try:
        userService = UserService()
        return await userService.email_verification(user=user, db=db)
    except Exception as e:
        handle_auth_error(e)



@app.post("/auth/verify/confirm")
async def confirm_verification(background_tasks: BackgroundTasks, 
                               verification_data: user_schema.EmailVerificationRequest, 
                               db: AsyncSession = Depends(get_db)):
    try:
        userService = UserService()
        verified = await userService.verify_email(
            email=verification_data.email,
            verification_code=verification_data.verification_code,
            db=db
        )
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = await Auth.create_access_token(
            data={"id": verified.id}, db=db, expires_delta=access_token_expires
        )

        #schedule_subscription_task(user_id=verified.id)
        #await Auth.create_free_subscription(verified.id, db)
        
        background_tasks.add_task(Auth.create_free_subscription, verified.id, db)
        
        return auth_schema.SignUpResponse(**{
            "data": user_schema.ShowUser.model_validate(verified),
            "access_token": access_token,
            "subscription_plan": "Basic plan",
            "token_type": "bearer",
            "message": "Email verified successfully, free subscription creation initiated"
        })
    except Exception as e:
        handle_auth_error(e)


@app.post("/auth/login", status_code=status.HTTP_200_OK, response_model=auth_schema.LoginResponse)
async def login_for_access_token(
    request: Request,  # Add this parameter
    response: Response,
    username: str = Form(...),
    password: str = Form(...),
    db: AsyncSession = Depends(get_db)
):  
    """
    LOGIN

    Returns: Logged in User and access token.
    """
    try:
        authService = Auth()
        user = await authService.authenticate_user(email=username, password=password, db=db)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=responses.INVALID_CREDENTIALS,
                headers={"WWW-Authenticate": "Bearer"},
            )
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = await authService.create_access_token(
            data={"id": user.id}, db=db, expires_delta=access_token_expires
        )

        refresh_token = await authService.create_refresh_token(data={"id": user.id}, db=db)

        response.set_cookie(
                key="refresh_token",
                value=refresh_token,
                max_age=JWT_REFRESH_EXPIRY,
                secure=True,
                httponly=True,
                samesite="strict",
                path="/"
            )
        
        handle_auth_notification(user_id=user.id, action="login", request=request)
        return auth_schema.LoginResponse(**{
            "data": user_schema.ShowUser.model_validate(user),
            "access_token": access_token, 
            "token_type": "bearer",
        })

    except Exception as e:
        #handle_auth_notification(user_id=user.id, action="login_failed", request=request)
        handle_auth_error(e)



@app.post("/auth/forgot-password")
async def forgot_password(
    request: user_schema.PasswordResetRequest,
    db: AsyncSession = Depends(get_db)):
    """Request a password reset code"""
    try:
        userService = UserService()
        return await userService.initiate_password_reset(request.email, db)
    except Exception as e:
        handle_auth_error(e)


@app.post("/auth/reset-password")
async def reset_password(
    reset_data: user_schema.PasswordReset,
    db: AsyncSession = Depends(get_db)):
    """Reset password using the reset code"""
    try:    
        userService = UserService()
        user_id = await userService.reset_password(reset_data.email, reset_data.code, reset_data.new_password, db)
        handle_auth_notification(user_id=user_id, action="password_reset")
        return {"detail": "Password reset successful"}
    except Exception as e:
        handle_auth_error(e)


@app.put("/auth/reset-password", status_code=status.HTTP_200_OK)
async def reset_password(
    password_change: auth_schema.ResetPassword,
    user: user_schema.CreateUser = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db)
):
    """Reset password endpoint
    """
    try:
        authService = Auth()
        new_password = await authService.reset_password(password_change=password_change, user=user, db=db)
        
        handle_auth_notification(user_id=user.id, action="change_password")
        return new_password

    except Exception as e:
        handle_auth_notification(user_id=user.id, action="change_password_failed")
        handle_auth_error(e)


"""
@app.get("/auth/refresh-access-token", status_code=status.HTTP_200_OK)
async def refresh_access_token(
    response: Response,
    refresh_token: Union[str, None] = Cookie(default=None),
    db: AsyncSession = Depends(get_db),
):
    "Refreshes an access_token with the issued refresh_token
    Parameters
        ----------
        refresh_token : str, None
            The refresh token sent in the cookie by the client (default is None)

        Raises
        ------
        UnauthorizedError
            If the refresh token is None.
    "

    credentials_exception =HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    if refresh_token is None:
        raise HTTPException(
            detail="Log in to authenticate user",
            status_code=status.HTTP_401_UNAUTHORIZED,
        )

    valid_refresh_token = Auth.verify_refresh_token(
        refresh_token, db
    )

    if valid_refresh_token.email is None:
        response.set_cookie(
            key="refresh_token",
            value=refresh_token,
            max_age=JWT_REFRESH_EXPIRY,
            secure=IS_REFRESH_TOKEN_SECURE,
            httponly=True,
            samesite="strict",
        )

        print("refresh failed")
    else:
        user = (
            db.query(UserModel)
            .filter(UserModel.id == valid_refresh_token.id)
            .first()
        )

        access_token = Auth.create_access_token(
            {"user_id": valid_refresh_token.id}, db
        )

        response.set_cookie(
            key="refresh_token",
            value=refresh_token,
            max_age=JWT_REFRESH_EXPIRY,
            secure=IS_REFRESH_TOKEN_SECURE,
            httponly=True,
            samesite="strict",
        )
    
    # Access token expires in 15 mins,
    return {"user": user_schema.ShowUser.model_validate(user), "access_token": access_token, "expires_in": 900}
"""


@app.post("/auth/logout", status_code=status.HTTP_200_OK)
async def logout_user(
    request: Request,
    response: Response,
    user: user_schema.User = Depends(not_authenticated),
    db: AsyncSession = Depends(get_db),
):
    """
        This endpoint logs out an authenticated user.

        Returns message: User logged out successfully.
    """
    try:
            
        authService = Auth()
        access_token = request.headers.get('Authorization')

        logout = await authService.logout(token=access_token, user=user, db=db)

        response.set_cookie(
            key="refresh_token",
            max_age="0",
            secure=True,
            httponly=True,
            samesite="strict",
        )

        return logout


    except Exception as e:
        handle_auth_error(e)



@app.get("/auth/login/google")
async def login_google():
    try:    
        return {
            "url": f"https://accounts.google.com/o/oauth2/auth?response_type=code&client_id={GOOGLE_CLIENT_ID}&redirect_uri={GOOGLE_REDIRECT_URI}&scope=openid%20profile%20email&access_type=offline"
        }
    except Exception as e:
        handle_auth_error(e)


"""
@app.get("/dashboard")
async def auth_google(code: str):
    
    #For testing only, will be replaced by the frontend
    
    return code
"""



@app.get("/auth/callback/google")
async def auth_google(request: Request, code: str, db: AsyncSession = Depends(get_db)):
    """
    Google auth callback
    """
    try:
        try:
            token_url = "https://accounts.google.com/o/oauth2/token"
            data = {
                "code": code,
                "client_id": GOOGLE_CLIENT_ID,
                "client_secret": GOOGLE_CLIENT_SECRET,
                "redirect_uri": GOOGLE_REDIRECT_URI,
                "grant_type": "authorization_code",
            }
            response = requests.post(token_url, data=data)
            access_token = response.json().get("access_token")
            
            user_info_response = requests.get(
                "https://www.googleapis.com/oauth2/v1/userinfo",
                headers={"Authorization": f"Bearer {access_token}"}
            )
            #print(user_info_response.json())
            user_info = user_schema.GoogleUserInfo.model_validate(user_info_response.json())

        except Exception as e:
            raise GoogleAuthError(f"Google callback authentication failed {str(e)}")

        authService = Auth()
        user = await authService.create_google_user(user_info, db)

        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = await authService.create_access_token(
            data={"id": user.id}, 
            db=db, 
            expires_delta=access_token_expires
        )
        handle_auth_notification(user_id=user.id, action="login", request=request)
        return auth_schema.LoginResponse(
            data=user_schema.ShowUser.model_validate(user),
            access_token=access_token,
            token_type="bearer",
            #redirct_dashboard = "http://127.0.0.1:5500/zests.html"
        )
        
    except Exception as e:
        #handle_auth_notification(user_id=user.id, action="login_failed", request=request)
        handle_auth_error(e)
    




@app.get("/auth/callback/wallet")
async def auth_wallet(request: Request, address: str, db: AsyncSession = Depends(get_db)):
    """
    Wallet auth callback
    """
    try:
        authService = Auth()
        user = await authService.create_or_get_polygon_wallet_user(
            address=address,
            db=db
        )
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = await authService.create_access_token(
            data={"id": user.id}, 
            db=db, 
            expires_delta=access_token_expires
        )

        handle_auth_notification(user_id=user.id, action="login", request=request)
        return auth_schema.LoginResponse(
            data=user_schema.ShowUser.model_validate(user),
            access_token=access_token,
            token_type="bearer",
        )
    
    except Exception as e:
        #handle_auth_notification(user_id=user.id, action="login_failed", request=request)
        handle_auth_error(e)
    