import secrets
import smtplib
import os
from email.mime.text import MIME<PERSON>ext
from email.mime.multipart import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from datetime import datetime
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from .exceptions import logger
from config import config
from pathlib import Path

# Email Configuration
EMAIL_SENDER = config.EMAIL_SENDER
EMAIL_PASSWORD = config.EMAIL_PASSWORD
SMTP_SERVER = config.SMTP_SERVER
SMTP_PORT = config.SMTP_PORT
VERIFICATION_TOKEN_LENGTH = config.VERIFICATION_TOKEN_LENGTH
VERIFICATION_TOKEN_EXPIRY_HOURS = config.VERIFICATION_TOKEN_EXPIRY_HOURS

# Company/Brand Information
COMPANY_NAME = "Atlas"
COMPANY_DOMAIN = "stellus.io"
SUPPORT_EMAIL = EMAIL_SENDER
COMPANY_ADDRESS = "Atlas Headquarters, Innovation District, Lagos, Nigeria"
COMPANY_LOGO_URL = "https://api.server.stellus.com/static/logo.png"

# Email Subjects
EMAIL_SUBJECT = f"{COMPANY_NAME} - Verify Your Email Address"

# Template file paths
TEMPLATE_DIR = Path(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "templates", "emails"))
VERIFICATION_EMAIL_TEMPLATE = TEMPLATE_DIR / "verification_email.html"
PASSWORD_RESET_EMAIL_TEMPLATE = TEMPLATE_DIR / "password_reset_email.html"
BASE_URL = "http://localhost:8000"  # IMPORTANT: Change this to your actual domain in production
ACCESS_TOKEN_EXPIRE_MINUTES = 30  # Example - adjust as needed
JWT_REFRESH_EXPIRY = 7 * 24 * 60 * 60 # 7 days in seconds



PASSWORD_RESET_TOKEN_EXPIRY_HOURS = 1 # Shorter expiry for password reset
EMAIL_SUBJECT_PASSWORD_RESET = f"{COMPANY_NAME} - Reset Your Password"




class EmailVerification:
    def __init__(self):
        pass

    def generate_verification_code(self) -> str:
        """Generate a 6-digit verification code"""
        return ''.join(secrets.choice('0123456789') for _ in range(6))

    def send_email(self, receiver, message):
        """Send an email using the configured SMTP server"""
        try:
            with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
                server.starttls()
                server.login(EMAIL_SENDER, EMAIL_PASSWORD)
                server.sendmail(EMAIL_SENDER, receiver, message.as_string())
                return True
        except Exception as e:
            logger.error(f"SMTP Error: {str(e)}")
            raise


    def load_template(self, template_path):
        """Load an HTML template from file"""
        try:
            with open(template_path, 'r', encoding='utf-8') as file:
                return file.read()
        except Exception as e:
            logger.error(f"Error loading template {template_path}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to load email template: {e}")

    def send_verification_email(self, email: str, verification_code: str) -> None:
        """Send a branded HTML verification email with the verification code"""
        try:
            # Create a multipart message
            message = MIMEMultipart("alternative")
            message["Subject"] = EMAIL_SUBJECT
            message["From"] = f"{COMPANY_NAME} <{EMAIL_SENDER}>"
            message["To"] = email

            # Get current year for copyright
            current_year = datetime.now().year

            # Load the HTML template
            template = self.load_template(VERIFICATION_EMAIL_TEMPLATE)

            # Replace template variables
            html_content = template.replace("{{ verification_code }}", verification_code)\
                                  .replace("{{ expiry_hours }}", str(VERIFICATION_TOKEN_EXPIRY_HOURS))\
                                  .replace("{{ company_name }}", COMPANY_NAME)\
                                  .replace("{{ company_logo }}", COMPANY_LOGO_URL)\
                                  .replace("{{ company_address }}", COMPANY_ADDRESS)\
                                  .replace("{{ support_email }}", SUPPORT_EMAIL)\
                                  .replace("{{ current_year }}", str(current_year))

            # Create HTML part
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)

            # Send the email
            self.send_email(email, message)
            logger.info(f"Verification email sent to {email}")

        except Exception as e:
            logger.error(f"Error sending verification email to {email}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to send verification email: {e}")



    def send_password_reset_email(self, email: str, reset_code: str) -> None:
        """Send a branded HTML password reset email with the reset code"""
        try:
            # Create a multipart message
            message = MIMEMultipart("alternative")
            message["Subject"] = EMAIL_SUBJECT_PASSWORD_RESET
            message["From"] = f"{COMPANY_NAME} <{EMAIL_SENDER}>"
            message["To"] = email

            # Get current year for copyright
            current_year = datetime.now().year

            # Load the HTML template
            template = self.load_template(PASSWORD_RESET_EMAIL_TEMPLATE)

            # Replace template variables
            html_content = template.replace("{{ reset_code }}", reset_code)\
                                  .replace("{{ expiry_hours }}", str(PASSWORD_RESET_TOKEN_EXPIRY_HOURS))\
                                  .replace("{{ company_name }}", COMPANY_NAME)\
                                  .replace("{{ company_logo }}", COMPANY_LOGO_URL)\
                                  .replace("{{ company_address }}", COMPANY_ADDRESS)\
                                  .replace("{{ support_email }}", SUPPORT_EMAIL)\
                                  .replace("{{ current_year }}", str(current_year))

            # Create HTML part
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)

            # Send the email
            self.send_email(email, message)
            logger.info(f"Password reset email sent to {email}")

        except Exception as e:
            logger.error(f"Error sending password reset email to {email}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to send password reset email: {e}")






"""

EMAIL_SENDER = config.EMAIL_SENDER
EMAIL_PASSWORD = config.EMAIL_PASSWORD
SMTP_SERVER = config.SMTP_SERVER
SMTP_PORT = config.SMTP_PORT
VERIFICATION_TOKEN_LENGTH = config.VERIFICATION_TOKEN_LENGTH
VERIFICATION_TOKEN_EXPIRY_HOURS = config.VERIFICATION_TOKEN_EXPIRY_HOURS
EMAIL_SUBJECT = "Verify Your Email Address"
EMAIL_BODY_TEMPLATE = ""
Hello,

Please click the link below to verify your email address:

{verification_link}

This link will expire in {expiry_hours} hours.

Regards,
Your Application Team
""
BASE_URL = "http://localhost:8000"  # IMPORTANT: Change this to your actual domain in production
ACCESS_TOKEN_EXPIRE_MINUTES = 30  # Example - adjust as needed
JWT_REFRESH_EXPIRY = 7 * 24 * 60 * 60 # 7 days in seconds



PASSWORD_RESET_TOKEN_EXPIRY_HOURS = 1 # Shorter expiry for password reset
EMAIL_SUBJECT_PASSWORD_RESET = "Reset Your Password"
EMAIL_BODY_TEMPLATE_PASSWORD_RESET = ""
Hello,

Please click the link below to reset your password:

{reset_link}

This link will expire in {expiry_hours} hours.

If you did not request a password reset, please ignore this email.

Regards,
Your Application Team
""




class EmailVerification:
    def __init__(self):
        pass

    def generate_verification_token(self, length: int = VERIFICATION_TOKEN_LENGTH) -> str:
        return secrets.token_urlsafe(length)

    def encode_verification_data(self, data: Dict) -> str:
        return base64.urlsafe_b64encode(urlencode(data).encode()).decode()

    def decode_verification_data(self, encoded_data: str) -> Dict:
        try:
            #return dict(pair.split("=") for pair in base64.urlsafe_b64decode(encoded_data).decode().split("&"))

            decoded = base64.urlsafe_b64decode(encoded_data).decode()
            parsed = parse_qs(decoded)
            # Convert from {key: [value]} to {key: value} and decode URL-encoded characters
            return {k: unquote(v[0]) for k, v in parsed.items()}
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid verification link")


    def send_email(self, receiver, message):

        #sender = "<EMAIL>"
        #receiver = "<EMAIL>"
        with smtplib.SMTP(SMTP_SERVER, SMTP_PORT) as server:
            server.starttls()
            server.login(EMAIL_SENDER, EMAIL_PASSWORD)
            server.sendmail(EMAIL_SENDER, receiver, message.as_string())
            #server.sendmail(sender, receiver, message.as_string())


    def send_verification_email(self, email: str, verification_link: str) -> None:
        try:

            message = MIMEText(EMAIL_BODY_TEMPLATE.format(verification_link=verification_link, expiry_hours=VERIFICATION_TOKEN_EXPIRY_HOURS))
            message['Subject'] = EMAIL_SUBJECT
            message['From'] = EMAIL_SENDER
            message['To'] = email

            self.send_email(email, message)
            logger.info(f"Verification email sent to {email}")

        except Exception as e:
            logger.error(f"Error sending email to {email}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to send verification email: {e}")



    def send_password_reset_email(self, email: str, reset_link: str) -> None:
        try:
            message = MIMEText(EMAIL_BODY_TEMPLATE_PASSWORD_RESET.format(reset_link=reset_link,
                                                                         expiry_hours=PASSWORD_RESET_TOKEN_EXPIRY_HOURS))
            message['Subject'] = EMAIL_SUBJECT_PASSWORD_RESET
            message['From'] = EMAIL_SENDER
            message['To'] = email

            self.send_email(email, message)
            logger.info(f"Password reset email sent to {email}")

        except Exception as e:
            logger.error(f"Error sending password reset email to {email}: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to send password reset email: {e}")


"""
