from sqlalchemy import <PERSON><PERSON><PERSON>, Column, ForeignKey, Text, Integer, String, DateTime, BIGINT, JSON, Enum
from sqlalchemy.orm import relationship
from datetime import datetime, date, timezone
from api.db.database import Base
import enum


class NotificationPriority(str, enum.Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

class NotificationType(str, enum.Enum):
    SYSTEM = "system"
    TRANSACTION = "transaction"
    CONTRACT = "contract"
    AUTH = "auth"
    USER = "user"
    WALLET = "wallet"
    PAYMENT = "payment"
    SUBSCRIPTION = "subscription"

class NotificationStatus(str, enum.Enum):
    PENDING = "pending"
    DELIVERED = "delivered"
    READ = "read"
    FAILED = "failed"
    DELETED = "deleted"

class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False, index=True)
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    type = Column(Enum(NotificationType), nullable=False, default=NotificationType.SYSTEM)
    priority = Column(Enum(NotificationPriority), nullable=False, default=NotificationPriority.NORMAL)
    status = Column(Enum(NotificationStatus), nullable=False, default=NotificationStatus.PENDING)

    is_read = Column(Boolean, default=False, index=True)
    is_deleted = Column(Boolean, default=False, index=True)

    noti_metadata = Column(JSON, nullable=True)
    action_url = Column(String(512), nullable=True)  # Optional URL for notification action

    created_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now)
    updated_at = Column(DateTime(timezone=True), nullable=False, default=datetime.now, onupdate=datetime.now)
    read_at = Column(DateTime(timezone=True), nullable=True)
    delivered_at = Column(DateTime(timezone=True), nullable=True)

    # Relationships
    user = relationship("User", back_populates="notifications")

    def __repr__(self):
        return f"<Notification(id={self.id}, type={self.type}, status={self.status})>"


    def mark_as_read(self):
        """Mark the notification as read and update read_at timestamp"""
        self.is_read = True
        self.status = NotificationStatus.READ
        self.read_at = datetime.now(timezone.utc)

    def mark_as_deleted(self):
        """Mark the notification as deleted"""
        self.status = NotificationStatus.DELETED

    """
    def is_expired(self):
        #""Check if the notification has expired""
        if self.expires_at:
            return datetime.now(timezone.utc) > self.expires_at
        return False
    """