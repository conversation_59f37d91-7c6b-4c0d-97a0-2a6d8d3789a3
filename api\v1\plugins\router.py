from sqlalchemy.orm import Session
from typing import Optional
from api.v1.user import schemas as user_schema

# routes/rwa_plugin.py
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List
from api.db.database import get_db
from api.core.dependencies import is_authenticated
from api.v1.plugins.schemas import (
    CreateRealWorldAssetRequest,
    RealWorldAssetResponse,
    AssetVerificationRequest,
    RWATokenizationRequest,
    AssetVerificationResponse
)
from api.v1.plugins.services import RWAPluginService
from . import models


app = APIRouter(tags=["RWA Plugin"])
rwa_service = RWAPluginService()


from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, File, UploadFile
from sqlalchemy.orm import Session
from typing import Optional, List
from web3 import Web3

from api.v1.user import schemas as user_schema
from api.db.database import get_db
from api.core.dependencies import is_authenticated
from api.v1.plugins.schemas import (
    CreateRealWorldAssetRequest,
    RealWorldAssetResponse,
    AssetVerificationRequest,
    RWATokenizationRequest,
    AssetVerificationResponse
)
from api.v1.plugins.services import RWAPluginService
from . import models

@app.post("/plugins/rwa/assets", response_model=RealWorldAssetResponse)
async def create_rwa(
    asset_type: models.RWAType,
    token_standard: models.TokenStandard,
    asset_data: CreateRealWorldAssetRequest,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """
    Create a new Real World Asset
    """
    try:
        return rwa_service.create_real_world_asset(
            db=db,
            user_id=user.id,
            asset_data=asset_data,
            asset_type=asset_type,
            token_standard=token_standard
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create Real World Asset."
        )

@app.get("/plugins/rwa/assets/{asset_id}", response_model=RealWorldAssetResponse)
async def get_rwa(
    asset_id: int,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """
    Get Real World Asset details
    """
    try:
        asset = rwa_service.get_asset(db=db, asset_id=asset_id)
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        return asset
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve Real World Asset."
        )

@app.get("/plugins/rwa/assets", response_model=List[RealWorldAssetResponse])
async def list_rwa(
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 10
):
    """
    List Real World Assets
    """
    try:
        return rwa_service.list_assets(db=db, skip=skip, limit=limit)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list Real World Assets."
        )

@app.post("/plugins/rwa/verify", response_model=AssetVerificationResponse)
async def verify_rwa(
    verification_data: AssetVerificationRequest,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """
    Submit verification documents for a Real World Asset
    """
    try:
        return rwa_service.verify_asset_documents(
            db=db,
            verifier_id=user.id,
            verification_data=verification_data
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify asset documents."
        )

@app.post("/plugins/rwa/tokenize")
async def tokenize_rwa(
    tokenization_request: RWATokenizationRequest,
    background_tasks: BackgroundTasks,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """
    Tokenize a verified Real World Asset on the blockchain
    """
    try:
        return rwa_service.tokenize_asset(
            db=db,
            user_id=user.id,
            tokenization_request=tokenization_request,
            background_tasks=background_tasks
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to tokenize Real World Asset."
        )

@app.get("/plugins/rwa/market")
async def get_rwa_market(
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    asset_type: Optional[str] = None,
    status: Optional[str] = None,
    min_price: Optional[float] = None,
    max_price: Optional[float] = None
):
    """
    Get RWA marketplace listings with optional filters
    """
    try:
        return rwa_service.get_market_listings(
            db=db,
            asset_type=asset_type,
            status=status,
            min_price=min_price,
            max_price=max_price,
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve marketplace listings."
        )

@app.post("/plugins/rwa/assets/{asset_id}/list")
async def list_rwa_market(
    asset_id: int,
    listing_price: float,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    """
    List a tokenized RWA on the marketplace
    """
    try:
        return rwa_service.list_asset_on_market(
            db=db,
            asset_id=asset_id,
            user_id=user.id,
            listing_price=listing_price
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list asset on marketplace."
        )