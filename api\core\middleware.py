from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
import os
class MaintenanceModeMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Skip maintenance check for backup endpoints and static files
        if request.url.path.startswith(("/backup", "/static")):
            return await call_next(request)

        # Check for maintenance mode
        if os.path.exists("/tmp/maintenance_mode"):
            raise HTTPException(
                status_code=503,
                detail="System is under maintenance. Please try again later."
            )
        
        return await call_next(request)