import os
import shutil
import asyncio
import aiofiles
import time
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from api.core.logging_config import get_logger
from config import config
from azure.storage.blob.aio import BlobServiceClient
from cryptography.fernet import Fernet
import gzip
from prometheus_client import Counter, Histogram, Gauge
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.pool import NullPool
from api.db.database import create_async_db_url


logger = get_logger(__name__)

# Prometheus metrics
backup_operations = Counter(
    'atlas_backup_operations_total',
    'Number of backup operations',
    ['operation', 'status']
)
backup_duration = Histogram(
    'atlas_backup_duration_seconds',
    'Time spent performing backups',
    ['operation']
)
backup_size = Gauge(
    'atlas_backup_size_bytes',
    'Size of backup files',
    ['type']
)
health_check_operations = Counter(
    'atlas_health_check_operations_total',
    'Number of health check operations',
    ['status']
)
health_metrics = Gauge(
    'atlas_database_metrics',
    'Database health metrics',
    ['metric']
)
recovery_operations = Counter(
    'atlas_recovery_operations_total',
    'Number of auto-recovery operations',
    ['action', 'status']
)
maintenance_operations = Counter(
    'atlas_maintenance_operations_total',
    'Number of maintenance operations',
    ['operation', 'status']
)

class BackupManager:
    """Enterprise-grade backup manager with Azure integration for production"""

    def __init__(self):
        self.env = config.PYTHON_ENV
        self.backup_dir = config.BACKUP_DIR
        self.retention_days = config.BACKUP_RETENTION_DAYS
        self.azure_enabled = self._check_azure_enabled()
        if self.azure_enabled:
            self._init_azure_client()
        self._init_encryption()
        self.ensure_backup_directory()


    def _check_azure_enabled(self) -> bool:
        """Determine if Azure storage should be enabled"""
        if self.env != "production":
            return False
            
        if not config.AZURE_STORAGE_CONNECTION_STRING:
            logger.warning("Azure Storage connection string not configured in production")
            return False
            
        if not config.AZURE_BACKUP_CONTAINER:
            logger.warning("Azure backup container not configured in production")
            return False
            
        return True

    def _init_azure_client(self):
        """Initialize Azure client for production environment"""
        self.connection_string = config.AZURE_STORAGE_CONNECTION_STRING
        if not self.connection_string:
            raise ValueError("AZURE_STORAGE_CONNECTION_STRING required in production")

        self.container_name = config.AZURE_BACKUP_CONTAINER
        self.temp_dir = config.BACKUP_TEMP_DIR
        self.ensure_temp_directory()

    def _init_encryption(self):
        """Initialize encryption for sensitive data"""
        encryption_key = config.BACKUP_ENCRYPTION_KEY
        if not encryption_key and self.azure_enabled:
            raise ValueError("BACKUP_ENCRYPTION_KEY required in production")

        self.fernet = Fernet(encryption_key.encode()) if encryption_key else None

    def ensure_backup_directory(self):
        """Ensure backup directory exists with secure permissions"""
        try:
            os.makedirs(self.backup_dir, mode=0o700, exist_ok=True)
        except Exception as e:
            logger.critical(f"Failed to create backup directory: {e}")
            raise

    def ensure_temp_directory(self):
        """Ensure temporary directory exists for Azure operations"""
        if self.azure_enabled:
            try:
                os.makedirs(self.temp_dir, mode=0o700, exist_ok=True)
            except Exception as e:
                logger.critical(f"Failed to create temporary directory: {e}")
                raise

    async def create_database_backup(self) -> Optional[str]:
        """Create an encrypted and compressed database backup"""
        with backup_duration.labels(operation='create').time():
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = f"{self.backup_dir}/db_backup_{timestamp}.sql"
                compressed_file = f"{backup_file}.gz"

                # Run pg_dump with enhanced settings
                dump_cmd = [
                    "pg_dump",
                    "-U", config.DB_USER,
                    "-d", config.DB_NAME,
                    "-h", config.DB_HOST,
                    "-p", str(config.DB_PORT),
                    "--format=custom",
                    "--blobs",
                    "--no-owner",
                    "--no-privileges",
                    "--compress=9",
                    "-F", "c",  # Custom format for better compression
                    "-f", backup_file
                ]

                process = await asyncio.create_subprocess_exec(
                    *dump_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE,
                    env={"PGPASSWORD": config.DB_PASSWORD}
                )

                stdout, stderr = await process.communicate()

                if process.returncode == 0:
                    # Compress with maximum compression
                    with open(backup_file, 'rb') as f_in:
                        with gzip.open(compressed_file, 'wb', compresslevel=9) as f_out:
                            shutil.copyfileobj(f_in, f_out)

                    os.remove(backup_file)

                    # Verify backup integrity
                    if not await self.verify_backup_integrity(compressed_file):
                        raise ValueError("Backup verification failed")

                    # Handle encryption and Azure upload for production
                    if self.azure_enabled:
                        encrypted_file = await self._encrypt_and_upload(compressed_file)
                        os.remove(compressed_file)
                        backup_operations.labels(operation='create', status='success').inc()
                        return encrypted_file

                    backup_size.labels(type='database').set(os.path.getsize(compressed_file))
                    backup_operations.labels(operation='create', status='success').inc()
                    return compressed_file
                else:
                    logger.error(f"pg_dump failed: {stderr.decode()}")
                    backup_operations.labels(operation='create', status='failed').inc()
                    raise Exception("Backup creation failed")

            except Exception as e:
                logger.error(f"Backup creation failed: {str(e)}")
                backup_operations.labels(operation='create', status='failed').inc()
                raise

    async def _encrypt_and_upload(self, file_path: str) -> str:
        """Encrypt and upload file to Azure"""
        try:
            # Encrypt file
            with open(file_path, 'rb') as f:
                data = f.read()
            encrypted_data = self.fernet.encrypt(data)

            # Upload to Azure
            async with BlobServiceClient.from_connection_string(self.connection_string) as client:
                container_client = client.get_container_client(self.container_name)
                blob_name = os.path.basename(file_path) + ".encrypted"
                blob_client = container_client.get_blob_client(blob_name)

                await blob_client.upload_blob(encrypted_data, overwrite=True)
                return blob_name

        except Exception as e:
            logger.error(f"Encryption/upload failed: {e}")
            raise

    async def restore_database_backup(self, backup_name: str, target_connection: Optional[Dict[str, Any]] = None) -> bool:
        """Restore database with safety checks and verification"""
        with backup_duration.labels(operation='restore').time():
            try:
                if self.azure_enabled:
                    local_file = await self._download_and_decrypt(backup_name)
                else:
                    local_file = os.path.join(self.backup_dir, backup_name)

                if not os.path.exists(local_file):
                    raise FileNotFoundError(f"Backup file not found: {local_file}")

                # Use target connection if provided, otherwise use config
                conn = target_connection or {
                    'host': config.DB_HOST,
                    'port': config.DB_PORT,
                    'user': config.DB_USER,
                    'password': config.DB_PASSWORD,
                    'dbname': config.DB_NAME
                }

                # Create test database for verification
                test_db = f"restore_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                try:
                    # Test restore to temporary database
                    success = await self._test_restore(local_file, test_db, conn)
                    if not success:
                        pass

                    # Perform actual restore
                    success = await self._perform_restore(local_file, conn)
                    if success:
                        backup_operations.labels(operation='restore', status='success').inc()
                        return True

                    backup_operations.labels(operation='restore', status='failed').inc()
                    return False

                finally:
                    # Cleanup
                    await self._cleanup_test_db(test_db, conn)
                    if self.azure_enabled:
                        os.remove(local_file)

            except Exception as e:
                logger.error(f"Restore failed: {str(e)}")
                backup_operations.labels(operation='restore', status='failed').inc()
                raise

    async def _test_restore(self, backup_file: str, test_db: str, conn: Dict[str, Any]) -> bool:
        try:
                
            """Test restore to temporary database"""
            create_cmd = [
                "createdb",
                "-h", conn['host'],
                "-p", str(conn['port']),
                "-U", conn['user'],
                test_db
            ]

            process = await asyncio.create_subprocess_exec(
                *create_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env={"PGPASSWORD": conn['password']}
            )

            stdout, stderr = await process.communicate()
            if process.returncode != 0:
                logger.error(f"Failed to create test database: {stderr.decode()}")
                return False

            restore_cmd = [
                "pg_restore",
                "-h", conn['host'],
                "-p", str(conn['port']),
                "-U", conn['user'],
                "-d", test_db,
                "--no-owner",
                "--no-privileges",
                backup_file
            ]

            process = await asyncio.create_subprocess_exec(
                *restore_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env={"PGPASSWORD": conn['password']}
            )

            stdout, stderr = await process.communicate()
            return process.returncode == 0
        except Exception as e:
            raise e

    async def _perform_restore(self, backup_file: str, conn: Dict[str, Any]) -> bool:
        """Perform actual database restore"""
        logger.debug(f"Attempting to restore from file: {backup_file}")
        logger.debug(f"File exists: {os.path.exists(backup_file)}")
        logger.debug(f"File size: {os.path.getsize(backup_file)}")
        
        restore_cmd = [
            "pg_restore",
            #"--jobs=1",
            "-h", conn['host'],
            "-p", str(conn['port']),
            "-U", conn['user'],
            "-d", conn['dbname'],
            "--clean",
            "--if-exists",
            "--no-owner",
            "--no-privileges",
            "-v",  # Add verbose output
            backup_file
        ]

        process = await asyncio.create_subprocess_exec(
            *restore_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env={"PGPASSWORD": conn['password']}
        )

        stdout, stderr = await process.communicate()
        logger.debug(f"pg_restore stdout: {stdout.decode()}")
        logger.debug(f"pg_restore stderr: {stderr.decode()}")
        return process.returncode == 0

    async def _cleanup_test_db(self, test_db: str, conn: Dict[str, Any]):
        """Clean up test database"""
        drop_cmd = [
            "dropdb",
            "-h", conn['host'],
            "-p", str(conn['port']),
            "-U", conn['user'],
            "--if-exists",
            test_db
        ]

        await asyncio.create_subprocess_exec(
            *drop_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            env={"PGPASSWORD": conn['password']}
        )

    async def _download_and_decrypt(self, blob_name: str) -> str:
        """Download and decrypt file from Azure"""
        local_file = os.path.join(self.temp_dir, blob_name)

        try:
            async with BlobServiceClient.from_connection_string(self.connection_string) as client:
                container_client = client.get_container_client(self.container_name)
                blob_client = container_client.get_blob_client(blob_name)

                # Download
                blob_data = await blob_client.download_blob()
                encrypted_data = await blob_data.readall()

                # Decrypt
                decrypted_data = self.fernet.decrypt(encrypted_data)

                # Save locally
                decrypted_file = local_file.replace('.encrypted', '')
                async with aiofiles.open(decrypted_file, 'wb') as f:
                    await f.write(decrypted_data)

                return decrypted_file

        except Exception as e:
            logger.error(f"Download/decrypt failed: {e}")
            raise

    async def verify_backup_integrity(self, backup_file: str) -> bool:
        """Verify backup file integrity"""
        try:
            if not os.path.exists(backup_file):
                return False

            if os.path.getsize(backup_file) == 0:
                return False

            if backup_file.endswith('.gz'):
                with gzip.open(backup_file, 'rb') as f:
                    f.read(1024)  # Test reading

            elif backup_file.endswith('.encrypted'):
                with open(backup_file, 'rb') as f:
                    self.fernet.decrypt(f.read())  # Test decryption

            return True

        except Exception as e:
            logger.error(f"Backup verification failed: {e}")
            return False


    async def cleanup_old_backups(self) -> None:
        """Clean up old backups based on retention policy"""
        try:
            # --- only for test ---
            #retention_minutes = 0.002 * 24 * 60  # Convert days to minutes
            #cutoff_date = datetime.now(timezone.utc) - timedelta(minutes=retention_minutes)
            
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=self.retention_days)
            
            # --- Clean Azure backups in production ---
            if self.azure_enabled:
                if not self.connection_string:
                    logger.error("Azure Storage connection string is not configured. Skipping Azure backup cleanup.")
                else:
                    try:
                        async with BlobServiceClient.from_connection_string(self.connection_string) as client:
                            container_client = client.get_container_client(self.container_name)
                            blobs_processed = 0
                            blobs_deleted = 0

                            async for blob in container_client.list_blobs():
                                blobs_processed += 1
                                try:
                                    if blob.creation_time and blob.creation_time.tzinfo is not None:
                                        blob_creation_utc = blob.creation_time
                                    else:
                                        logger.warning(f"Azure blob {blob.name} has naive creation_time. Assuming UTC.")
                                        blob_creation_utc = blob.creation_time.replace(tzinfo=timezone.utc) if blob.creation_time else None

                                    if blob_creation_utc and blob_creation_utc < cutoff_date:
                                        await container_client.delete_blob(blob.name)
                                        logger.info(f"Deleted old Azure backup: {blob.name} (Created: {blob_creation_utc.isoformat()})")
                                        blobs_deleted += 1
                                except Exception as e_azure_blob:
                                    logger.error(f"Error processing Azure blob {blob.name} for cleanup: {e_azure_blob}")

                                logger.info(f"Azure cleanup: Processed {blobs_processed} blobs, deleted {blobs_deleted}.")
                    except Exception as e_azure_setup:
                        logger.error(f"Failed to initialize Azure client or list blobs: {e_azure_setup}")
                        # reraise

            # --- Clean local backups ---
            logger.info(f"Checking local backup directory: {self.backup_dir} for old backups.")
            if not os.path.exists(self.backup_dir):
                logger.warning(f"Local backup directory {self.backup_dir} does not exist. Skipping local cleanup.")
            elif not os.path.isdir(self.backup_dir):
                logger.warning(f"Local backup path {self.backup_dir} is not a directory. Skipping local cleanup.")
            else:
                files_processed = 0
                files_deleted = 0

            for filename in os.listdir(self.backup_dir):
                filepath = os.path.join(self.backup_dir, filename)
                files_processed += 1
                try:
                    if os.path.isfile(filepath):
                        file_stat_ts = os.path.getctime(filepath)
                        file_created_at_utc = datetime.fromtimestamp(file_stat_ts, timezone.utc)
                        
                        if file_created_at_utc < cutoff_date:
                            os.remove(filepath)
                            logger.info(f"Deleted old local backup: {filename} (Created/MetadataChanged: {file_created_at_utc.isoformat()})")
                            files_deleted += 1
                    else:
                        logger.debug(f"Skipping non-file entry in local backup_dir: {filename}")
                except FileNotFoundError:
                    logger.warning(f"Local backup file {filepath} not found during cleanup (possibly deleted by another process).")
                except Exception as e_local_file:
                    logger.error(f"Error processing local file {filepath} for cleanup: {e_local_file}")
                    # Continue to next file
                logger.info(f"Local cleanup: Processed {files_processed} files/entries, deleted {files_deleted} files.")

        except Exception as e_general:
            logger.error(f"General backup cleanup task failed: {str(e_general)}", exc_info=True)
            raise


    async def get_backup_status(self) -> Dict[str, Any]:
        """Get comprehensive backup status"""
        try:
            backups = []
            total_size = 0

            # Get local backups
            for filename in os.listdir(self.backup_dir):
                filepath = os.path.join(self.backup_dir, filename)
                stats = os.stat(filepath)
                backups.append({
                    'filename': filename,
                    'size': stats.st_size,
                    'created_at': datetime.fromtimestamp(os.path.getctime(filepath)).isoformat(),
                    'location': 'local',
                    'type': 'database' if filename.endswith('.sql.gz') else 'other'
                })
                total_size += stats.st_size

            # Get Azure backups in production
            if self.azure_enabled:
                async with BlobServiceClient.from_connection_string(self.connection_string) as client:
                    container_client = client.get_container_client(self.container_name)
                    async for blob in container_client.list_blobs():
                        backups.append({
                            'filename': blob.name,
                            'size': blob.size,
                            'created_at': blob.creation_time.isoformat(),
                            'location': 'azure',
                            'type': 'database' if blob.name.endswith('.sql.gz.encrypted') else 'other'
                        })
                        total_size += blob.size

            return {
                'backups': sorted(backups, key=lambda x: x['created_at'], reverse=True),
                'total_size': total_size,
                'backup_count': len(backups),
                'storage_locations': ['local', 'azure'] if self.azure_enabled else ['local'],
                'retention_days': self.retention_days
            }

        except Exception as e:
            logger.error(f"Failed to get backup status: {str(e)}")
            return {}

    async def check_database_health(self) -> Dict[str, Any]:
        """Check database health and performance metrics"""
        try:
            engine = create_async_engine(create_async_db_url(), poolclass=NullPool)
            try:
                async with engine.connect() as conn:
                        
                    # Check active connections
                    connections = await conn.execute(text(
                        "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
                    ))
                    active_conns = connections.scalar()

                    # Check connection response time
                    start_time = time.time()
                    await conn.execute(text("SELECT 1"))
                    response_time = time.time() - start_time

                    # Check replication lag if using replica
                    replication_lag = None
                    if config.USING_REPLICA:
                        lag_result = await conn.execute(text(
                            "SELECT EXTRACT(EPOCH FROM (now() - pg_last_xact_replay_timestamp()))"
                        ))
                        replication_lag = lag_result.scalar()

                    # Check database size
                    size_result = await conn.execute(text(
                        "SELECT pg_database_size(current_database())"
                    ))
                    db_size = size_result.scalar()

                    # Check transaction wraparound status
                    wraparound = await conn.execute(text(
                        "SELECT age(datfrozenxid) FROM pg_database WHERE datname = current_database()"
                    ))
                    xid_age = wraparound.scalar()

                    status = "healthy"
                    if (response_time > 1.0 or  # Response time > 1 second
                        active_conns > 100 or   # Too many active connections
                        (replication_lag and replication_lag > 300) or  # Replication lag > 5 minutes
                        xid_age > **********):  # Transaction ID wraparound approaching
                        status = "unhealthy"

                    if health_check_operations:  # Check if metrics are enabled
                        health_check_operations.labels(status=status).inc()
                        health_metrics.labels(metric="connections").set(active_conns)
                        health_metrics.labels(metric="response_time").set(response_time)
                        if replication_lag is not None:
                            health_metrics.labels(metric="replication_lag").set(replication_lag)
                        health_metrics.labels(metric="database_size").set(db_size)
                        health_metrics.labels(metric="xid_age").set(xid_age)

                    return {
                        "status": status,
                        "connections": active_conns,
                        "response_time": response_time,
                        "replication_lag": replication_lag,
                        "database_size": db_size,
                        "xid_age": xid_age,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
            finally:
                await engine.dispose()

        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            if health_check_operations:
                health_check_operations.labels(status="error").inc()
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    async def vacuum_analyze(self) -> bool:
        """Perform VACUUM ANALYZE with safety checks"""
        try:
            # Create a new engine for this specific vacuum operation to avoid event loop conflicts
            engine = create_async_engine(create_async_db_url(), poolclass=NullPool)

            try:
                async with engine.connect() as conn:
                    # Get list of tables
                    tables = await conn.execute(text(
                        "SELECT tablename FROM pg_tables WHERE schemaname = 'public'"
                    ))

                    for table in tables:
                        try:
                            # Check table size and bloat
                            size_result = await conn.execute(text(
                                f"SELECT pg_total_relation_size('{table[0]}')"
                            ))
                            table_size = size_result.scalar()

                            # Skip small tables or if recently vacuumed
                            stats = await conn.execute(text(
                                f"SELECT last_vacuum, last_analyze FROM pg_stat_all_tables WHERE relname = '{table[0]}'"
                            ))
                            last_maintenance = stats.fetchone()

                            if (table_size < 1024 * 1024 or  # Skip if < 1MB
                                (last_maintenance and
                                last_maintenance[0] and
                                (datetime.now() - last_maintenance[0]).days < 1)):
                                continue

                            # Perform VACUUM ANALYZE
                            await conn.execute(text(f"VACUUM ANALYZE {table[0]}"))
                            logger.info(f"VACUUM ANALYZE completed for table {table[0]}")
                            maintenance_operations.labels(operation="vacuum_analyze", status="success").inc()

                        except Exception as e:
                            logger.error(f"Failed to vacuum table {table[0]}: {e}")
                            maintenance_operations.labels(operation="vacuum_analyze", status="failed").inc()
                            continue

                    return True
            finally:
                await engine.dispose()

        except Exception as e:
            logger.error(f"VACUUM ANALYZE failed: {str(e)}")
            maintenance_operations.labels(operation="vacuum_analyze", status="failed").inc()
            return False

    async def auto_recover(self) -> bool:
        """Attempt automatic recovery of database issues"""
        try:
            # Create a new engine for this specific recovery to avoid event loop conflicts
            from api.db.database import create_async_engine_with_retry
            engine = create_async_engine_with_retry()
            async with engine.connect() as conn:
                health = await self.check_database_health()

                if health["status"] != "unhealthy":
                    return True

                recovery_actions = []

                # Handle connection overload
                if health.get("connections", 0) > 100:
                    await conn.execute(text(
                        "SELECT pg_terminate_backend(pid) FROM pg_stat_activity "
                        "WHERE state = 'idle' AND state_change < now() - interval '1 hour'"
                    ))
                    recovery_actions.append("terminated_idle_connections")
                    recovery_operations.labels(action="terminate_idle_connections", status="success").inc()

                # Handle replication lag
                if health.get("replication_lag", 0) > 300 and config.USING_REPLICA:
                    # Notify monitoring system about high replication lag
                    logger.warning(f"High replication lag detected: {health['replication_lag']}s")
                    recovery_actions.append("replication_lag_alert")
                    recovery_operations.labels(action="replication_lag_alert", status="success").inc()

                # Handle transaction wraparound
                if health.get("xid_age", 0) > **********:
                    # Schedule VACUUM FREEZE during next maintenance window
                    recovery_actions.append("vacuum_freeze_scheduled")
                    recovery_operations.labels(action="vacuum_freeze_scheduled", status="success").inc()

                if recovery_actions:
                    logger.info(f"Auto-recovery actions taken: {', '.join(recovery_actions)}")
                    return True

                return False

        except Exception as e:
            logger.error(f"Auto-recovery failed: {str(e)}")
            recovery_operations.labels(action="auto_recovery", status="failed").inc()
            return False
