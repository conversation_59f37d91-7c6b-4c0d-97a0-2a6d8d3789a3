from datetime import datetime, date, timedelta, timezone
from api.db.database import Base
from sqlalchemy import Column, Integer, String, DateTime, Numeric, JSON, BIGINT, Enum, ForeignKey, Text
from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func
import enum
from .schemas import ContractDeploymentResponse, ContractResponse, TransactionResponse, BlockResponse
from sqlalchemy.orm import relationship
from sqlalchemy.types import JSON


class ContractStatus(enum.Enum):
    DEPLOYED = "deployed"
    FAILED = "failed"


class TransactionStatus(enum.Enum):
    SUCCESS = 1
    FAILURE = 0


class TransactionType(enum.Enum):
    ER20 = "erc20"
    ERC721 = "erc721"
    NONE = "none"


class ContractDeployment(Base):
    __tablename__ = 'contract_deployments'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), default=datetime.now, nullable=False)

    name = Column(String, nullable=False)
    symbol = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    image = Column(String, nullable=True)

    # Contract fields
    contract_address = Column(String, nullable=False)
    contract_type = Column(Enum(TransactionType), nullable=False)
    contract_status = Column(Enum(ContractStatus), nullable=False)

    # Transaction fields
    transaction_hash = Column(String, nullable=False)
    transaction_from = Column(String, nullable=False)
    transaction_to = Column(String, nullable=True)
    transaction_status = Column(Enum(TransactionStatus), nullable=True)
    transaction_index = Column(Integer, nullable=True)
    gas_used = Column(BIGINT, nullable=True)
    effective_gas_price = Column(BIGINT, nullable=True)
    cumulative_gas_used = Column(BIGINT, nullable=True)
    transaction_type = Column(Integer, nullable=True)

    # Block fields
    block_hash = Column(String, nullable=False)
    block_number = Column(BIGINT, nullable=False)



    def to_response(self) -> ContractDeploymentResponse:
        return ContractDeploymentResponse(
            id=self.id,
            createdAt=self.created_at,
            contract=ContractResponse(
                address=self.contract_address,
                type=self.contract_type,
                status=self.contract_status,

                name = self.name,
                symbol = self.symbol,
                description = self.description,
                image = self.image,

            ),
            transaction=TransactionResponse(
                hash=self.transaction_hash,
                from_=self.transaction_from,
                to=self.transaction_to,
                status=self.transaction_status,
                transactionIndex=self.transaction_index,
                gasUsed=self.gas_used,
                effectiveGasPrice=self.effective_gas_price,
                cumulativeGasUsed=self.cumulative_gas_used,
                type=self.transaction_type
            ),
            block=BlockResponse(
                hash=self.block_hash,
                number=self.block_number
            )
        )



import json
from web3 import Web3
from decimal import Decimal

def make_json_serializable(obj):
    if isinstance(obj, bytes):
        return Web3.to_hex(obj)
    if isinstance(obj, Decimal):
        return str(obj)
    if isinstance(obj, enum.Enum):
        return obj.value
    if hasattr(obj, '__dict__'):
        return obj.__dict__
    return str(obj)


# Then when preparing your transaction data:

# Updated ToBeSigned model (add to your models)
class ToBeSigned(Base):
    __tablename__ = "tobesigned"

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('user.id'), index=True)
    nft_id = Column(Integer, ForeignKey('nfts.id'), nullable=True, index=True)

    operation = Column(JSON, nullable=True)  # Changed to JSON type

    transaction_data = Column(JSON, nullable=False)
    total_cost_wei = Column(Numeric(32), nullable=False)
    total_cost_eth = Column(Numeric(32, 18), nullable=False)

    name = Column(String, nullable=True)
    symbol = Column(String, nullable=True)
    description = Column(Text, nullable=True)
    image = Column(String, nullable=True)

    status = Column(String(20), default='pending')
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    expires_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc) + timedelta(minutes=15))


    def __init__(self, **kwargs):
        if 'transaction_data' in kwargs:
            # Ensure transaction_data is JSON serializable
            kwargs['transaction_data'] = json.loads(
                json.dumps(kwargs['transaction_data'], default=make_json_serializable)
            )
        super().__init__(**kwargs)