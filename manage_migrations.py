#!/usr/bin/env python
"""
Alembic Migration Management Script

This script provides a convenient way to manage database migrations using Alembic.
"""

import argparse
import os
import subprocess
import sys

def run_command(command):
    """Run a shell command and print output"""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, text=True)
    if result.returncode != 0:
        print(f"Command failed with exit code {result.returncode}")
        sys.exit(result.returncode)
    return result

def create_migration(message, autogenerate=False):
    """Create a new migration"""
    cmd = f'alembic revision {"--autogenerate" if autogenerate else ""} -m "{message}"'
    run_command(cmd)

def upgrade(revision="head"):
    """Upgrade to a newer migration"""
    run_command(f"alembic upgrade {revision}")

def downgrade(revision="-1"):
    """Downgrade to an older migration"""
    run_command(f"alembic downgrade {revision}")

def show_current():
    """Show current migration version"""
    run_command("alembic current")

def show_history():
    """Show migration history"""
    run_command("alembic history --verbose")

def stamp(revision="head"):
    """Stamp the database with a specific revision without running migrations"""
    run_command(f"alembic stamp {revision}")

def main():
    parser = argparse.ArgumentParser(description="Alembic Migration Management Script")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # create command
    create_parser = subparsers.add_parser("create", help="Create a new migration")
    create_parser.add_argument("message", help="Migration message")
    create_parser.add_argument("--auto", action="store_true", help="Autogenerate migration based on model changes")

    # upgrade command
    upgrade_parser = subparsers.add_parser("upgrade", help="Upgrade to a newer migration")
    upgrade_parser.add_argument("revision", nargs="?", default="head", help="Revision to upgrade to (default: head)")

    # downgrade command
    downgrade_parser = subparsers.add_parser("downgrade", help="Downgrade to an older migration")
    downgrade_parser.add_argument("revision", nargs="?", default="-1", help="Revision to downgrade to (default: -1)")

    # current command
    current_parser = subparsers.add_parser("current", help="Show current migration version")

    # history command
    history_parser = subparsers.add_parser("history", help="Show migration history")

    # stamp command
    stamp_parser = subparsers.add_parser("stamp", help="Stamp the database with a specific revision")
    stamp_parser.add_argument("revision", nargs="?", default="head", help="Revision to stamp (default: head)")

    args = parser.parse_args()

    if args.command == "create":
        create_migration(args.message, args.auto)
    elif args.command == "upgrade":
        upgrade(args.revision)
    elif args.command == "downgrade":
        downgrade(args.revision)
    elif args.command == "current":
        show_current()
    elif args.command == "history":
        show_history()
    elif args.command == "stamp":
        stamp(args.revision)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
