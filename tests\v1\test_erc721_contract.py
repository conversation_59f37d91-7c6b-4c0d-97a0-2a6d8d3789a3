import pytest
import pytest_asyncio
import io
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from web3 import Web3
from httpx import AsyncClient
from redis.asyncio import Redis

from api.v1.deploy_contracts.models import ContractDeployment, ToBeSigned
from api.v1.deploy_contracts.services import TransactionType
from api.v1.erc721_contract.services import ERC721Interaction
from api.v1.erc721_contract.schemas import (
    Metadata, 
    ApprovalRequest, 
    TransferRequest, 
    ApprovalForAllRequest
)

# Test constants
TEST_ADDRESS = "0x742d35Cc6634C0532925a3b844Bc454e4438f44e"
TEST_RECIPIENT_ADDRESS = "0x123456789012345678901234567890123456789a"
TEST_CONTRACT_ADDRESS = "0x742d35Cc6634C0532925a3b844Bc454e4438f44e"
TEST_BLOCK_NUMBER = 1000000
TEST_TRANSACTION_HASH = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
TEST_TOKEN_ID = 1
TEST_METADATA_URI = "ipfs://QmTest123"

# Sample ABI for ERC721 contract
SAMPLE_ABI = [
    {
        "inputs": [],
        "name": "name",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "symbol",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "totalSupply",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "string", "name": "uri", "type": "string"}
        ],
        "name": "mint",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "uint256", "name": "tokenId", "type": "uint256"}
        ],
        "name": "approve",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "from", "type": "address"},
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "uint256", "name": "tokenId", "type": "uint256"}
        ],
        "name": "transferFrom",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "from", "type": "address"},
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "uint256", "name": "tokenId", "type": "uint256"}
        ],
        "name": "safeTransferFrom",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "operator", "type": "address"},
            {"internalType": "bool", "name": "approved", "type": "bool"}
        ],
        "name": "setApprovalForAll",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "renounceOwnership",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "newOwner", "type": "address"}],
        "name": "transferOwnership",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "tokensOfOwner",
        "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}],
        "name": "supportsInterface",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "view",
        "type": "function"
    }
]

class MockRedis:
    """Mock Redis for testing"""
    async def get(self, key):
        return None
    
    async def set(self, key, value, ex=None):
        return True
    
    async def delete(self, key):
        return True

@pytest_asyncio.fixture
async def test_rpc_web3():
    """Create a Web3 instance connected to a test RPC"""
    mock = AsyncMock()
    mock.eth = AsyncMock()
    mock.eth.chain_id = 1337  # Local test chain ID
    mock.eth.get_block_number = AsyncMock(return_value=TEST_BLOCK_NUMBER)
    mock.eth.get_transaction_count = AsyncMock(return_value=0)
    mock.eth.gas_price = AsyncMock(return_value=Web3.to_wei(50, 'gwei'))
    mock.eth.estimate_gas = AsyncMock(return_value=200000)
    
    # Mock contract instance
    mock_contract = AsyncMock()
    mock_contract.address = TEST_CONTRACT_ADDRESS
    mock_contract.functions = AsyncMock()
    
    # Mock contract functions
    mock_contract.functions.name = AsyncMock()
    mock_contract.functions.name.call = AsyncMock(return_value="Test NFT")
    
    mock_contract.functions.symbol = AsyncMock()
    mock_contract.functions.symbol.call = AsyncMock(return_value="TNFT")
    
    mock_contract.functions.totalSupply = AsyncMock()
    mock_contract.functions.totalSupply.call = AsyncMock(return_value=1)
    
    mock_contract.functions.balanceOf = AsyncMock()
    mock_contract.functions.balanceOf.call = AsyncMock(return_value=1)
    
    mock_contract.functions.tokensOfOwner = AsyncMock()
    mock_contract.functions.tokensOfOwner.call = AsyncMock(return_value=[TEST_TOKEN_ID])
    
    mock_contract.functions.supportsInterface = AsyncMock()
    mock_contract.functions.supportsInterface.call = AsyncMock(return_value=True)
    
    # Mock transaction functions
    mock_mint = AsyncMock()
    mock_mint.call = AsyncMock(return_value=TEST_TOKEN_ID)
    mock_mint.build_transaction = AsyncMock(return_value={
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'value': 0,
        'gas': 200000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'chainId': 1337
    })
    mock_contract.functions.mint = MagicMock(return_value=mock_mint)
    
    mock_approve = AsyncMock()
    mock_approve.call = AsyncMock(return_value=None)
    mock_approve.build_transaction = AsyncMock(return_value={
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'value': 0,
        'gas': 200000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'chainId': 1337
    })
    mock_contract.functions.approve = MagicMock(return_value=mock_approve)
    
    mock_transfer_from = AsyncMock()
    mock_transfer_from.call = AsyncMock(return_value=None)
    mock_transfer_from.build_transaction = AsyncMock(return_value={
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'value': 0,
        'gas': 200000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'chainId': 1337
    })
    mock_contract.functions.transferFrom = MagicMock(return_value=mock_transfer_from)
    
    mock_safe_transfer_from = AsyncMock()
    mock_safe_transfer_from.call = AsyncMock(return_value=None)
    mock_safe_transfer_from.build_transaction = AsyncMock(return_value={
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'value': 0,
        'gas': 200000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'chainId': 1337
    })
    mock_contract.functions.safeTransferFrom = MagicMock(return_value=mock_safe_transfer_from)
    
    mock_set_approval_for_all = AsyncMock()
    mock_set_approval_for_all.call = AsyncMock(return_value=None)
    mock_set_approval_for_all.build_transaction = AsyncMock(return_value={
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'value': 0,
        'gas': 200000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'chainId': 1337
    })
    mock_contract.functions.setApprovalForAll = MagicMock(return_value=mock_set_approval_for_all)
    
    mock_renounce_ownership = AsyncMock()
    mock_renounce_ownership.call = AsyncMock(return_value=None)
    mock_renounce_ownership.build_transaction = AsyncMock(return_value={
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'value': 0,
        'gas': 200000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'chainId': 1337
    })
    mock_contract.functions.renounceOwnership = MagicMock(return_value=mock_renounce_ownership)
    
    mock_transfer_ownership = AsyncMock()
    mock_transfer_ownership.call = AsyncMock(return_value=None)
    mock_transfer_ownership.build_transaction = AsyncMock(return_value={
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'value': 0,
        'gas': 200000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'chainId': 1337
    })
    mock_contract.functions.transferOwnership = MagicMock(return_value=mock_transfer_ownership)
    
    # Mock contract creation
    mock.eth.contract = MagicMock(return_value=mock_contract)
    
    # Mock transaction sending
    mock.eth.send_raw_transaction = AsyncMock(return_value=TEST_TRANSACTION_HASH)
    mock.eth.wait_for_transaction_receipt = AsyncMock(return_value={
        'transactionHash': TEST_TRANSACTION_HASH,
        'blockHash': '0x...',
        'blockNumber': TEST_BLOCK_NUMBER,
        'contractAddress': None,  # Not a contract creation
        'cumulativeGasUsed': 100000,
        'effectiveGasPrice': Web3.to_wei(50, 'gwei'),
        'gasUsed': 100000,
        'logs': [],
        'logsBloom': '0x...',
        'status': 1,  # Success
        'transactionIndex': 0
    })
    
    # Mock address conversion and utility functions
    mock.to_checksum_address = MagicMock(lambda addr: addr)
    mock.from_wei = MagicMock(lambda amount, unit: amount / 10**18 if unit == 'ether' else amount)
    mock.to_wei = MagicMock(lambda amount, unit: amount * 10**18 if unit == 'ether' else amount)
    
    return mock

@pytest_asyncio.fixture
async def mock_redis():
    return MockRedis()

@pytest_asyncio.fixture
async def test_erc721_contract(async_db, test_verified_user):
    """Create a test ERC721 contract deployment"""
    contract = ContractDeployment(
        user_id=test_verified_user["id"],
        contract_address=TEST_CONTRACT_ADDRESS,
        contract_type=TransactionType.ERC721,
        block_number=TEST_BLOCK_NUMBER,
        name="Test NFT",
        symbol="TNFT",
        created_at=datetime.now(timezone.utc)
    )
    async_db.add(contract)
    await async_db.commit()
    await async_db.refresh(contract)
    return contract

@pytest.mark.asyncio
async def test_get_nft_balance(client, test_verified_user, test_erc721_contract, test_rpc_web3, mock_redis):
    """Test getting NFT balance using test RPC"""
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.router.get_redis', return_value=mock_redis):
        
        response = await client.get(
            f"/nft/balance?contract_id={test_erc721_contract.id}&account_address={TEST_ADDRESS}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        assert "balance" in data
        assert data["balance"] == 1  # As mocked in test_rpc_web3

@pytest.mark.asyncio
async def test_get_total_supply(client, test_verified_user, test_erc721_contract, test_rpc_web3, mock_redis):
    """Test getting total supply of NFTs using test RPC"""
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.router.get_redis', return_value=mock_redis):
        
        # Create a custom endpoint for testing total supply
        response = await client.get(
            f"/nft/total-supply?contract_id={test_erc721_contract.id}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        assert "total_supply" in data
        assert data["total_supply"] == 1  # As mocked in test_rpc_web3

@pytest.mark.asyncio
async def test_get_all_nfts(client, test_verified_user, test_erc721_contract, test_rpc_web3, mock_redis):
    """Test getting all NFTs for a contract using test RPC"""
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.router.get_redis', return_value=mock_redis):
        
        response = await client.get(
            f"/nft/nfts?contract_id={test_erc721_contract.id}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        assert "body" in data
        # The actual structure depends on your implementation

@pytest.mark.asyncio
async def test_mint_nft(client, test_verified_user, test_erc721_contract, test_rpc_web3, mock_redis):
    """Test minting an NFT using test RPC"""
    # Create a test file for upload
    test_file_content = b"test nft image content"
    test_file = io.BytesIO(test_file_content)
    
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.router.get_redis', return_value=mock_redis), \
         patch('api.v1.erc721_contract.services.upload_image', return_value=("test_nft.png", "/path/to/test_nft.png")), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._initialize_wallet'), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._save_unsigned_transaction') as mock_save_unsigned:
        
        # Mock the save_unsigned_transaction to return a transaction ID
        mock_save_unsigned.return_value = {
            "signature_id": 1,
            "message": "NFT minting prepared. Please sign the transaction."
        }
        
        response = await client.post(
            f"/nft/mint?contract_id={test_erc721_contract.id}&default_wallet=true",
            files={"file": ("test_nft.png", test_file, "image/png")},
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data

@pytest.mark.asyncio
async def test_approve_nft(client, test_verified_user, test_erc721_contract, test_rpc_web3):
    """Test approving an NFT using test RPC"""
    approval_request = {
        "to_address": TEST_RECIPIENT_ADDRESS,
        "token_id": TEST_TOKEN_ID
    }
    
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._initialize_wallet'), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._save_unsigned_transaction') as mock_save_unsigned:
        
        # Mock the save_unsigned_transaction to return a transaction ID
        mock_save_unsigned.return_value = {
            "signature_id": 2,
            "message": "NFT approval prepared. Please sign the transaction."
        }
        
        response = await client.post(
            f"/nft/approve?contract_id={test_erc721_contract.id}&default_wallet=true",
            json=approval_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data

@pytest.mark.asyncio
async def test_transfer_nft(client, test_verified_user, test_erc721_contract, test_rpc_web3):
    """Test transferring an NFT using test RPC"""
    transfer_request = {
        "to_address": TEST_RECIPIENT_ADDRESS,
        "token_id": TEST_TOKEN_ID
    }
    
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._initialize_wallet'), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._save_unsigned_transaction') as mock_save_unsigned:
        
        # Mock the save_unsigned_transaction to return a transaction ID
        mock_save_unsigned.return_value = {
            "signature_id": 3,
            "message": "NFT transfer prepared. Please sign the transaction."
        }
        
        response = await client.post(
            f"/nft/transfer?contract_id={test_erc721_contract.id}&default_wallet=true",
            json=transfer_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data

@pytest.mark.asyncio
async def test_safe_transfer_nft(client, test_verified_user, test_erc721_contract, test_rpc_web3):
    """Test safe transferring an NFT using test RPC"""
    transfer_request = {
        "to_address": TEST_RECIPIENT_ADDRESS,
        "token_id": TEST_TOKEN_ID
    }
    
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._initialize_wallet'), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._save_unsigned_transaction') as mock_save_unsigned:
        
        # Mock the save_unsigned_transaction to return a transaction ID
        mock_save_unsigned.return_value = {
            "signature_id": 4,
            "message": "NFT safe transfer prepared. Please sign the transaction."
        }
        
        response = await client.post(
            f"/nft/safe-transfer?contract_id={test_erc721_contract.id}&default_wallet=true",
            json=transfer_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data

@pytest.mark.asyncio
async def test_set_approval_for_all(client, test_verified_user, test_erc721_contract, test_rpc_web3):
    """Test setting approval for all NFTs using test RPC"""
    approval_request = {
        "operator": TEST_RECIPIENT_ADDRESS,
        "approved": True
    }
    
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._initialize_wallet'), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._save_unsigned_transaction') as mock_save_unsigned:
        
        # Mock the save_unsigned_transaction to return a transaction ID
        mock_save_unsigned.return_value = {
            "signature_id": 5,
            "message": "NFT approval for all prepared. Please sign the transaction."
        }
        
        response = await client.post(
            f"/nft/set-approval-for-all?contract_id={test_erc721_contract.id}&default_wallet=true",
            json=approval_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data

@pytest.mark.asyncio
async def test_renounce_ownership(client, test_verified_user, test_erc721_contract, test_rpc_web3):
    """Test renouncing ownership of an NFT contract using test RPC"""
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._initialize_wallet'), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._save_unsigned_transaction') as mock_save_unsigned:
        
        # Mock the save_unsigned_transaction to return a transaction ID
        mock_save_unsigned.return_value = {
            "signature_id": 6,
            "message": "NFT ownership renouncement prepared. Please sign the transaction."
        }
        
        response = await client.post(
            f"/nft/renounce-ownership?contract_id={test_erc721_contract.id}&default_wallet=true",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data

@pytest.mark.asyncio
async def test_supports_interface(client, test_verified_user, test_erc721_contract, test_rpc_web3, mock_redis):
    """Test checking if contract supports an interface using test RPC"""
    with patch('api.v1.erc721_contract.router.get_web3', return_value=test_rpc_web3), \
         patch('api.v1.erc721_contract.router.get_redis', return_value=mock_redis):
        
        # Test with ERC721 interface ID
        response = await client.get(
            f"/nft/supports-interface?contract_id={test_erc721_contract.id}&interface_id=0x80ac58cd",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "success"
        assert "supports_interface" in data
        assert data["supports_interface"] is True  # As mocked in test_rpc_web3

@pytest.mark.asyncio
async def test_direct_erc721_interaction_class(async_db, test_verified_user, test_erc721_contract, test_rpc_web3, mock_redis):
    """Test direct interaction with the ERC721Interaction class using test RPC"""
    # This test directly uses the ERC721Interaction class without going through the API
    with patch('api.v1.erc721_contract.services.NFTWriteOperations._initialize_wallet'), \
         patch('api.v1.erc721_contract.services.NFTWriteOperations._save_unsigned_transaction') as mock_save_unsigned:
        
        # Mock the save_unsigned_transaction to return a transaction ID
        mock_save_unsigned.return_value = {
            "signature_id": 7,
            "message": "Transaction prepared. Please sign the transaction."
        }
        
        # Create an instance of ERC721Interaction
        contract_instance = await ERC721Interaction.create(
            db=async_db,
            user_id=test_verified_user["id"],
            contract_id=test_erc721_contract.id,
            contract_type=TransactionType.ERC721,
            web3=test_rpc_web3,
            redis=mock_redis
        )
        
        # Test view operations
        total_supply = await contract_instance.get_total_supply()
        assert total_supply == 1
        
        balance = await contract_instance.get_balance_of(TEST_ADDRESS)
        assert balance == 1
        
        tokens = await contract_instance.get_nfts_by_wallet()
        assert tokens == [TEST_TOKEN_ID]
        
        supports = await contract_instance.supports_interface("0x80ac58cd")
        assert supports is True
        
        # Test a write operation (approve)
        tx_details = await contract_instance.approve_nft(
            TEST_RECIPIENT_ADDRESS,
            TEST_TOKEN_ID,
            True  # default_wallet
        )
        
        assert "signature_id" in tx_details
        assert tx_details["signature_id"] == 7
