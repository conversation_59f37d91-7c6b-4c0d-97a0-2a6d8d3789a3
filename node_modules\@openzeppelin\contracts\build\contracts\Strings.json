{"_format": "hh-sol-artifact-1", "contractName": "Strings", "sourceName": "contracts/utils/Strings.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "StringsInsufficientHexLength", "type": "error"}, {"inputs": [], "name": "StringsInvalidAddressFormat", "type": "error"}, {"inputs": [], "name": "StringsInvalidChar", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f80fdfea264697066735822122042c3595ea00d1d1f7c78125284ebd5387bebfe8b7b51dc1069a32081f657cb7764736f6c63430008180033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f80fdfea264697066735822122042c3595ea00d1d1f7c78125284ebd5387bebfe8b7b51dc1069a32081f657cb7764736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}