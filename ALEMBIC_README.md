# Alembic Database Migration Guide

This guide explains how to use Alembic for database migrations in this project.

## Overview

Alembic is a database migration tool for SQLAlchemy. It allows you to:
- Track database schema changes
- Upgrade and downgrade your database schema
- Automatically generate migrations based on model changes

## Prerequisites

Make sure you have Alembic installed:

```bash
pip install alembic
```

## Basic Commands

### Create a New Migration

To create a new migration after changing your models:

```bash
python alembic_commands.py revision "Description of your changes"
```

This will:
1. Compare your models with the current database schema
2. Generate a new migration file in `migrations/versions/`
3. Include the necessary upgrade and downgrade operations

### Apply Migrations

To upgrade your database to the latest version:

```bash
python alembic_commands.py upgrade
```

To upgrade to a specific version:

```bash
python alembic_commands.py upgrade <revision_id>
```

### Downgrade Migrations

To downgrade your database by one version:

```bash
python alembic_commands.py downgrade
```

To downgrade to a specific version:

```bash
python alembic_commands.py downgrade <revision_id>
```

### View Migration Information

To see the current migration version:

```bash
python alembic_commands.py current
```

To see the migration history:

```bash
python alembic_commands.py history
```

## Best Practices

1. **Always review generated migrations** before applying them to ensure they do what you expect.
2. **Test migrations** in a development environment before applying to production.
3. **Commit migration files** to version control along with your model changes.
4. **Don't modify existing migrations** that have been applied - create new ones instead.
5. **Include meaningful messages** when creating migrations to document what changed.

## Troubleshooting

### Migration Not Detecting Changes

If Alembic isn't detecting your model changes:
- Make sure your model is imported in `migrations/env.py`
- Ensure your model inherits from `Base` defined in `api/db/database.py`
- Check that your model has `__tablename__` defined

### Migration Fails to Apply

If a migration fails to apply:
1. Check the error message for details
2. Fix any issues in your models or migration file
3. If needed, downgrade and then apply a fixed migration

## Project Structure

- `alembic.ini` - Alembic configuration file
- `migrations/` - Directory containing all migration files
  - `env.py` - Environment configuration for Alembic
  - `script.py.mako` - Template for migration files
  - `versions/` - Directory containing individual migration files
- `alembic_commands.py` - Helper script for running Alembic commands
