from typing import List, Dict, Any, Optional, <PERSON>ple
import pandas as pd
from datetime import datetime
import time
import math
import asyncio
from config import config
from fastapi import HTTPException, status
from api.v1.deploy_contracts.models import ContractDeployment
from sqlalchemy.ext.asyncio import AsyncSession
import json
from api.v1.account.models import Account as AccountWallet
from api.core.blockchain_dep import (class_exponential_backoff)

from web3.exceptions import BlockNotFound, TransactionNotFound, Web3Exception

from .exceptions import (logger, ContractError, DataProcessingError,
                         BlockchainError, CacheError)
from redis.asyncio import Redis
from web3 import AsyncWeb3
from sqlalchemy.future import select



PRODUCTION = config.PRODUCTION
PLATFORM_WALLET = config.PLATFORM_WALLET
CACHE_TIME = 600
CHUNCK_SIZE = 50
LOGS_CHUNK_SIZE = 10000
MAX_CONCURRENT_REQUESTS = 25  # Adjust based on API rate limits


@class_exponential_backoff()
class GeneralChart:

    async def __init__(self, db: AsyncSession, user_id: int,
                 web3: AsyncWeb3, redis: Optional[Redis] = None):
        self.db = db
        self.user_id = user_id
        self.contract_id = None
        self.redis = redis
        self.cache = CACHE_TIME
        self.web3 = web3
        self.chunk_size = CHUNCK_SIZE

        result = await db.execute(select(ContractDeployment).filter(
            ContractDeployment.user_id==self.user_id))
        self.contract = result.scalars().all()

        if not self.contract:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Contract not found")

        if PRODUCTION == True:
            result = await db.execute(select(AccountWallet).filter(AccountWallet.user_id == user_id))
            account = result.scalar_one_or_none()

            if not account:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"User does not have a vault")

            self.wallet = account.address
        else:
            self.wallet = PLATFORM_WALLET

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

    # EARNING CHART DATA:
    async def earnings_chart(self, freq):
        """
        Generates the earnings chart data for the specified frequency.
        """
        start_time = time.time()
        try:
            cache_key = f"earning_chart:{self.contract[0].contract_address}{self.user_id}"
            transactions_data = await self._get_cached_or_fetch_data(cache_key, self._process_earning)
            monthly_stats = await self._get_earning_dataframe(transactions_data, freq)
            frontend_data = self._prepare_earning_data(monthly_stats)

            logger.info(
                f"History chart generated successfully. "
                f"Execution time: {time.time() - start_time:.2f} seconds"
            )

            return {
                'status': 'success',
                'data': frontend_data
            }

        except CacheError as e:
            logger.error(f"Cache operation failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Cache service temporarily unavailable"
            )
        except BlockchainError as e:
            logger.error(f"Blockchain interaction failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="Failed to interact with blockchain"
            )
        except DataProcessingError as e:
            logger.error(f"Data processing failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process contract data"
            )
        except Exception as e:
            logger.critical(f"Unexpected error in history_chart: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred"
            )


    async def _process_earning(self, log):
        """Process individual log entry"""
        try:

            block, tx, balance, receipt = await asyncio.gather(
                self.web3.eth.get_block(log['blockNumber']),
                self.web3.eth.get_transaction(log['transactionHash']),
                self.web3.eth.get_balance(self.wallet, log['blockNumber']),
                self.web3.eth.get_transaction_receipt(log['transactionHash'])
            )

            balance = self.web3.from_wei(balance, 'ether')
            fee_in_wei = receipt.gasUsed * tx.gasPrice
            fee_in_matic = self.web3.from_wei(fee_in_wei, 'ether')
            return {
                'timestamp': datetime.fromtimestamp(block['timestamp']).isoformat(),
                'balance': float(balance),
                'network_earning': float(fee_in_matic)
            }

        except BlockNotFound:
            logger.warning(f"Block {log['blockNumber']} not found")
            return None
        except TransactionNotFound:
            logger.warning(f"Transaction {log['transactionHash'].hex()} not found")
            return None
        except Web3Exception as e:
            logger.error(f"Web3 error processing log: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error processing log: {str(e)}")
            return None


    async def _get_earning_dataframe(self, transactions_data, freq):
        """
        Optimized monthly statistics calculation using parallel processing
        and efficient pandas operations
        """
        try:
            if not transactions_data:
                return pd.DataFrame()

            df = pd.DataFrame(transactions_data)
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_localize('UTC')
            df = df.set_index('timestamp')

            deployed_stats = self._deployment_time(freq)

            stats = pd.DataFrame({
                'wallet_balance': df['balance'].resample(freq).last().fillna(method='ffill').fillna(0),
                'network_earnings': df['network_earning'].resample(freq).sum().fillna(0),
                'contracts_deployed': deployed_stats.astype('int32')
            })

            stats.ffill(inplace=True)
            return stats

        except Exception as e:
            logger.error(f"Failed to process transaction data: {str(e)}")
            raise DataProcessingError(f"Failed to process transaction data: {str(e)}")


    def _prepare_earning_data(self, time_series_stats):
        try:

            return [{
                'timestamp': index.isoformat(),
                'networkEarnings': float(row['network_earnings']) if pd.notna(row['network_earnings']) else 0.0,
                'contractsDeployed': int(row['contracts_deployed']),
                'walletBalance': float(row['wallet_balance']) if pd.notna(row['wallet_balance']) else 0.0
            } for index, row in time_series_stats.iterrows()][::-1]

        except Exception as e:
            logger.error(f"Failed to prepare history data: {str(e)}")
            raise DataProcessingError(f"Failed to prepare history data: {str(e)}")



    # HISTORY CHART DATA: No transaction data retrieved from any contract
    async def history_chart(self, freq):
        """
        Generates the history chart data for the specified frequency.
        """

        start_time = time.time()
        cache_key = f"history_chart:{self.contract[0].contract_address}{self.user_id}"
        try:
            transactions_data = await self._get_cached_or_fetch_data(cache_key,  self._process_history)
            monthly_stats, total_transactions, weekly_metrics = await self._get_history_dataframe(transactions_data, freq)
            frontend_data = self._prepare_history_data(monthly_stats)

            logger.info(
                f"History chart generated successfully. "
                f"Execution time: {time.time() - start_time:.2f} seconds"
            )

            return {
                'status': 'success',
                'data': frontend_data,
                'overall_totals': {
                    'totalTransactions': {
                        'total': total_transactions,
                        'weekly_rate_change': weekly_metrics['trx_rate_change'],
                    },
                    'totalContractsDeployed': {
                        'weekly_rate_change': weekly_metrics['contracts_rate_change'],
                    }
                }
            }

        except CacheError as e:
            logger.error(f"Cache operation failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Cache service temporarily unavailable"
            )
        except BlockchainError as e:
            logger.error(f"Blockchain interaction failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="Failed to interact with blockchain"
            )
        except DataProcessingError as e:
            logger.error(f"Data processing failed: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process contract data"
            )
        except Exception as e:
            logger.critical(f"Unexpected error in history_chart: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred"
            )



    async def _process_history(self, log):
        """Process individual log entry"""
        try:
            block = await self.web3.eth.get_block(log['blockNumber'])
            return {
                'timestamp': datetime.fromtimestamp(block['timestamp']).isoformat(),
                'transactionHash': log['transactionHash'].hex()}

        except BlockNotFound:
            logger.warning(f"Block {log['blockNumber']} not found")
            return None
        except TransactionNotFound:
            logger.warning(f"Transaction {log['transactionHash'].hex()} not found")
            return None
        except Web3Exception as e:
            logger.error(f"Web3 error processing log: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error processing log: {str(e)}")
            return None



    async def _get_history_dataframe(self, transaction_data, freq):

        try:
            if not transaction_data:
                return pd.DataFrame(), 0, {'trx_rate_change': 0, 'contracts_rate_change': 0}

            df = pd.DataFrame(transaction_data)
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_localize('UTC')
            df.set_index('timestamp', inplace=True)

            def calculate_period_stats(period_freq):
                deployed_stats = self._deployment_time(period_freq)
                return pd.DataFrame({
                    'transactions': df['transactionHash'].resample(period_freq).nunique().fillna(0),
                    'contracts_deployed': deployed_stats.astype('int32').fillna(0)
                })

            stats = calculate_period_stats(freq)
            weekly_stats = calculate_period_stats('W')
            stats.ffill(inplace=True)

            # Calculate week-over-week changes
            weekly_changes = weekly_stats.pct_change() * 100
            weekly_changes.fillna(0, inplace=True)

            # Get the latest changes
            latest_changes = weekly_changes.iloc[-1] if not weekly_changes.empty else pd.Series({
                'transactions': 0, 'contracts_deployed': 0
            })

            total_transactions = int(df['transactionHash'].nunique())

            weekly_metrics = {
                'trx_rate_change':  round(float(latest_changes['transactions']), 2),
                'contracts_rate_change': round(float(latest_changes['contracts_deployed']), 2),
            }

            return stats, total_transactions, weekly_metrics

        except Exception as e:
            raise DataProcessingError(f"{str(e)}")


    def _prepare_history_data(self, time_series_stats):
        """
        Convert final DataFrame into a list of dicts
        """
        try:

            return [{
                'timestamp': index.isoformat(),
                'uniqueContracts': int(row['contracts_deployed']) if pd.notna(row['contracts_deployed']) else 0,
                'transactions': int(row['transactions']) if pd.notna(row['transactions']) else 0
            } for index, row in time_series_stats.iterrows()][::-1]

        except Exception as e:
            logger.error(f"Failed to prepare history data: {str(e)}")
            raise DataProcessingError(f"{str(e)}")


    #GENERAL HELPERS

    async def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache with error handling"""
        if not self.redis:
            return None
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Cache data corruption detected: {str(e)}")
            await self.redis.delete(cache_key)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval failed: {str(e)}")
            return None

    async def _cache_data(self, cache_key: str, data: Any, cache_time: int) -> None:
        """Cache data with error handling"""
        if not self.redis:
            return None
        try:
            await self.redis.set(cache_key, json.dumps(data))
            await self.redis.expire(cache_key, cache_time)
        except Exception as e:
            logger.warning(f"Failed to cache data: {str(e)}")



    async def _get_cached_or_fetch_data(self, cache_key: str, processor) -> List[Dict[str, Any]]:
        """
        Retrieve data from cache or fetch from blockchain.

        Raises:
            CacheError: If cache operations fail
            BlockchainError: If blockchain operations fail
        """
        try:
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            transactions_data = []
            all_results = await asyncio.gather(
                *[self._fetch_logs(contract, processor) for contract in self.contract],
                return_exceptions=True
            )

            # Handle exceptions from gather
            for result in all_results:
                if isinstance(result, Exception):
                    logger.error(f"Error in contract processing: {str(result)}")
                    continue
                if result:
                    transactions_data.extend([event for event in result if event])

            if not transactions_data:
                logger.warning("No transaction data retrieved from any contract")
                return []

            await self._cache_data(cache_key, transactions_data, self.cache)
            return transactions_data

        except Exception as e:
            raise BlockchainError(f"Failed to retrieve contract data: {str(e)}")



    async def _fetch_logs(self, contract, processor) -> Optional[List[Dict[str, Any]]]:
        """
        Fetch and process contract events in chunks.

        Raises:
            BlockchainError: If blockchain operations fail
        """
        try:
            logs = await self._get_contract_logs(contract)
            all_results = []

            async with asyncio.Semaphore(MAX_CONCURRENT_REQUESTS):
                for i in range(0, len(logs), self.chunk_size):
                    chunk = logs[i:i + self.chunk_size]
                    chunk_results = await asyncio.gather(
                        *[processor(log) for log in chunk],
                        return_exceptions=True
                    )

                    # Filter out exceptions and None results
                    valid_results = [
                        result for result in chunk_results
                        if result and not isinstance(result, Exception)
                    ]
                    all_results.extend(valid_results)

                    if len(valid_results) < len(chunk):
                        logger.warning(
                            f"Some logs in chunk {i//self.chunk_size} failed processing: "
                            f"{len(chunk) - len(valid_results)} failures"
                        )

            return all_results if all_results else None

        except Exception as e:
            logger.error(f"Failed to fetch history for contract: {str(e)}")
            raise BlockchainError(f"Contract history fetch failed: {str(e)}")



    async def _get_contract_logs(self, contract):
        end_block = await self.web3.eth.get_block_number()
        start_block = contract.block_number if contract.block_number else (await self.web3.eth.get_block('earliest'))['number']
        if start_block > end_block:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contract has no activity"
            )
        async def fetch_chunk(chunk_start: int, chunk_end: int) -> List[Any]:
            filter_params = {
                'fromBlock': chunk_start,
                'toBlock': chunk_end,
                'address': contract.contract_address
            }
            try:
                return await self.web3.eth.get_logs(filter_params)
            except Exception as e:
                logger.warning(f"Error fetching logs for blocks {chunk_start}-{chunk_end}: {str(e)}")
                return []


        chunks = []
        for chunk_start in range(start_block, end_block + 1, LOGS_CHUNK_SIZE):
            chunk_end = min(chunk_start + LOGS_CHUNK_SIZE - 1, end_block)
            chunks.append((chunk_start, chunk_end))


        async with asyncio.Semaphore(MAX_CONCURRENT_REQUESTS):
            all_logs = []
            for i in range(0, len(chunks), MAX_CONCURRENT_REQUESTS):
                batch = chunks[i:i + MAX_CONCURRENT_REQUESTS]
                batch_result = await asyncio.gather(
                    *[fetch_chunk(start, end) for start, end in batch],
                    return_exceptions=True
                )
                for result in batch_result:
                    if isinstance(result, list):
                        all_logs.extend(result)

        if not all_logs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Contract has no activity"
            )
        return all_logs



    def _deployment_time(self, freq):
        try:
            deployment_time = [contract.created_at for contract in self.contract]
            dep_df = pd.DataFrame({'timestamp': deployment_time})
            dep_df['timestamp'] = pd.to_datetime(dep_df['timestamp'])
            dep_df = dep_df.set_index('timestamp')
            deployed_stats = dep_df.resample(freq).size().fillna(0)
            deployed_stats.name = 'contracts_deployed'

        except Exception as e:
            return None

        return deployed_stats
