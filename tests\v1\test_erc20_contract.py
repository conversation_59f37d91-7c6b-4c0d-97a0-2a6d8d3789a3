import pytest
import pytest_asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from web3 import AsyncWeb3, Web3
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from decimal import Decimal
import json

from api.v1.deploy_contracts.models import ContractDeployment, TransactionType, ToBeSigned
from api.v1.erc20_contract.services import ERC20Interaction
from api.core.logging_config import get_logger
from tests.v1.test_account import TEST_PROVIDER, TEST_ADDRESS, MockRedis

logger = get_logger(__name__)

# Test constants
TEST_CONTRACT_ADDRESS = "0x742d35Cc6634C0532925a3b844Bc454e4438f44e"
TEST_BLOCK_NUMBER = 1000000
TEST_TRANSACTION_HASH = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
TEST_PRIVATE_KEY = "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"
TEST_RECIPIENT_ADDRESS = "0x5B38Da6a701c568545dCfcB03FcB875f56beddC4"
TEST_SPENDER_ADDRESS = "0xAb8483F64d9C6d1EcF9b849Ae677dD3315835cb2"

# Sample ABI for ERC20 token
SAMPLE_ABI = [
    {
        "inputs": [],
        "name": "name",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "symbol",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "decimals",
        "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "totalSupply",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "account", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "owner", "type": "address"},
            {"internalType": "address", "name": "spender", "type": "address"}
        ],
        "name": "allowance",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "recipient", "type": "address"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"}
        ],
        "name": "transfer",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "spender", "type": "address"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"}
        ],
        "name": "approve",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "sender", "type": "address"},
            {"internalType": "address", "name": "recipient", "type": "address"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"}
        ],
        "name": "transferFrom",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "spender", "type": "address"},
            {"internalType": "uint256", "name": "addedValue", "type": "uint256"}
        ],
        "name": "increaseAllowance",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "spender", "type": "address"},
            {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}
        ],
        "name": "decreaseAllowance",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"}
        ],
        "name": "mint",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "payable",
        "type": "function"
    }
]

@pytest_asyncio.fixture
async def mock_web3():
    """Mock Web3 for testing"""
    mock = AsyncMock()
    mock.eth = AsyncMock()
    mock.eth.chain_id = 80001  # Polygon Mumbai testnet
    mock.eth.get_block_number = AsyncMock(return_value=TEST_BLOCK_NUMBER)
    mock.eth.get_transaction_count = AsyncMock(return_value=0)
    mock.eth.gas_price = AsyncMock(return_value=Web3.to_wei(50, 'gwei'))
    
    # Mock contract instance
    mock_contract = AsyncMock()
    mock_contract.address = TEST_CONTRACT_ADDRESS
    mock_contract.functions = AsyncMock()
    
    # Mock contract functions
    mock_contract.functions.name = AsyncMock()
    mock_contract.functions.name.call = AsyncMock(return_value="Test Token")
    
    mock_contract.functions.symbol = AsyncMock()
    mock_contract.functions.symbol.call = AsyncMock(return_value="TST")
    
    mock_contract.functions.decimals = AsyncMock()
    mock_contract.functions.decimals.call = AsyncMock(return_value=18)
    
    mock_contract.functions.totalSupply = AsyncMock()
    mock_contract.functions.totalSupply.call = AsyncMock(return_value=Web3.to_wei(1000000, 'ether'))
    
    mock_contract.functions.balanceOf = AsyncMock()
    mock_contract.functions.balanceOf.call = AsyncMock(return_value=Web3.to_wei(10000, 'ether'))
    
    mock_contract.functions.allowance = AsyncMock()
    mock_contract.functions.allowance.call = AsyncMock(return_value=Web3.to_wei(1000, 'ether'))
    
    # Mock transaction functions
    mock_transfer = AsyncMock()
    mock_transfer.estimate_gas = AsyncMock(return_value=100000)
    mock_transfer.build_transaction = AsyncMock(return_value={
        'chainId': 80001,
        'from': TEST_ADDRESS,
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'value': 0
    })
    mock_transfer._encode_transaction_data = AsyncMock(return_value='0x...')
    mock_contract.functions.transfer = AsyncMock(return_value=mock_transfer)
    
    mock_approve = AsyncMock()
    mock_approve.estimate_gas = AsyncMock(return_value=100000)
    mock_approve.build_transaction = AsyncMock(return_value={
        'chainId': 80001,
        'from': TEST_ADDRESS,
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'value': 0
    })
    mock_approve._encode_transaction_data = AsyncMock(return_value='0x...')
    mock_contract.functions.approve = AsyncMock(return_value=mock_approve)
    
    mock_transfer_from = AsyncMock()
    mock_transfer_from.estimate_gas = AsyncMock(return_value=100000)
    mock_transfer_from.build_transaction = AsyncMock(return_value={
        'chainId': 80001,
        'from': TEST_ADDRESS,
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'value': 0
    })
    mock_transfer_from._encode_transaction_data = AsyncMock(return_value='0x...')
    mock_contract.functions.transferFrom = AsyncMock(return_value=mock_transfer_from)
    
    mock_increase_allowance = AsyncMock()
    mock_increase_allowance.estimate_gas = AsyncMock(return_value=100000)
    mock_increase_allowance.build_transaction = AsyncMock(return_value={
        'chainId': 80001,
        'from': TEST_ADDRESS,
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'value': 0
    })
    mock_increase_allowance._encode_transaction_data = AsyncMock(return_value='0x...')
    mock_contract.functions.increaseAllowance = AsyncMock(return_value=mock_increase_allowance)
    
    mock_decrease_allowance = AsyncMock()
    mock_decrease_allowance.estimate_gas = AsyncMock(return_value=100000)
    mock_decrease_allowance.build_transaction = AsyncMock(return_value={
        'chainId': 80001,
        'from': TEST_ADDRESS,
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'value': 0
    })
    mock_decrease_allowance._encode_transaction_data = AsyncMock(return_value='0x...')
    mock_contract.functions.decreaseAllowance = AsyncMock(return_value=mock_decrease_allowance)
    
    mock_mint = AsyncMock()
    mock_mint.estimate_gas = AsyncMock(return_value=100000)
    mock_mint.build_transaction = AsyncMock(return_value={
        'chainId': 80001,
        'from': TEST_ADDRESS,
        'to': TEST_CONTRACT_ADDRESS,
        'data': '0x...',
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'value': Web3.to_wei(0.001, 'ether')  # Platform fee
    })
    mock_mint._encode_transaction_data = AsyncMock(return_value='0x...')
    mock_contract.functions.mint = AsyncMock(return_value=mock_mint)
    
    # Mock contract creation
    mock.eth.contract = MagicMock(return_value=mock_contract)
    
    # Mock transaction sending
    mock.eth.send_raw_transaction = AsyncMock(return_value=TEST_TRANSACTION_HASH)
    mock.eth.wait_for_transaction_receipt = AsyncMock(return_value={
        'transactionHash': TEST_TRANSACTION_HASH,
        'blockHash': '0x...',
        'blockNumber': TEST_BLOCK_NUMBER,
        'contractAddress': None,  # Not a contract creation
        'cumulativeGasUsed': 100000,
        'effectiveGasPrice': Web3.to_wei(50, 'gwei'),
        'gasUsed': 100000,
        'logs': [],
        'logsBloom': '0x...',
        'status': 1,  # Success
        'transactionIndex': 0
    })
    
    # Mock utility functions
    mock.to_checksum_address = MagicMock(lambda addr: addr)
    mock.to_wei = MagicMock(lambda amount, unit: int(amount * 10**18) if unit == 'ether' else amount)
    mock.from_wei = MagicMock(lambda amount, unit: amount / 10**18 if unit == 'ether' else amount)
    
    return mock

@pytest_asyncio.fixture
async def mock_redis():
    return MockRedis()

@pytest_asyncio.fixture
async def test_erc20_contract(async_db, test_verified_user):
    """Create a test ERC20 contract deployment"""
    contract = ContractDeployment(
        user_id=test_verified_user["id"],
        contract_address=TEST_CONTRACT_ADDRESS,
        contract_type=TransactionType.ER20,
        block_number=TEST_BLOCK_NUMBER,
        created_at=datetime.now(timezone.utc)
    )
    async_db.add(contract)
    await async_db.commit()
    await async_db.refresh(contract)
    return contract

@pytest.mark.asyncio
async def test_get_token_name(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis):
    """Test getting token name"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        response = await client.get(
            f"/token/name?contract_id={test_erc20_contract.id}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data == "Test Token"

@pytest.mark.asyncio
async def test_get_token_symbol(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis):
    """Test getting token symbol"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        response = await client.get(
            f"/token/symbol?contract_id={test_erc20_contract.id}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data == "TST"

@pytest.mark.asyncio
async def test_get_token_decimals(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis):
    """Test getting token decimals"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        response = await client.get(
            f"/token/decimals?contract_id={test_erc20_contract.id}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data == 18

@pytest.mark.asyncio
async def test_get_total_supply(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis):
    """Test getting token total supply"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        response = await client.get(
            f"/token/total-supply?contract_id={test_erc20_contract.id}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data == 1000000.0  # Converted from wei to ether

@pytest.mark.asyncio
async def test_get_owned_by_user(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis):
    """Test getting tokens owned by user"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        response = await client.get(
            f"/token/owned-by-user?contract_id={test_erc20_contract.id}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data == 10000.0  # Converted from wei to ether

@pytest.mark.asyncio
async def test_get_balance(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis):
    """Test getting token balance for an address"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        response = await client.get(
            f"/token/balance?contract_id={test_erc20_contract.id}&account_address={TEST_RECIPIENT_ADDRESS}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data == 10000.0  # Converted from wei to ether

@pytest.mark.asyncio
async def test_get_allowance(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis):
    """Test getting token allowance"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        response = await client.get(
            f"/token/allowance?contract_id={test_erc20_contract.id}&spender_address={TEST_SPENDER_ADDRESS}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data == 1000.0  # Converted from wei to ether

@pytest.mark.asyncio
async def test_mint_token(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis, async_db):
    """Test minting tokens"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        mint_request = {
            "recipient_address": TEST_RECIPIENT_ADDRESS,
            "amount": 100.0
        }
        
        response = await client.post(
            f"/token/mint?contract_id={test_erc20_contract.id}&default_wallet=true",
            json=mint_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "status" in data
        assert data["status"] == "pending"
        
        # Verify a ToBeSigned record was created
        result = await async_db.execute(
            select(ToBeSigned).where(ToBeSigned.id == data["signature_id"])
        )
        signature = result.scalar_one_or_none()
        assert signature is not None
        
        # Verify operation data
        operation = json.loads(signature.operation)
        assert operation["type"] == "erc20"
        assert operation["transaction"] == "mint"
        assert operation["metadata"]["transaction_details"]["amount"] == "100.0"
        assert operation["metadata"]["transaction_details"]["to_address"] == TEST_RECIPIENT_ADDRESS

@pytest.mark.asyncio
async def test_transfer_token(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis, async_db):
    """Test transferring tokens"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        transfer_request = {
            "recipient_address": TEST_RECIPIENT_ADDRESS,
            "amount": 50.0
        }
        
        response = await client.post(
            f"/token/transfer?contract_id={test_erc20_contract.id}&default_wallet=true",
            json=transfer_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "status" in data
        assert data["status"] == "pending"
        
        # Verify a ToBeSigned record was created
        result = await async_db.execute(
            select(ToBeSigned).where(ToBeSigned.id == data["signature_id"])
        )
        signature = result.scalar_one_or_none()
        assert signature is not None
        
        # Verify operation data
        operation = json.loads(signature.operation)
        assert operation["type"] == "erc20"
        assert operation["transaction"] == "transfer"
        assert operation["metadata"]["transaction_details"]["amount"] == "50.0"
        assert operation["metadata"]["transaction_details"]["to_address"] == TEST_RECIPIENT_ADDRESS

@pytest.mark.asyncio
async def test_approve_token(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis, async_db):
    """Test approving token spending"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        approval_request = {
            "spender_address": TEST_SPENDER_ADDRESS,
            "amount": 200.0
        }
        
        response = await client.post(
            f"/token/approve?contract_id={test_erc20_contract.id}&default_wallet=true",
            json=approval_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "status" in data
        assert data["status"] == "pending"
        
        # Verify a ToBeSigned record was created
        result = await async_db.execute(
            select(ToBeSigned).where(ToBeSigned.id == data["signature_id"])
        )
        signature = result.scalar_one_or_none()
        assert signature is not None

@pytest.mark.asyncio
async def test_transfer_from(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis, async_db):
    """Test transferring tokens on behalf of another address"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        transfer_from_request = {
            "sender_address": TEST_ADDRESS,
            "recipient_address": TEST_RECIPIENT_ADDRESS,
            "amount": 30.0
        }
        
        response = await client.post(
            f"/token/transfer-from?contract_id={test_erc20_contract.id}&default_wallet=true",
            json=transfer_from_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "status" in data
        assert data["status"] == "pending"
        
        # Verify a ToBeSigned record was created
        result = await async_db.execute(
            select(ToBeSigned).where(ToBeSigned.id == data["signature_id"])
        )
        signature = result.scalar_one_or_none()
        assert signature is not None

@pytest.mark.asyncio
async def test_increase_allowance(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis, async_db):
    """Test increasing token allowance"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        increase_allowance_request = {
            "spender_address": TEST_SPENDER_ADDRESS,
            "added_value": 100.0
        }
        
        response = await client.post(
            f"/token/increase-allowance?contract_id={test_erc20_contract.id}&default_wallet=true",
            json=increase_allowance_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "status" in data
        assert data["status"] == "pending"
        
        # Verify a ToBeSigned record was created
        result = await async_db.execute(
            select(ToBeSigned).where(ToBeSigned.id == data["signature_id"])
        )
        signature = result.scalar_one_or_none()
        assert signature is not None

@pytest.mark.asyncio
async def test_decrease_allowance(client, test_verified_user, test_erc20_contract, mock_web3, mock_redis, async_db):
    """Test decreasing token allowance"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services_sign.TokenBase._initialize_contract') as mock_init_contract:
        
        mock_init_wallet.return_value = None
        mock_init_contract.return_value = None
        
        decrease_allowance_request = {
            "spender_address": TEST_SPENDER_ADDRESS,
            "subtracted_value": 50.0
        }
        
        response = await client.post(
            f"/token/decrease-allowance?contract_id={test_erc20_contract.id}&default_wallet=true",
            json=decrease_allowance_request,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "status" in data
        assert data["status"] == "pending"
        
        # Verify a ToBeSigned record was created
        result = await async_db.execute(
            select(ToBeSigned).where(ToBeSigned.id == data["signature_id"])
        )
        signature = result.scalar_one_or_none()
        assert signature is not None
