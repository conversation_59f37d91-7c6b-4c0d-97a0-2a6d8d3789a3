import os
import base64
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PB<PERSON>DF2HMAC
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography.hazmat.backends import default_backend
import logging

logger = logging.getLogger(__name__)

MASTER_ENCRYPTION_KEY = os.environ.get("MASTER_ENCRYPTION_KEY")
PLATFORM_PRIVATE_KEY_SECURE = os.environ.get("PLATFORM_PRIVATE_KEY")

if not MASTER_ENCRYPTION_KEY:
    MASTER_ENCRYPTION_KEY = "12345678901234567890123456789012" # Default key for testing purposes
    #logger.critical("CRITICAL SECURITY WARNING: MASTER_ENCRYPTION_KEY is not set!")

PBKDF2_ITERATIONS = 390000
SALT_SIZE_BYTES = 16
NONCE_SIZE_BYTES = 12
AES_KEY_SIZE_BYTES = 32

class WalletSecurity:

    def derive_key_from_master(self, master_key_bytes: bytes, salt: bytes) -> bytes:
        """Derives a cryptographic key from the master key using PBKDF2."""
        if not master_key_bytes:
            raise ValueError("Master key cannot be empty.")
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=AES_KEY_SIZE_BYTES,
            salt=salt,
            iterations=PBKDF2_ITERATIONS,
            backend=default_backend()
        )
        return kdf.derive(master_key_bytes)


    def encrypt_data_with_master_key(self, data_str: str) -> dict:
        """
        Encrypts a string using AES-GCM with a key derived from the MASTER_ENCRYPTION_KEY.
        """
        if not MASTER_ENCRYPTION_KEY:
            raise ValueError("MASTER_ENCRYPTION_KEY is not configured.")
        if not isinstance(data_str, str):
            raise TypeError("Data to encrypt must be a string.")

        master_key_bytes = MASTER_ENCRYPTION_KEY.encode('utf-8')
        data_bytes = data_str.encode('utf-8')

        salt = os.urandom(SALT_SIZE_BYTES)

        derived_key = self.derive_key_from_master(master_key_bytes, salt)

        nonce = os.urandom(NONCE_SIZE_BYTES)

        aesgcm = AESGCM(derived_key)
        ciphertext = aesgcm.encrypt(nonce, data_bytes, None)

        return {
            "salt": base64.b64encode(salt).decode('utf-8'),
            "nonce": base64.b64encode(nonce).decode('utf-8'),
            "ciphertext": base64.b64encode(ciphertext).decode('utf-8'),
        }

    def decrypt_data_with_master_key(self, encrypted_package: dict) -> str:
        """
        Decrypts data using the MASTER_ENCRYPTION_KEY and the stored salt/nonce/ciphertext.
        """
        if not MASTER_ENCRYPTION_KEY:
            raise ValueError("MASTER_ENCRYPTION_KEY is not configured.")
        if not all(k in encrypted_package for k in ["salt", "nonce", "ciphertext"]):
            raise ValueError("Encrypted data package is missing required keys.")
        if not all(encrypted_package.values()):
            raise ValueError("Encrypted data package contains empty values.")

        try:
            salt = base64.b64decode(encrypted_package["salt"])
            nonce = base64.b64decode(encrypted_package["nonce"])
            ciphertext = base64.b64decode(encrypted_package["ciphertext"])
            master_key_bytes = MASTER_ENCRYPTION_KEY.encode('utf-8')
        except (TypeError, ValueError) as e:
            logger.error(f"Error decoding base64 data during decryption: {e}")
            raise ValueError(f"Invalid base64 encoding in encrypted data: {e}") from e

        derived_key = self.derive_key_from_master(master_key_bytes, salt)

        aesgcm = AESGCM(derived_key)
        try:
            decrypted_bytes = aesgcm.decrypt(nonce, ciphertext, None)
            return decrypted_bytes.decode('utf-8')
        except Exception as e:
            logger.error(f"Decryption failed. Master key mismatch or data corrupted? Error: {e}")
            raise ValueError("Decryption failed. Invalid master key or corrupted data.") from e


    def get_platform_private_key(self) -> str:
        """Retrieves the platform private key from its secure source."""
        if not PLATFORM_PRIVATE_KEY_SECURE:
            logger.critical("CRITICAL SECURITY WARNING: PLATFORM_PRIVATE_KEY is not set securely!")
            raise ValueError("PLATFORM_PRIVATE_KEY environment variable is not set.") # Uncomment for production81-
        return PLATFORM_PRIVATE_KEY_SECURE