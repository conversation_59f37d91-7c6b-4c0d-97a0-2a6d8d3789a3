from typing import Union, Annotated
from fastapi.security import OA<PERSON>2PasswordBearer
from fastapi import Depends, <PERSON><PERSON>, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.sql import and_
from jose import JWTError

from api.v1.auth import schemas as auth_schema
from api.v1.user import schemas as user_schema
from api.v1.auth.services import Auth
from api.v1.user.services import UserService
from api.v1.user.models import User as UserModel
from api.v1.auth.models import APIKey
from api.db.database import get_db
from api.core import responses

#oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

async def not_authenticated(
    access_credentials: Union[str, auth_schema.Token, auth_schema.APIAuth] = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db),
) -> Union[user_schema.User, JWTError]:

    if not access_credentials:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,
                            detail=responses.INVALID_CREDENTIALS,
                            headers={"WWW-Authenticate": "Bearer"},
                            )

    authService = Auth()

    if type(access_credentials) == str:

        access_token_info = await authService.verify_access_token(access_credentials, db)

        if type(access_token_info) is JWTError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=responses.INVALID_CREDENTIALS)

        user = await UserService.fetch(id=access_token_info.id, db=db)
        #user.app_id = None

    if type(access_credentials) == auth_schema.APIAuth:
        user = await authService.authenticate_api_key(db=db, key=access_credentials.api_key)
        #user.app_id=access_credentials.app_id

    return user


async def is_authenticated(
    user = Depends(not_authenticated)
):
    """
    Verified user dependency - only allows email-verified users
    """
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email not verified. Please verify your email to continue."
        )
    return user

"""
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")

def is_authenticated(access_credentials: Annotated[str, Depends(oauth2_scheme)], db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    authService = Auth()

    access_token_info = authService.verify_access_token(access_credentials, db)
    user = UserService.fetch(id=access_token_info.id, db=db)
    if not user:
        raise credentials_exception
    return user

"""
