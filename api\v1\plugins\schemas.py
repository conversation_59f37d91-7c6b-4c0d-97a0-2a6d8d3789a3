from pydantic import BaseModel, model_validator, EmailStr, ValidationError, ConfigDict
from pydantic import BaseModel, Field, field_validator
from .models import RWAType as RWATypeEnum, RWAStatus as RWAStatusEnum
from typing import Optional
from datetime import datetime
from api.v1.user.models import User
from fastapi import HTTPException, status
from api.core import responses

from typing import Optional, List
from datetime import datetime
from enum import Enum

class CreateRealWorldAssetRequest(BaseModel):
    name: str = Field(..., min_length=3, max_length=255, description="Name of the Real World Asset")
    description: Optional[str] = Field(None, max_length=1000)
    #asset_type: RWATypeEnum
    initial_value: float = Field(..., gt=0, description="Initial valuation of the asset")

    # Optional Tokenization Details
    total_tokens: Optional[int] = Field(None, gt=0)
    token_price: Optional[float] = Field(None, gt=0)
    #token_standard: Optional[str] = Field(None, description="Blockchain token standard")


class RealWorldAssetResponse(BaseModel):
    id: int
    name: str
    description: Optional[str]
    asset_type: RWATypeEnum
    initial_value: float
    current_value: Optional[float]
    status: RWAStatusEnum

    # Blockchain Details
    blockchain_contract_address: Optional[str]
    token_standard: Optional[str]
    total_tokens: Optional[int]
    tokens_available: Optional[int]
    token_price: Optional[float]

    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class AssetVerificationRequest(BaseModel):
    asset_id: int
    verification_type: str = Field(..., description="Type of verification")
    verification_document_url: str = Field(..., description="URL of verification document")

class AssetVerificationResponse(BaseModel):
    id: int
    asset_id: int
    verifier_id: int
    verification_type: str
    verification_status: str
    verified_at: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)

class RWATokenizationRequest(BaseModel):
    asset_id: int
    blockchain_network: str = Field(..., description="Blockchain network for tokenization")
    token_standard: str = Field(..., description="Token standard to use")
    total_tokens: int = Field(..., gt=0, description="Total number of tokens to create")
    token_price: float = Field(..., gt=0, description="Price per token")

    @field_validator('blockchain_network')
    def validate_blockchain_network(cls, v):
        supported_networks = ['ethereum', 'polygon', 'binance_smart_chain']
        if v.lower() not in supported_networks:
            raise ValueError(f'Unsupported blockchain network. Supported: {supported_networks}')
        return v.lower()