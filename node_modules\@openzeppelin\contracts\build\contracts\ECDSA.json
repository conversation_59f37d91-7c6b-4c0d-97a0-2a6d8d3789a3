{"_format": "hh-sol-artifact-1", "contractName": "ECDSA", "sourceName": "contracts/utils/cryptography/ECDSA.sol", "abi": [{"inputs": [], "name": "ECDSAInvalidSignature", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "length", "type": "uint256"}], "name": "ECDSAInvalidSignatureLength", "type": "error"}, {"inputs": [{"internalType": "bytes32", "name": "s", "type": "bytes32"}], "name": "ECDSAInvalidSignatureS", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220e89cd2162008caf70e5f75f1508efb42247af19c9e631fce751e038839e005ea64736f6c63430008180033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220e89cd2162008caf70e5f75f1508efb42247af19c9e631fce751e038839e005ea64736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}