from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String, DateTime, BIGINT
from sqlalchemy.orm import relationship
from datetime import datetime, date
from api.db.database import Base


class User(Base):
    __tablename__ = "user"
    id = Column(Integer, primary_key=True, autoincrement=True, index=True, unique=True)
    first_name = Column(String(255), nullable=True)
    last_name = Column(String(255), nullable=True)
    email = Column(String(500), index=True, nullable=False)
    password = Column(String(500), nullable=False)
    google_sub = Column(String, unique=True, nullable=True)
    is_active = Column(Boolean, default=False)
    verification_token = Column(String(255), nullable=True)
    verification_token_expiry = Column(DateTime(timezone=True), nullable=True)

    reset_token = Column(String(255), nullable=True)
    reset_token_expiry = Column(DateTime(timezone=True), nullable=True)

    date_created = Column(DateTime(timezone=True), default=datetime.now)
    last_updated = Column(DateTime(timezone=True), default=datetime.now)
    is_deleted = Column(Boolean, default=False)

    #vault = relationship("Account", back_populates="owner")
    accounts = relationship("Account", back_populates="user")
    notifications = relationship("Notification", back_populates="user") #changed notification to notifications


class Avater(Base):
    __tablename__ = "users_avatar"
    id = Column(Integer, primary_key=True, autoincrement=True, index=True, unique=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False, index=True)
    avatar_url = Column(String(500), nullable=True)
    date_created = Column(DateTime(timezone=True), default=datetime.now)
    last_updated = Column(DateTime(timezone=True), default=datetime.now)
    is_deleted = Column(Boolean, default=False)


class NotificationPreference(Base):
    __tablename__ = "notificaiton_prefrence"
    id = Column(Integer, primary_key=True, autoincrement=True, index=True, unique=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False, index=True)
    email_notification = Column(Boolean, default=True)
    sms_notification = Column(Boolean, default=True)
    push_notification = Column(Boolean, default=True)
    date_created = Column(DateTime(timezone=True), default=datetime.now)
    last_updated = Column(DateTime(timezone=True), default=datetime.now)
    is_deleted = Column(Boolean, default=False)




