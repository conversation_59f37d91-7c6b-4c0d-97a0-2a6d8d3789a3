import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from api.db.database import Base, get_db
from config import config
from api.core.logging_config import get_logger
from main import app
from typing import AsyncGenerator, Generator
from httpx import ASGITransport, AsyncClient
from sqlalchemy import select
from api.v1.user.models import User as UserModel
from api.v1.subscription.models import SubscriptionPlan, SubscriptionTier, user_subscription
from sqlalchemy.orm import sessionmaker, declarative_base
import asyncio
from unittest.mock import patch
from datetime import datetime, timedelta, timezone

logger = get_logger(__name__)


def create_async_db_url() -> str:
    db_url = config.REPLICA_DATABASE_URL

    if not db_url:
        raise ValueError("Test database URL (REPLICA_DATABASE_URL) is missing in config.")
    if not db_url.startswith("postgresql+asyncpg://"):
        raise ValueError("Database URL does not use asyncpg driver. Update config.")

    return db_url


@pytest_asyncio.fixture(scope="function")
async def async_engine():
    """Creates a session-scoped test database engine."""
    engine = create_async_engine(create_async_db_url())
    logger.info("Test database engine created.")

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
    logger.info("Test database tables created (session scope).")

    yield engine

    logger.info("Disposing test database engine...")
    await engine.dispose()
    logger.info("Test database engine disposed.")



@pytest_asyncio.fixture(scope="function")
def async_session_factory(async_engine):
    return async_sessionmaker(
        bind=async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
        autocommit=False,
        autoflush=False,
    )


@pytest_asyncio.fixture(scope="function")
async def async_db(async_session_factory):
    async with async_session_factory() as session:
        await session.begin()
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.rollback()
            await session.close()
"""
@pytest.fixture
async def client():
    ""Provides an async client for tests.""
    transport = ASGITransport(app=app)
    client = AsyncClient(
        transport=transport,
        base_url="http://localhost:8000",
        follow_redirects=True
    )
    logger.info("AsyncClient created and yielded.")
    yield client
    await client.aclose()
"""


@pytest_asyncio.fixture(scope="function")
async def client(async_db: AsyncSession) -> AsyncClient:

    def override_get_db():

        yield async_db

    app.dependency_overrides[get_db] = override_get_db
    transport = ASGITransport(app=app)


    return AsyncClient(
        transport=transport,
        base_url="http://localhost:8000",
        follow_redirects=True
    )

"""
@pytest.fixture(scope='session')
def event_loop():
    asyncio.get_event_loop_policy().set_event_loop(asyncio.new_event_loop())
    loop = asyncio.get_event_loop()
    yield loop
    loop.close()
"""
import uuid

def get_unique_test_user_data():
    unique_id = str(uuid.uuid4())[:8]
    return {
        "first_name": "Test",
        "last_name": "User",
        "email": f"test.user.{unique_id}@example.com",
        "password": "Password123"
    }




@pytest_asyncio.fixture
async def test_user(client, async_db: AsyncSession):
    """Fixture to sign up a user before a test and return the user data from db."""
    user_data = get_unique_test_user_data()

    with patch('api.v1.auth.services_email.EmailVerification.send_email') as mock_send_email:
        mock_send_email.return_value = True

        response = await client.post("/auth/signup", json=user_data)

        if response.status_code != 201:
            logger.error(f"Fixture 'test_user' failed signup. Status: {response.status_code}, Response: {response.text}")

        query = select(UserModel).where(UserModel.email == user_data["email"])
        result = await async_db.execute(query)
        user = result.scalar_one_or_none()

        if not user:
            pytest.fail(f"User with email {user_data['email']} not found in database after fixture signup.")

        return {
            "email": user.email,
            "password": user_data["password"],
            "verification_code": user.verification_token,
            "id": user.id,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "is_active": user.is_active
        }


@pytest_asyncio.fixture
async def basic_subscription_plan(async_db: AsyncSession):
    """Create a basic subscription plan for testing."""
    basic_plan = SubscriptionPlan(
        tier=SubscriptionTier.BASIC,
        name="Basic Test Plan",
        description="Basic plan for testing",
        price_monthly=0,
        price_yearly=0,
        api_rate_limit=60,  # 60 requests per minute
        daily_request_limit=1000,
        monthly_request_limit=10000,
        allowed_endpoints=["*"],  # All endpoints allowed
        max_payload_size=1024,  # 1MB
        has_priority_support=False,
        has_advanced_features=False,
        concurrent_requests=5,
        max_response_time=1000  # 1 second
    )
    
    async_db.add(basic_plan)
    await async_db.commit()
    await async_db.refresh(basic_plan)
    return basic_plan


@pytest_asyncio.fixture
async def test_verified_user(client, test_user, async_db: AsyncSession, basic_subscription_plan):
    """Fixture to create a verified user with a basic subscription."""
    # First verify the user's email to activate the account
    verification_data = {
        "email": test_user["email"],
        "verification_code": test_user["verification_code"]
    }

    response = await client.post("/auth/verify/confirm", json=verification_data)
    if response.status_code == 200:
        response_json = response.json()
        
        # Create user subscription
        subscription = user_subscription(
            user_id=test_user["id"],
            subscription_plan_id=basic_subscription_plan.id,
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=365),  # 1 year subscription
            status="active"
        )
        
        async_db.add(subscription)
        await async_db.commit()
        await async_db.refresh(subscription)
        
        return {
            "access_token": response_json["access_token"],
            "email": response_json["data"]["email"],
            "password": test_user["password"],
            "id": test_user["id"],
            "subscription_id": subscription.id,
            "subscription_plan_id": basic_subscription_plan.id
        }

