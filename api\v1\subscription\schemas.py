from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List
from enum import Enum
from pydantic import BaseModel, Field
from typing import List, Literal




class SubscriptionTier(str, Enum):
    BASIC = "BASIC"
    PROFESSIONAL = "PROFESSIONAL"
    ENTERPRISE = "ENTERPRISE"

class SubscriptionPlanBase(BaseModel):
    id: int
    tier: SubscriptionTier
    name: str = Field(..., max_length=255)
    description: str = None
    price_monthly: int = Field(..., ge=0)
    price_yearly: int = Field(..., ge=0)
    api_rate_limit: int = Field(..., ge=0)
    daily_request_limit: int = Field(..., ge=0)
    monthly_request_limit: int = Field(..., ge=0)
    allowed_endpoints: List[str] = Field(default_factory=list)
    max_payload_size: int = Field(..., ge=0)
    has_priority_support: bool = False
    has_advanced_features: bool = False
    concurrent_requests: int = Field(..., ge=0)
    max_response_time: int = Field(..., ge=0)  # in milliseconds

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True



class SubscriptionPlanCreate(BaseModel):
    #tier: SubscriptionTier
    name: str = Field(..., min_length=3, max_length=255)
    description: Optional[str] = None
    price_monthly: int = Field(..., ge=0)
    price_yearly: int = Field(..., ge=0)
    
    # API Limits
    api_rate_limit: int = Field(..., ge=0)
    daily_request_limit: int = Field(..., ge=0)
    monthly_request_limit: int = Field(..., ge=0)
    allowed_endpoints: Optional[List[str]] = None
    max_payload_size: int = Field(..., ge=0)
    
    # Features
    has_priority_support: Optional[bool] = False
    has_advanced_features: Optional[bool] = False
    concurrent_requests: int = Field(..., ge=0)
    max_response_time: int = Field(..., ge=0)


class SubscriptionPlanPricing(BaseModel):
    thirty_days: int
    sixty_days: int

"""
# And update the subscription plan response if needed
class SubscriptionPlanResponse(SubscriptionPlanBase):
    pricing: SubscriptionPlanPricing

    @validator('pricing', pre=True)
    def create_pricing(cls, v, values):
        return SubscriptionPlanPricing(
            thirty_days=values['price_monthly'],
            sixty_days=values['price_yearly']
        )
"""



from pydantic import BaseModel, Field
from typing import List, Optional

class SubscriptionPlanUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=3, max_length=255)
    description: Optional[str] = None
    price_monthly: Optional[int] = Field(None, ge=0)
    price_yearly: Optional[int] = Field(None, ge=0)
    
    # API Limits
    api_rate_limit: Optional[int] = Field(None, ge=0)
    daily_request_limit: Optional[int] = Field(None, ge=0)
    monthly_request_limit: Optional[int] = Field(None, ge=0)
    allowed_endpoints: Optional[List[str]] = None
    max_payload_size: Optional[int] = Field(None, ge=0)
    
    # Features
    has_priority_support: Optional[bool] = None
    has_advanced_features: Optional[bool] = None
    concurrent_requests: Optional[int] = Field(None, ge=0)
    max_response_time: Optional[int] = Field(None, ge=0)



class UserSubscription(BaseModel):
    plan_id: int
    plan_tier: str
    plan_name: str
    start_date: datetime
    end_date: datetime
    

    class Config:
        from_attributes = True


class SubscriptionPlanResponse(SubscriptionPlanBase):
    id: int
    #user_id: Optional[int]
    created_at: datetime
    updated_at: datetime


    class Config:
        from_attributes = True


# User Subscription Schemas
class UserSubscriptionBase(BaseModel):
    user_id: int
    subscription_plan_id: int
    status: str = "active"

    class Config:
        from_attributes = True


class UserSubscriptionCreate(UserSubscriptionBase):
    pass

class UserSubscriptionUpdate(BaseModel):
    status: Optional[str] = None
    end_date: Optional[datetime] = None

class UserSubscriptionResponse(UserSubscriptionBase):
    id: int
    start_date: datetime
    end_date: datetime
    created_at: datetime
    updated_at: datetime
    #subscription_plan: SubscriptionPlanResponse



    class Config:
        from_attributes = True


# Payment Schemas
class PaymentInitialize(BaseModel):
    email: str
    amount: int
    callback_url: Optional[str] = None
    reference: Optional[str] = None

class PaymentResponse(BaseModel):
    authorization_url: str
    access_code: str
    reference: str

class PaymentVerificationResponse(BaseModel):
    status: bool
    message: str
    data: Optional[dict]

# Response Schemas
class SuccessResponse(BaseModel):
    status: bool = True
    message: str
    data: Optional[dict] = None

class ErrorResponse(BaseModel):
    status: bool = False
    message: str
    error: Optional[dict] = None

# Updated main.py with schemas SubscriptionPlan