from sqlalchemy.orm import Session
from api.v1.websockets.models import Notification, NotificationType, NotificationPriority
from api.v1.websockets.services import NotificationService
from api.v1.websockets.schemas import NotificationCreate
from datetime import datetime


def handle_wallet_notification(user_id: int, action: str, wallet_address: str = None):
    """Handle wallet-related notifications"""
    messages = {
        "connect_wallet": f"Wallet {wallet_address} connected successfully.",
        "get_wallet_details": "Wallet details retrieved successfully.",
        "get_wallets": "Wallet list retrieved successfully.",
        "wallet_not_found": "No wallets found for this user.",
        "wallet_error": "Error occurred while processing wallet operation.",
        "wallet_connect_failed": "Failed to establish wallet connection."
    }
    
    titles = {
        "connect_wallet": "Wallet Connected",
        "get_wallet_details": "Wallet Details",
        "get_wallets": "Wallets Retrieved",
        "wallet_not_found": "Wallet Not Found",
        "wallet_error": "Wallet Error",
        "wallet_connect_failed": "Connection Failed"
    }
    
    if action in messages:
        message = messages[action]
        title = titles.get(action, "Wallet Notification")
        
        if wallet_address and "{wallet_address}" in message:
            message = message.format(wallet_address=wallet_address)
        
        priority = NotificationPriority.HIGH if "failed" in action or "failed" in action else NotificationPriority.NORMAL
        
        # Create notification with enhanced structure
        notification = NotificationCreate(
            title=title,
            message=message,
            type=NotificationType.WALLET,
            priority=priority,
            metadata={
                "timestamp": datetime.now().isoformat(),
                "action": action,
                "wallet_address": wallet_address,
                "transaction_details": {
                    "user_id": user_id,
                    "wallet_address": wallet_address,
                    "status": "failed" if "failed" in action or "failed" in action else "success"
                }
            },
            action_url=None
        )
        
        notification_service = NotificationService()
        notification_service.publish_notification(user_id=user_id, notification=notification)

