from web3 import AsyncWeb3
from web3.providers import AsyncHTTP<PERSON>rovider
from web3.middleware import ExtraDataToPOAMiddleware
from typing import AsyncGenerator, Dict, Any, Type, Callable, Tuple, Optional
from fastapi import HTTPException, status
from api.v1.deploy_contracts.models import TransactionType
from functools import wraps
import random
from redis.asyncio import Redis
import json
import os
import time  
import asyncio
from config import config
from pathlib import Path
import logging
from api.core.logging_config import get_logger
import aiofiles
from pathlib import Path

logger = get_logger(__name__)
#logging.basicConfig(level=logging.INFO)



REDIS_URL = config.REDIS_URL
PROVIDER = config.PROVIDER

class Web3Provider:
    _instance: Optional[AsyncWeb3] = None
    _lock = asyncio.Lock()

    @classmethod
    async def get_instance(cls):
        if not cls._instance:
            async with cls._lock:
                if not cls._instance:
                    try:                            
                        cls._instance = AsyncWeb3(AsyncHTTPProvider(PROVIDER))
                        cls._instance.middleware_onion.inject(ExtraDataToPOAMiddleware, layer=0)
                    except Exception as e:
                        logger.error(f"Failed to initialize Web3 provider: {e}")
                        raise e
        return cls._instance

    @classmethod
    async def close(cls):
        async with cls._lock:    
            if cls._instance:
                await cls._instance.provider.disconnect()
                cls._instance = None
                logger.info("Web3 provider connection closed")


async def get_web3() -> AsyncGenerator[AsyncWeb3, None]:
    """FastAPI dependency for Web3"""
    web3 = await Web3Provider.get_instance()
    yield web3

def class_exponential_backoff(maximum_backoff: int = 64,
                            total_timeout: int = 150) -> Callable[[Type], Type]:
    
    def method_wrapper(method: Callable) -> Callable:
        @wraps(method)
        async def wrapped(*args, **kwargs) -> Any:
            attempt = 0
            start_time = time.time()

            while True:
                try:
                    return await method(*args, **kwargs)
                except HTTPException as e:
                    if e.status_code != status.HTTP_429_TOO_MANY_REQUESTS:
                        raise e

                    current_time = time.time()
                    if current_time - start_time > total_timeout:
                        raise HTTPException(
                            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                            detail="Rate limit retry timeout exceeded"
                        )

                    # Calculate backoff time
                    base_wait = 2 ** attempt
                    jitter = random.uniform(0, 1)
                    wait_time = min(base_wait + jitter, maximum_backoff)

                    print(f"Rate limit hit on attempt {attempt + 1}. "
                          f"Waiting {wait_time:.2f} seconds before retry...")

                    await asyncio.sleep(wait_time)
                    
                    if base_wait < maximum_backoff:
                        attempt += 1
        return wrapped

    def class_decorator(cls: Type) -> Type:
        for attr_name, attr_value in cls.__dict__.items():
            if asyncio.iscoroutinefunction(attr_value):
                setattr(cls, attr_name, method_wrapper(attr_value))
        return cls

    return class_decorator
"""
def create_redis() -> Redis:
    try:
        #client = Redis(host="localhost", port=6379, db=0)
        client = Redis(
            host=config.REDIS_HOST,
            port=config.REDIS_PORT,
            password=config.REDIS_PASSWORD,
            decode_responses=True,
            encoding="utf-8",
            socket_timeout=5,
            retry_on_timeout=True
        )
        return client
    except Exception as e:
        print(f"Redis connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Cache service unavailable: {str(e)}"
        )
"""        
"""

CONTRACT_CONFIG = {
    TransactionType.ER20: {
        "path": Path("api/v1/deploy_contracts/compiled_erc20.json"),
        "keys": ["contracts", "erc_twenty.sol", "ERC20Token"],
        "contract_path": Path("api/v1/deploy_contracts/erc_twenty.sol")
    },
    TransactionType.ERC721: {
        "path": Path("api/v1/deploy_contracts/compiled_erc721.json"),
        "keys": ["contracts", "erc_seventwoone.sol", "NFTContract"],
        "contract_path": Path("api/v1/deploy_contracts/erc_seventwoone.sol")
    }
}
async def load_contract_artifacts(contract_type: TransactionType) -> Tuple[Dict[str, Any], str]:
    ""Load contract ABI and bytecode for specified contract type.""
    
    if contract_type not in CONTRACT_CONFIG:
        # logger.warning(f"Unsupported contract type: {contract_type}")
        return None, None

    config = CONTRACT_CONFIG[contract_type]
    contract_path = config["path"]
    key_chain = config["keys"]

    try:
        async with aiofiles.open(contract_path, 'r') as file:
            compiled_data = json.load(file)
    except FileNotFoundError:
        # logger.error(f"Contract file not found: {contract_path}")
        raise ValueError(f"Contract file not found: {contract_path}")
    except json.JSONDecodeError:
        # logger.error(f"Invalid JSON in file: {contract_path}")
        raise ValueError(f"Invalid JSON in file: {contract_path}")

    # Traverse nested keys
    current_data = compiled_data
    for key in key_chain:
        try:
            current_data = current_data[key]
        except KeyError:
            # logger.error(f"Missing key '{key}' in contract data")
            raise ValueError(f"Invalid contract structure - missing key: '{key}'")

    try:
        abi = current_data['abi']
        bytecode = current_data['evm']['bytecode']['object']
    except KeyError as e:
        # logger.error(f"Missing required field in contract data: {e}")
        raise ValueError(f"Invalid contract format - missing: {e}")

    return abi, bytecode
"""








class ArtifactError(Exception): pass
class ArtifactNotFoundError(ArtifactError): pass
class InvalidArtifactFormatError(ArtifactError): pass


CONTRACT_CONFIG = {
    TransactionType.ER20: {
        "path": Path("api/v1/deploy_contracts/compiled_erc20.json"),
        "keys": ["contracts", "erc_twenty.sol", "ERC20Token"],
        "contract_path": Path("api/v1/deploy_contracts/erc_twenty.sol")
    },
    TransactionType.ERC721: {
        "path": Path("api/v1/deploy_contracts/compiled_erc721.json"),
        "keys": ["contracts", "erc_seventwoone.sol", "NFTContract"],
        "contract_path": Path("api/v1/deploy_contracts/erc_seventwoone.sol")
    }
}
async def load_contract_artifacts(contract_type: TransactionType) -> Tuple[Dict[str, Any], str]:
    """Load contract ABI and bytecode asynchronously."""

    if contract_type not in CONTRACT_CONFIG:
        logger.warning(f"Unsupported contract type requested: {contract_type}")
        return None, None

    config = CONTRACT_CONFIG[contract_type]
    contract_path: Path = config["path"]
    key_chain = config["keys"]

    try:
        async with aiofiles.open(contract_path, mode='r', encoding='utf-8') as file:
            content = await file.read()
        compiled_data = json.loads(content)

    except FileNotFoundError:
        logger.error(f"Contract artifact file not found: {contract_path}")
        raise ArtifactNotFoundError(f"Contract artifact file not found: {contract_path}")
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in artifact file: {contract_path} - {e}")
        raise InvalidArtifactFormatError(f"Invalid JSON in artifact file: {contract_path}")
    except Exception as e:
        logger.exception(f"Error reading artifact file {contract_path}: {e}")
        raise ArtifactError(f"Failed to read artifact file {contract_path}: {e}") from e


    current_data = compiled_data
    for key in key_chain:
        try:
            current_data = current_data[key]
        except (KeyError, TypeError):
            logger.error(f"Missing or invalid key '{key}' in artifact data structure for {contract_path}")
            raise InvalidArtifactFormatError(f"Invalid artifact structure - missing or invalid key: '{key}'")

    try:
        abi = current_data['abi']
        bytecode = current_data['evm']['bytecode']['object']
        if not isinstance(abi, list) or not isinstance(bytecode, str) or not bytecode:
             raise InvalidArtifactFormatError("ABI is not a list or bytecode is not a non-empty string.")
    except (KeyError, TypeError) as e:
        logger.error(f"Missing required field (abi/bytecode) in artifact data for {contract_path}: {e}")
        raise InvalidArtifactFormatError(f"Invalid artifact format - missing abi or bytecode: {e}")

    return abi, bytecode