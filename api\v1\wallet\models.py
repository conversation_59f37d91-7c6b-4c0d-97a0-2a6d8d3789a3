from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, ForeignKey, Text, Integer, String, DateTime, BIGINT
from sqlalchemy.orm import relationship
from datetime import datetime, date, timezone
from api.db.database import Base

"""
class Wallet(Base):
    __tablename__ = "users_wallet"
    id = Column(BIGINT, primary_key=True, autoincrement=True, index=True, unique=True)
    user_id = Column(BIGINT, ForeignKey("user.id"), nullable=False, index=True)

    wallet_address = Column(String, unique=True, index=True)
    chain_id = Column(Integer)
    is_verified = Column(Boolean, default=False)
    #is_active = Column(Boolean, default=True)
    #last_connected = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    #nonce = Column(String, unique=True)
    session_metadata = Column(Text)
"""


class WalletAddress(Base):
    __tablename__ = "wallet_addresses"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(BIGINT, ForeignKey("user.id"), nullable=False, index=True)
    address = Column(String, unique=True, index=True)
    chain_id = Column(Integer, nullable=True)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    last_verified = Column(DateTime(timezone=True), nullable=True)
    wc_session_id = Column(String, nullable=True)
