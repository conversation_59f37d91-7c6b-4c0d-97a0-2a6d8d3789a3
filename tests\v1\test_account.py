import pytest
import pytest_asyncio
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta, timezone
from sqlalchemy import select
from httpx import AsyncClient
from redis.asyncio import Redis
import json
from eth_account import Account
from web3 import AsyncWeb3, Web3

from api.v1.account.models import Account as AccountModel
from api.v1.account.models import WalletScore, WalletTransactions, AccountBalanceHistory
from api.v1.account import schemas as account_schemas
from api.core.logging_config import get_logger
from config import config
from api.core.wallet_security import WalletSecurity

logger = get_logger(__name__)

ALCHEMY_API_KEY = "************************************"
TEST_PROVIDER: str = f"https://polygon-amoy.g.alchemy.com/v2/{ALCHEMY_API_KEY}"

# Test constants
TEST_ADDRESS = "******************************************"
TEST_PRIVATE_KEY = "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"


class MockRedis:
    """Mock Redis for testing"""
    def __init__(self):
        self.data = {}

    async def get(self, key):
        return self.data.get(key)

    async def set(self, key, value, ex=None):
        self.data[key] = value
        return True

    async def delete(self, key):
        if key in self.data:
            del self.data[key]
        return True

    async def expire(self, key, time):
        return True


@pytest_asyncio.fixture
async def mock_redis():
    return MockRedis()


@pytest_asyncio.fixture
async def mock_web3():
    w3 = AsyncWeb3(AsyncWeb3.AsyncHTTPProvider(TEST_PROVIDER))
    return w3


@pytest.mark.asyncio
async def test_create_vault(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test creating a new vault"""
    
    # Mock Account.create() to return predictable values
    mock_account = MagicMock()
    mock_account.address = TEST_ADDRESS
    mock_account._private_key = bytes.fromhex(TEST_PRIVATE_KEY[2:] if TEST_PRIVATE_KEY.startswith('0x') else TEST_PRIVATE_KEY)
    
    with patch('eth_account.Account.create', return_value=mock_account):
        response = await client.post(
            "/account/vault/create",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

        assert response.status_code == 200
        data = response.json()
        assert "address" in data
        assert "id" in data
        assert "created_at" in data

        # Verify vault was created in database
        result = await async_db.execute(
            select(AccountModel).filter(AccountModel.address == data["address"])
        )
        vault = result.scalar_one_or_none()
        assert vault is not None
        assert vault.address == data["address"]


@pytest.mark.asyncio
async def test_get_vault(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test getting vault information"""
    # First create a vault
    with patch('eth_account.Account.create', return_value=MagicMock(
        address=TEST_ADDRESS,
        _private_key=bytes.fromhex(TEST_PRIVATE_KEY[2:] if TEST_PRIVATE_KEY.startswith('0x') else TEST_PRIVATE_KEY)
    )):
        await client.post(
            "/account/vault/create",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

    # Now try to get it
    response = await client.get(
        "/account/vault/get",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    assert "address" in data
    assert data["address"] == TEST_ADDRESS


@pytest.mark.asyncio
async def test_get_nonexistent_vault(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test getting vault information when user has no vault"""
    response = await client.get(
        "/account/vault/get",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 404
    assert response.json()["detail"] == "User does not have a vault"


@pytest.mark.asyncio
async def test_get_vault_balance(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test getting vault balance"""
    # First create a vault
    with patch('eth_account.Account.create', return_value=MagicMock(
        address=TEST_ADDRESS,
        _private_key=bytes.fromhex(TEST_PRIVATE_KEY[2:] if TEST_PRIVATE_KEY.startswith('0x') else TEST_PRIVATE_KEY)
    )):
        await client.post(
            "/account/vault/create",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

    # Mock the web3 get_balance call
    mock_balance = Web3.to_wei(1.5, 'ether')
    with patch.object(mock_web3.eth, 'get_balance', return_value=mock_balance):
        response = await client.get(
            "/account/vault/get_balance",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

        assert response.status_code == 200
        data = response.json()
        assert "balance" in data
        assert data["balance"] == 1.5  # Should be converted from Wei to ETH


@pytest.mark.asyncio
async def test_calculate_credit_score(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test calculating credit score"""
    # First create a vault
    with patch('eth_account.Account.create', return_value=MagicMock(
        address=TEST_ADDRESS,
        _private_key=bytes.fromhex(TEST_PRIVATE_KEY[2:] if TEST_PRIVATE_KEY.startswith('0x') else TEST_PRIVATE_KEY)
    )):
        await client.post(
            "/account/vault/create",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

    # Add some transaction history
    vault = await async_db.execute(
        select(AccountModel).filter(AccountModel.address == TEST_ADDRESS)
    )
    vault = vault.scalar_one()

    # Add some test transactions
    transactions = [
        WalletTransactions(
            user_id=test_verified_user["id"],
            address_id=vault.id,
            contract_status="deployed",
            value=Web3.to_wei(1, 'ether'),
            created_at=datetime.now(timezone.utc) - timedelta(days=i)
        )
        for i in range(5)  # 5 transactions over last 5 days
    ]
    async_db.add_all(transactions)
    await async_db.commit()

    response = await client.get(
        "/account/credit-score",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert data["status"] == "sucess"
    assert "score" in data
    
    score = data["score"]
    assert "total_score" in score
    assert "historical_score" in score
    assert "activity_score" in score
    assert "financial_score" in score
    assert "network_score" in score
    assert "weekly_rate_change" in score


@pytest.mark.asyncio
async def test_vault_creation_with_redis_failure(client, test_verified_user, async_db, mock_web3):
    """Test that vault creation works even when Redis is unavailable"""
    # Mock redis to raise an exception
    bad_redis = MagicMock()
    bad_redis.get.side_effect = Exception("Redis connection failed")
    bad_redis.set.side_effect = Exception("Redis connection failed")

    with patch('eth_account.Account.create', return_value=MagicMock(
        address=TEST_ADDRESS,
        _private_key=bytes.fromhex(TEST_PRIVATE_KEY[2:] if TEST_PRIVATE_KEY.startswith('0x') else TEST_PRIVATE_KEY)
    )):
        response = await client.post(
            "/account/vault/create",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

        assert response.status_code == 200
        data = response.json()
        assert "address" in data
        assert data["address"] == TEST_ADDRESS


@pytest.mark.asyncio
async def test_create_duplicate_vault(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test that creating a duplicate vault fails"""
    # Create first vault
    with patch('eth_account.Account.create', return_value=MagicMock(
        address=TEST_ADDRESS,
        _private_key=bytes.fromhex(TEST_PRIVATE_KEY[2:] if TEST_PRIVATE_KEY.startswith('0x') else TEST_PRIVATE_KEY)
    )):
        await client.post(
            "/account/vault/create",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

    # Try to create second vault
    response = await client.post(
        "/account/vault/create",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 409
    assert response.json()["detail"] == "User already has a vault"


@pytest.mark.asyncio
async def test_get_balance_blockchain_error(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test handling of blockchain errors when getting balance"""
    # First create a vault
    with patch('eth_account.Account.create', return_value=MagicMock(
        address=TEST_ADDRESS,
        _private_key=bytes.fromhex(TEST_PRIVATE_KEY[2:] if TEST_PRIVATE_KEY.startswith('0x') else TEST_PRIVATE_KEY)
    )):
        await client.post(
            "/account/vault/create",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

    # Mock web3 to raise an exception
    with patch.object(mock_web3.eth, 'get_balance', side_effect=Exception("Blockchain error")):
        response = await client.get(
            "/account/vault/get_balance",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

        assert response.status_code == 502
        assert "Failed to interact with blockchain" in response.json()["detail"]


@pytest.mark.asyncio
async def test_calculate_credit_score_no_vault(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test calculating credit score when user has no vault"""
    response = await client.get(
        "/account/credit-score",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 404
    assert response.json()["detail"] == "Wallet address not found"


@pytest.mark.asyncio
async def test_access_without_auth(client, async_db, mock_redis, mock_web3):
    """Test accessing endpoints without authentication"""
    endpoints = [
        ("POST", "/account/vault/create"),
        ("GET", "/account/vault/get"),
        ("GET", "/account/vault/get_balance"),
        ("GET", "/account/credit-score"),
    ]

    for method, endpoint in endpoints:
        if method == "GET":
            response = await client.get(endpoint)
        else:
            response = await client.post(endpoint)

        assert response.status_code == 401
        json_response = response.json()
        assert "detail" in json_response
        assert "Could not validate credentials" in json_response["detail"]


@pytest.mark.asyncio
async def test_credit_score_calculation_with_old_transactions(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test credit score calculation with transactions spanning different time periods"""
    # First create a vault
    with patch('eth_account.Account.create', return_value=MagicMock(
        address=TEST_ADDRESS,
        _private_key=bytes.fromhex(TEST_PRIVATE_KEY[2:] if TEST_PRIVATE_KEY.startswith('0x') else TEST_PRIVATE_KEY)
    )):
        await client.post(
            "/account/vault/create",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

    vault = await async_db.execute(
        select(AccountModel).filter(AccountModel.address == TEST_ADDRESS)
    )
    vault = vault.scalar_one()

    # Create transactions at different time periods
    transactions = [
        # Recent daily transactions
        *[WalletTransactions(
            user_id=test_verified_user["id"],
            address_id=vault.id,
            contract_status="deployed",
            value=Web3.to_wei(1, 'ether'),
            created_at=datetime.now(timezone.utc) - timedelta(days=i)
        ) for i in range(25)],  # Last 25 days
        
        # Older transactions
        *[WalletTransactions(
            user_id=test_verified_user["id"],
            address_id=vault.id,
            contract_status="deployed",
            value=Web3.to_wei(0.5, 'ether'),
            created_at=datetime.now(timezone.utc) - timedelta(days=30+i)
        ) for i in range(10)],  # 30-40 days ago
        
        # Some failed transactions
        *[WalletTransactions(
            user_id=test_verified_user["id"],
            address_id=vault.id,
            contract_status="failed",
            value=Web3.to_wei(0.1, 'ether'),
            created_at=datetime.now(timezone.utc) - timedelta(days=i*3)
        ) for i in range(5)]  # Every 3 days for last 15 days
    ]
    
    async_db.add_all(transactions)
    await async_db.commit()

    response = await client.get(
        "/account/credit-score",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    score = data["score"]
    
    # We expect high activity score due to daily transactions
    assert score["activity_score"] >= 150  # Should be high due to daily activity
    
    # Success rate should be affected by the failed transactions
    total_tx = 40  # 25 + 10 + 5
    success_tx = 35  # 25 + 10
    expected_success_rate = (success_tx / total_tx) * 100
    
    # Allow small floating point differences
    assert abs(score["transaction_success_rate"] - expected_success_rate) < 0.01 if "transaction_success_rate" in score else True


@pytest.mark.asyncio
async def test_credit_score_with_different_contract_types(client, test_verified_user, async_db, mock_redis, mock_web3):
    """Test credit score calculation with different types of contracts"""
    # First create a vault
    with patch('eth_account.Account.create', return_value=MagicMock(
        address=TEST_ADDRESS,
        _private_key=bytes.fromhex(TEST_PRIVATE_KEY[2:] if TEST_PRIVATE_KEY.startswith('0x') else TEST_PRIVATE_KEY)
    )):
        await client.post(
            "/account/vault/create",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

    vault = await async_db.execute(
        select(AccountModel).filter(AccountModel.address == TEST_ADDRESS)
    )
    vault = vault.scalar_one()

    # Add transactions with different contract types
    transactions = [
        # ERC20 transactions
        WalletTransactions(
            user_id=test_verified_user["id"],
            address_id=vault.id,
            contract_status="deployed",
            value=Web3.to_wei(1, 'ether'),
            contract_type="erc20",
            created_at=datetime.now(timezone.utc) - timedelta(days=1)
        ),
        # ERC721 (NFT) transactions
        WalletTransactions(
            user_id=test_verified_user["id"],
            address_id=vault.id,
            contract_status="deployed",
            value=Web3.to_wei(0.5, 'ether'),
            contract_type="erc721",
            created_at=datetime.now(timezone.utc) - timedelta(days=2)
        )
    ]
    
    async_db.add_all(transactions)
    await async_db.commit()

    response = await client.get(
        "/account/credit-score",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    score = data["score"]
    
    # Asset diversity should be reflected in the financial score
    assert score["financial_score"] > 0  # Should get points for having both ERC20 and NFT transactions