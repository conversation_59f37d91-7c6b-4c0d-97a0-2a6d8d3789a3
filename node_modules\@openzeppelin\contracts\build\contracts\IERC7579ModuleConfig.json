{"_format": "hh-sol-artifact-1", "contractName": "IERC7579ModuleConfig", "sourceName": "contracts/interfaces/draft-IERC7579.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "module", "type": "address"}], "name": "ModuleInstalled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "module", "type": "address"}], "name": "ModuleUninstalled", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"internalType": "address", "name": "module", "type": "address"}, {"internalType": "bytes", "name": "initData", "type": "bytes"}], "name": "installModule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"internalType": "address", "name": "module", "type": "address"}, {"internalType": "bytes", "name": "additionalContext", "type": "bytes"}], "name": "isModuleInstalled", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "moduleTypeId", "type": "uint256"}, {"internalType": "address", "name": "module", "type": "address"}, {"internalType": "bytes", "name": "deInitData", "type": "bytes"}], "name": "uninstallModule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}