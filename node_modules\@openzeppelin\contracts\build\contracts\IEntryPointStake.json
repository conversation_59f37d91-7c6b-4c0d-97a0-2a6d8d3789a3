{"_format": "hh-sol-artifact-1", "contractName": "IEntryPointStake", "sourceName": "contracts/interfaces/draft-IERC4337.sol", "abi": [{"inputs": [{"internalType": "uint32", "name": "unstakeDelaySec", "type": "uint32"}], "name": "addStake", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "depositTo", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "unlockStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "withdraw<PERSON><PERSON><PERSON>", "type": "address"}], "name": "withdrawStake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "withdraw<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "uint256", "name": "withdrawAmount", "type": "uint256"}], "name": "withdrawTo", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}