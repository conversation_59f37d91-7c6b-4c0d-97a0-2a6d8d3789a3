import pytest
import pytest_asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from httpx import AsyncClient
import json
import asyncio
from fastapi import WebSocket, WebSocketDisconnect

from api.v1.websockets.models import Notification, NotificationType, NotificationPriority, NotificationStatus
from api.v1.websockets.services import NotificationService, NotificationManger
from api.v1.websockets.connection_manager import ConnectionManager
from api.v1.websockets.schemas import NotificationCreate, NotificationUpdate, Notifications
from api.core.logging_config import get_logger

logger = get_logger(__name__)

# Test constants
TEST_NOTIFICATION_TITLE = "Test Notification"
TEST_NOTIFICATION_MESSAGE = "This is a test notification"

class MockRedis:
    """Mock Redis for testing"""
    def __init__(self):
        self.data = {}
        self.published_messages = []
        self.channels = {}

    async def get(self, key):
        return self.data.get(key)

    async def set(self, key, value, ex=None):
        self.data[key] = value
        return True

    async def delete(self, key):
        if key in self.data:
            del self.data[key]
        return True

    async def publish(self, channel, message):
        self.published_messages.append((channel, message))
        if channel in self.channels:
            for callback in self.channels[channel]:
                await callback(channel, message)
        return 1

    async def subscribe(self, *channels):
        for channel in channels:
            if channel not in self.channels:
                self.channels[channel] = []
        return None

    async def unsubscribe(self, *channels):
        for channel in channels:
            if channel in self.channels:
                del self.channels[channel]
        return None

    def pubsub(self):
        return MockPubSub(self)

class MockPubSub:
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.channels = {}
        self.patterns = {}
        self.subscribed = False

    async def subscribe(self, *channels):
        self.subscribed = True
        for channel in channels:
            self.channels[channel] = []
        await self.redis_client.subscribe(*channels)
        return None

    async def unsubscribe(self, *channels):
        for channel in channels:
            if channel in self.channels:
                del self.channels[channel]
        await self.redis_client.unsubscribe(*channels)
        return None

    async def listen(self):
        while self.subscribed:
            yield {"type": "message", "data": "test"}
            await asyncio.sleep(0.1)

    async def close(self):
        self.subscribed = False
        return None

class MockWebSocket:
    """Mock WebSocket for testing"""
    def __init__(self, user_id=None):
        self.user_id = user_id
        self.accepted = False
        self.closed = False
        self.close_code = None
        self.close_reason = None
        self.sent_messages = []
        self.query_params = {"token": f"test_token_{user_id}"} if user_id else {}
        self.received_messages = []

    async def accept(self):
        self.accepted = True
        return None

    async def close(self, code=1000, reason=None):
        self.closed = True
        self.close_code = code
        self.close_reason = reason
        return None

    async def send_text(self, data):
        self.sent_messages.append({"type": "text", "data": data})
        return None

    async def send_json(self, data):
        self.sent_messages.append({"type": "json", "data": data})
        return None

    async def receive_text(self):
        if not self.received_messages:
            raise WebSocketDisconnect()
        return self.received_messages.pop(0)

    async def receive_json(self):
        if not self.received_messages:
            raise WebSocketDisconnect()
        return json.loads(self.received_messages.pop(0))

    def add_received_message(self, message):
        if isinstance(message, dict):
            self.received_messages.append(json.dumps(message))
        else:
            self.received_messages.append(message)

@pytest_asyncio.fixture
async def mock_redis():
    return MockRedis()

@pytest_asyncio.fixture
async def test_notification(async_db, test_verified_user):
    """Create a test notification for a user"""
    notification = Notification(
        user_id=test_verified_user["id"],
        title=TEST_NOTIFICATION_TITLE,
        message=TEST_NOTIFICATION_MESSAGE,
        type=NotificationType.SYSTEM,
        priority=NotificationPriority.NORMAL,
        status=NotificationStatus.PENDING,
        is_read=False,
        is_deleted=False,
        created_at=datetime.now(timezone.utc)
    )
    async_db.add(notification)
    await async_db.commit()
    await async_db.refresh(notification)
    return notification

@pytest_asyncio.fixture
async def mock_websocket():
    """Create a mock WebSocket for testing"""
    return MockWebSocket(user_id=1)

@pytest.mark.asyncio
async def test_connection_manager_connect(mock_websocket):
    """Test connecting a WebSocket to the ConnectionManager"""
    connection_manager = ConnectionManager()
    
    # Test successful connection
    result = await connection_manager.connect(mock_websocket, 1)
    
    assert result is True
    assert mock_websocket.accepted is True
    assert 1 in connection_manager._active_connections
    assert len(connection_manager._active_connections[1]) == 1
    assert connection_manager._active_connections[1][0].socket == mock_websocket
    assert mock_websocket in connection_manager._user_connecitons

@pytest.mark.asyncio
async def test_connection_manager_max_connections(mock_websocket):
    """Test ConnectionManager max connections limit"""
    connection_manager = ConnectionManager(max_connection=2)
    
    # Connect first socket (should succeed)
    socket1 = MockWebSocket(user_id=1)
    result1 = await connection_manager.connect(socket1, 1)
    assert result1 is True
    
    # Connect second socket (should succeed)
    socket2 = MockWebSocket(user_id=1)
    result2 = await connection_manager.connect(socket2, 1)
    assert result2 is True
    
    # Connect third socket (should fail due to max connections)
    socket3 = MockWebSocket(user_id=1)
    result3 = await connection_manager.connect(socket3, 1)
    assert result3 is False
    assert socket3.closed is True
    assert socket3.close_code == 1008
    assert "Too many connections" in socket3.close_reason

@pytest.mark.asyncio
async def test_connection_manager_disconnect(mock_websocket):
    """Test disconnecting a WebSocket from the ConnectionManager"""
    connection_manager = ConnectionManager()
    
    # First connect the socket
    await connection_manager.connect(mock_websocket, 1)
    
    # Then disconnect it
    await connection_manager.disconnect(mock_websocket)
    
    assert mock_websocket.closed is True
    assert 1 not in connection_manager._active_connections
    assert mock_websocket not in connection_manager._user_connecitons

@pytest.mark.asyncio
async def test_connection_manager_send_notification(mock_websocket):
    """Test sending a notification through the ConnectionManager"""
    connection_manager = ConnectionManager()
    
    # First connect the socket
    await connection_manager.connect(mock_websocket, 1)
    
    # Create a test notification
    notification = {
        "title": TEST_NOTIFICATION_TITLE,
        "message": TEST_NOTIFICATION_MESSAGE,
        "type": "system",
        "priority": "normal"
    }
    
    # Send the notification
    successful_sends = await connection_manager.send_notification(1, notification, 1)
    
    assert successful_sends == 1
    assert len(mock_websocket.sent_messages) == 1
    assert mock_websocket.sent_messages[0]["type"] == "json"
    assert mock_websocket.sent_messages[0]["data"]["type"] == "notification"
    assert mock_websocket.sent_messages[0]["data"]["id"] == 1
    assert mock_websocket.sent_messages[0]["data"]["data"] == notification

@pytest.mark.asyncio
async def test_connection_manager_cleanup_stale_connections():
    """Test cleaning up stale connections in the ConnectionManager"""
    connection_manager = ConnectionManager(connection_timeout=1)  # 1 second timeout for testing
    
    # Create and connect a socket
    socket = MockWebSocket(user_id=1)
    await connection_manager.connect(socket, 1)
    
    # Set the last heartbeat to a time in the past
    connection_manager._active_connections[1][0].last_heartbeat = datetime.now() - timedelta(seconds=2)
    
    # Run the cleanup
    await connection_manager._cleanup_stale_connections()
    
    # The socket should be disconnected
    assert socket.closed is True
    assert 1 not in connection_manager._active_connections

@pytest.mark.asyncio
async def test_notification_service_create(async_db, test_verified_user):
    """Test creating a notification using the NotificationService"""
    notification_service = NotificationService()
    
    # Create a notification
    notification_data = NotificationCreate(
        title=TEST_NOTIFICATION_TITLE,
        message=TEST_NOTIFICATION_MESSAGE,
        type=NotificationType.SYSTEM,
        priority=NotificationPriority.NORMAL
    )
    
    with patch('api.v1.websockets.services.process_notification_task') as mock_task:
        result = notification_service.publish_notification(
            user_id=test_verified_user["id"],
            notification=notification_data
        )
        
        assert result is True
        mock_task.delay.assert_called_once()
        args, kwargs = mock_task.delay.call_args
        assert kwargs["user_id"] == test_verified_user["id"]
        assert kwargs["noti"]["title"] == TEST_NOTIFICATION_TITLE
        assert kwargs["noti"]["message"] == TEST_NOTIFICATION_MESSAGE

@pytest.mark.asyncio
async def test_notification_service_get_user_notifications(async_db, test_verified_user, test_notification):
    """Test getting user notifications using the NotificationService"""
    notification_service = await NotificationService.create()
    
    # Get notifications for the user
    notifications = await notification_service.get_user_notifications(
        db=async_db,
        user_id=test_verified_user["id"]
    )
    
    assert len(notifications) == 1
    assert notifications[0].title == TEST_NOTIFICATION_TITLE
    assert notifications[0].message == TEST_NOTIFICATION_MESSAGE
    assert notifications[0].user_id == test_verified_user["id"]

@pytest.mark.asyncio
async def test_notification_service_get_notification(async_db, test_verified_user, test_notification):
    """Test getting a specific notification using the NotificationService"""
    notification_service = await NotificationService.create()
    
    # Get the specific notification
    notification = await notification_service.get_notification(
        db=async_db,
        notification_id=test_notification.id,
        user_id=test_verified_user["id"]
    )
    
    assert notification.id == test_notification.id
    assert notification.title == TEST_NOTIFICATION_TITLE
    assert notification.message == TEST_NOTIFICATION_MESSAGE
    assert notification.user_id == test_verified_user["id"]

@pytest.mark.asyncio
async def test_notification_service_mark_notification_read(async_db, test_verified_user, test_notification):
    """Test marking a notification as read using the NotificationService"""
    notification_service = await NotificationService.create()
    
    # Mark the notification as read
    await notification_service.mark_notification_read(
        db=async_db,
        notification_id=test_notification.id,
        user_id=test_verified_user["id"]
    )
    
    # Verify the notification is marked as read
    result = await async_db.execute(
        select(Notification).filter(
            Notification.id == test_notification.id,
            Notification.user_id == test_verified_user["id"]
        )
    )
    notification = result.scalar_one_or_none()
    assert notification is not None
    assert notification.is_read is True

@pytest.mark.asyncio
async def test_notification_manager_handle_websocket(mock_websocket, mock_redis):
    """Test handling a WebSocket connection with the NotificationManager"""
    notification_manager = NotificationManger()
    notification_manager.redis_client = mock_redis
    
    # Add a test message to the WebSocket
    mock_websocket.add_received_message({
        "type": "ping"
    })
    
    # Mock the connection_manager.connect method
    with patch.object(notification_manager.connection_manager, 'connect', return_value=True), \
         patch.object(notification_manager.connection_manager, 'disconnect'):
        
        # Create a task that will be cancelled after a short time
        async def cancel_after_delay():
            await asyncio.sleep(0.1)
            raise WebSocketDisconnect()
        
        # Run the handle_websocket method with the mock WebSocket
        with pytest.raises(WebSocketDisconnect):
            await asyncio.gather(
                notification_manager.handle_websocket(mock_websocket, 1),
                cancel_after_delay()
            )
        
        # Verify the WebSocket received the connection status message
        assert len(mock_websocket.sent_messages) >= 1
        assert mock_websocket.sent_messages[0]["type"] == "json"
        assert mock_websocket.sent_messages[0]["data"]["type"] == "connection_status"
        assert mock_websocket.sent_messages[0]["data"]["status"] == "connected"

@pytest.mark.asyncio
async def test_notification_endpoint(client, test_verified_user, test_notification):
    """Test the notification endpoint"""
    response = await client.get(
        f"/notification/?notification_id={test_notification.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == test_notification.id
    assert data["title"] == TEST_NOTIFICATION_TITLE
    assert data["message"] == TEST_NOTIFICATION_MESSAGE
    assert data["user_id"] == test_verified_user["id"]

@pytest.mark.asyncio
async def test_notification_endpoint_nonexistent(client, test_verified_user):
    """Test the notification endpoint with a nonexistent notification"""
    response = await client.get(
        "/notification/?notification_id=9999",  # Nonexistent notification ID
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "Notification not found" in data["detail"]

@pytest.mark.asyncio
async def test_notification_endpoint_unauthorized(client, test_verified_user, async_db):
    """Test the notification endpoint with an unauthorized user"""
    # Create a notification for a different user
    other_user_notification = Notification(
        user_id=999,  # Different user ID
        title=TEST_NOTIFICATION_TITLE,
        message=TEST_NOTIFICATION_MESSAGE,
        type=NotificationType.SYSTEM,
        priority=NotificationPriority.NORMAL,
        status=NotificationStatus.PENDING,
        is_read=False,
        is_deleted=False,
        created_at=datetime.now(timezone.utc)
    )
    async_db.add(other_user_notification)
    await async_db.commit()
    await async_db.refresh(other_user_notification)
    
    # Try to access the notification as the test user
    response = await client.get(
        f"/notification/?notification_id={other_user_notification.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "Notification not found" in data["detail"]
