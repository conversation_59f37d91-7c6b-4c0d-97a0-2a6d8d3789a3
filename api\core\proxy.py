from typing import Dict, Any, Optional
import aiohttp
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from api.core.logging_config import get_logger
from config import config
import uuid

logger = get_logger(__name__)

class ProxyManager:
    """Manages API proxy requests"""
    
    def __init__(self, base_url: str, api_key: str, timeout: int = 30):
        self.session = None
        self.base_url = base_url
        self.api_key = api_key
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        
    async def get_session(self) -> aiohttp.ClientSession:
        """Get or create aiohttp session"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self.session
        
    async def close(self):
        """Close the aiohttp session"""
        if self.session and not self.session.closed:
            await self.session.close()
            
    async def proxy_request(self, method: str, endpoint: str,
                          headers: Optional[Dict[str, str]] = None,
                          params: Optional[Dict[str, Any]] = None,
                          json_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Proxy API request"""
        try:
            session = await self.get_session()
            url = f"{self.base_url}{endpoint}"
            
            # Add security headers
            security_headers = {
                "X-Request-ID": str(uuid.uuid4()),
                "X-Forwarded-For": "internal",
                "X-API-Key": self.api_key
            }
            
            if headers:
                #headers.update(security_headers)
                headers=headers
            else:
                headers = security_headers
                
            async with session.request(method, url, headers=headers,
                                    params=params, json=json_data) as response:
                if response.status >= 400:
                    error_text = await response.text()
                    logger.error(f"Proxy request failed: {error_text}")
                    raise HTTPException(
                        status_code=response.status,
                        detail=f"Proxy request failed: {error_text}"
                    )
                    
                return await response.json()
                
        except aiohttp.ClientError as e:
            logger.error(f"Proxy request error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="Proxy request failed"
            )
            
    async def proxy_get(self, endpoint: str,
                       headers: Optional[Dict[str, str]] = None,
                       params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Proxy GET request"""
        return await self.proxy_request("GET", endpoint, headers, params)
        
    async def proxy_post(self, endpoint: str,
                        headers: Optional[Dict[str, str]] = None,
                        json_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Proxy POST request"""
        return await self.proxy_request("POST", endpoint, headers, json_data=json_data)
        
    async def proxy_put(self, endpoint: str,
                       headers: Optional[Dict[str, str]] = None,
                       json_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Proxy PUT request"""
        return await self.proxy_request("PUT", endpoint, headers, json_data=json_data)
        
    async def proxy_delete(self, endpoint: str,
                          headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Proxy DELETE request"""
        return await self.proxy_request("DELETE", endpoint, headers)
        
    async def proxy_websocket(self, endpoint: str,
                            headers: Optional[Dict[str, str]] = None) -> aiohttp.ClientWebSocketResponse:
        """Proxy WebSocket connection"""
        try:
            session = await self.get_session()
            url = f"{self.base_url}{endpoint}"
            
            # Add security headers
            security_headers = {
                "X-Request-ID": str(uuid.uuid4()),
                "X-Forwarded-For": "internal",
                "X-API-Key": self.api_key
            }
            
            if headers:
                #headers.update(security_headers)
                headers=headers
            else:
                headers = security_headers
                
            return await session.ws_connect(url, headers=headers)
            
        except aiohttp.ClientError as e:
            logger.error(f"WebSocket proxy error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="WebSocket proxy failed"
            )
            
    async def proxy_file_upload(self, endpoint: str,
                              file_data: bytes,
                              filename: str,
                              headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """Proxy file upload"""
        try:
            session = await self.get_session()
            url = f"{self.base_url}{endpoint}"
            
            # Add security headers
            security_headers = {
                "X-Request-ID": str(uuid.uuid4()),
                "X-Forwarded-For": "internal",
                "X-API-Key": self.api_key
            }
            
            if headers:
                #headers.update(security_headers)
                headers=headers
            else:
                headers = security_headers
                
            data = aiohttp.FormData()
            data.add_field('file', file_data, filename=filename)
            
            async with session.post(url, headers=headers, data=data) as response:
                if response.status >= 400:
                    error_text = await response.text()
                    logger.error(f"File upload proxy failed: {error_text}")
                    raise HTTPException(
                        status_code=response.status,
                        detail=f"File upload proxy failed: {error_text}"
                    )
                    
                return await response.json()
                
        except aiohttp.ClientError as e:
            logger.error(f"File upload proxy error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="File upload proxy failed"
            )
            
    async def proxy_stream(self, endpoint: str,
                          headers: Optional[Dict[str, str]] = None,
                          params: Optional[Dict[str, Any]] = None) -> aiohttp.ClientResponse:
        """Proxy streaming response"""
        try:
            session = await self.get_session()
            url = f"{self.base_url}{endpoint}"
            
            # Add security headers
            security_headers = {
                "X-Request-ID": str(uuid.uuid4()),
                "X-Forwarded-For": "internal",
                "X-API-Key": self.api_key
            }
            
            if headers:
                #headers.update(security_headers)
                headers=headers
            else:
                headers = security_headers
                
            return await session.get(url, headers=headers, params=params)
            
        except aiohttp.ClientError as e:
            logger.error(f"Stream proxy error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="Stream proxy failed"
            ) 
        




#EVERYTHING BELOW IS ADDITIONAL AND CAN BE REMOVED


from redis import Redis
from datetime import datetime

class RateLimitedProxy(ProxyManager):
    def __init__(self, redis_client: Redis, rate_limit: int = 100, **kwargs):
        super().__init__(**kwargs)
        self.redis = redis_client
        self.rate_limit = rate_limit

    async def check_rate_limit(self, client_id: str) -> bool:
        key = f"rate_limit:{client_id}"
        current = await self.redis.incr(key)
        if current == 1:
            await self.redis.expire(key, 60)  # Reset after 1 minute
        return current <= self.rate_limit

    async def proxy_request(self, method: str, endpoint: str, **kwargs):
        client_id = kwargs.get('headers', {}).get('X-Client-ID')
        if not await self.check_rate_limit(client_id):
            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded"
            )
        return await super().proxy_request(method, endpoint, **kwargs)
    


"""
from jwt import decode

class AuthenticatedProxy(ProxyManager):
    def __init__(self, jwt_secret: str, **kwargs):
        super().__init__(**kwargs)
        self.jwt_secret = jwt_secret

    async def validate_token(self, token: str) -> dict:
        try:
            return decode(token, self.jwt_secret, algorithms=["HS256"])
        except Exception as e:
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication token"
            )

    async def proxy_request(self, method: str, endpoint: str, **kwargs):
        headers = kwargs.get('headers', {})
        token = headers.get('Authorization', '').replace('Bearer ', '')
        
        # Validate JWT token before proceeding
        user_data = await self.validate_token(token)
        
        # Add user context to headers
        headers['X-User-ID'] = user_data['user_id']
        kwargs['headers'] = headers
        
        return await super().proxy_request(method, endpoint, **kwargs)
    
"""


import random

class LoadBalancedProxy(ProxyManager):
    def __init__(self, service_urls: list[str], **kwargs):
        # Remove base_url from kwargs as we'll handle it dynamically
        kwargs.pop('base_url', None)
        super().__init__(base_url="", **kwargs)
        self.service_urls = service_urls
        self.current_index = 0

    def get_next_server(self) -> str:
        # Round-robin load balancing
        url = self.service_urls[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.service_urls)
        return url

    async def proxy_request(self, method: str, endpoint: str, **kwargs):
        # Set the base URL for this request
        self.base_url = self.get_next_server()
        return await super().proxy_request(method, endpoint, **kwargs)