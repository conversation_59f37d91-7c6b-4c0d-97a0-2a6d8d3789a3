from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, status
from api.db.backup import BackupManager
from api.core.auth import require_super_admin
from typing import Optional
from datetime import datetime
import os
import logging
from api.core.logging_config import get_logger
import aiofiles
import os
from datetime import datetime

# Setup logger
logger = get_logger(__name__)

app = APIRouter(prefix="/backup", tags=["Backup"])



async def create_maintenance_file():
    maintenance_file = "/tmp/maintenance_mode"
    try:
        # Create /tmp directory if it doesn't exist
        os.makedirs("/tmp", exist_ok=True)
        
        async with aiofiles.open(maintenance_file, "w") as f:
            await f.write(str(datetime.now()))
        return True
    except Exception as e:
        logger.error(f"Failed to create maintenance file: {e}")
        return False
    

@app.post("/restore/{backup_name}")
async def restore_backup(
    backup_name: str,
    background_tasks: BackgroundTasks,
    admin = Depends(require_super_admin),  # Only super admins can restore
):
    """
    Restore database from backup with safety checks
    Requires maintenance mode and super admin privileges
    """
    # Create maintenance mode flag
    maintenance_created = await create_maintenance_file()
    if not maintenance_created:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to enable maintenance mode"
        )
    backup_manager = BackupManager()
    
    # Start restore process
    success = await backup_manager.restore_database_backup(backup_name)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database restore failed"
        )

    if success:
        # Remove maintenance flag after successful restore
        background_tasks.add_task(os.remove, maintenance_created)
        return {"status": "success", "message": "Database restored successfully"}
    else:
        raise HTTPException(
            status_code=500,
            detail="Database restore failed. System is in maintenance mode."
        )
        


@app.get("/available")
async def list_available_backups(admin = Depends(require_super_admin)):
    """List available backups for restore"""
    backup_manager = BackupManager()
    return await backup_manager.get_backup_status()