from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List
from .models import Notification
from fastapi import HTT<PERSON><PERSON>x<PERSON>, WebSocket, WebSocketDisconnect, status
from .connection_manager import ConnectionManager, NOTIFICATION_CHANNEL, process_notification_task
import json
import logging
from api.core.logging_config import get_logger
from api.db.database import SessionLocal
from .models import Notification, NotificationStatus
from .schemas import NotificationCreate, NotificationUpdate, Notifications, NotificationResponse
from sqlalchemy import desc
from datetime import datetime, timezone
import redis.asyncio as redis
from config import config
import redis.asyncio as aioredis
from typing import Optional
from .exceptions import logger
import asyncio

REDIS_URL = config.REDIS_URL



class NotificationService:

    def publish_notification(self, user_id: int, notification:NotificationCreate, notification_type:Optional[str] = None):
        try:

            notification_data = {
                "title": notification.title,
                "message": notification.message,
                "type": notification.type if notification.type else "transaction",
                "priority": notification.priority if notification.priority else "normal",
                "metadata": notification.metadata,
                "action_url": notification.action_url
            }

            process_notification_task.delay(user_id=user_id, noti=notification_data)
        except Exception as e:
            logging.error(f"Notification queuing failed: {str(e)}")
            return False
        return True

    """
    def create_notification(self, db: Session, user_id: int, message: str):
        notification = Notification(user_id=user_id, message=message)

        db.add(notification)
        db.commit()
        db.refresh(notification)

        return notification
    """
    async def create_notification(self, db: AsyncSession, user_id: int,
                                  notification: NotificationCreate) -> NotificationResponse:
        """Create a new notification"""
        try:
            db_notification = Notification(
                user_id=user_id,
                title=notification.title,
                message=notification.message,
                type=notification.type,
                priority=notification.priority,
                noti_metadata=notification.metadata,
                action_url=notification.action_url
            )
            db.add(db_notification)
            await db.commit()
            await db.refresh(db_notification)

            # Trigger real-time notification delivery
            self.publish_notification(user_id, db_notification.to_dict())
            return NotificationResponse.model_validate(db_notification)

        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create notification: {str(e)}"
            )


    async def get_user_notifications(self, db: AsyncSession,
                                     user_id: int, skip: int = 0,
                                     limit: int = 50, include_read: bool = False
                                     ) -> List[Notifications]:
        """Get user's notifications"""
        query = select(Notification).where(
            Notification.user_id == user_id,
            Notification.is_deleted == False
        )

        if not include_read:
            query = query.where(Notification.is_read == False)

        query = query.order_by(desc(Notification.created_at))\
                    .offset(skip)\
                    .limit(limit)

        result = await db.execute(query)
        notifications = result.scalars().all()
        notifications = list(reversed(notifications))
        return [Notifications.model_validate(n) for n in notifications]


    async def get_notification(self, db: AsyncSession, notification_id: int, user_id: int) -> NotificationResponse:
        """Get a single notification"""
        result = await db.execute(select(Notification).where(
            Notification.id == notification_id,
            Notification.user_id == user_id,
            Notification.is_deleted == False
        ))
        notification = result.scalar_one_or_none()

        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )

        if notification.is_read == False:
            notification.read_at = datetime.now(timezone.utc)
            notification.status = NotificationStatus.READ

        await db.commit()
        await db.refresh(notification)


        # Convert notification to dict and handle metadata conversion
        notification_dict = {
            "id": notification.id,
            "user_id": notification.user_id,
            "title": notification.title,
            "message": notification.message,
            "type": notification.type,
            "priority": notification.priority,
            "status": notification.status,
            "is_read": notification.is_read,
            "metadata": notification.noti_metadata if notification.noti_metadata else {},  # Convert None to empty dict
            "action_url": notification.action_url,
            "created_at": notification.created_at,
            "updated_at": notification.updated_at,
            "read_at": notification.read_at,
            "delivered_at": notification.delivered_at
        }

        return NotificationResponse.model_validate(notification_dict)
        #return NotificationResponse.model_validate(notification)

    async def update_notification( self, db: AsyncSession, notification_id: int,
                                  user_id: int, update_data: NotificationUpdate
    ) -> NotificationResponse:
        """Update a notification"""
        result = await db.execute(select(Notification).where(
            Notification.id == notification_id,
            Notification.user_id == user_id
        ))
        notification = result.scalar_one_or_none()

        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Notification not found"
            )

        for field, value in update_data.model_dump(exclude_unset=True).items():
            setattr(notification, field, value)

        if update_data.is_read:
            notification.read_at = datetime.now(timezone.utc)
            notification.status = NotificationStatus.READ

        await db.commit()
        await db.refresh(notification)
        return NotificationResponse.model_validate(notification)

    async def mark_all_read(self, db: AsyncSession, user_id: int) -> int:
        """Mark all user's notifications as read"""
        stmt = select(Notification).where(
            Notification.user_id == user_id,
            Notification.is_read == False
        )
        result = await db.execute(stmt)
        notifications = result.scalars().all()

        for notification in notifications:
            notification.is_read = True
            notification.read_at = datetime.now(timezone.utc)
            notification.status = NotificationStatus.READ
            notification.updated_at = datetime.now(timezone.utc)

        await db.commit()
        return len(notifications)



    async def delete_notification(self, db: AsyncSession, notification_id: int, user_id: int) -> bool:
        """Soft delete a notification"""
        try:
            stmt = select(Notification).where(
                Notification.id == notification_id,
                Notification.user_id == user_id,
                Notification.is_deleted == False
            )
            result = await db.execute(stmt)
            notification = result.scalar_one_or_none()

            if not notification:
                return False

            notification.is_deleted = True
            notification.status = NotificationStatus.DELETED
            notification.updated_at = datetime.now(timezone.utc)

            await db.commit()
            return True

        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to delete notification: {str(e)}"
            )

    async def bulk_delete_notifications(self, db: AsyncSession, user_id: int,
                                        notification_ids: List[int] = None) -> int:
        """Soft delete multiple notifications"""
        try:
            stmt = select(Notification).where(
                Notification.user_id == user_id,
                Notification.is_deleted == False
            )

            if notification_ids:
                stmt = stmt.where(Notification.id.in_(notification_ids))

            result = await db.execute(stmt)
            notifications = result.scalars().all()

            for notification in notifications:
                notification.is_deleted = True
                notification.status = NotificationStatus.DELETED
                notification.updated_at = datetime.now(timezone.utc)

            await db.commit()
            return len(notifications)

        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to bulk delete notifications: {str(e)}"
            )

    """
    def get_unread_notifications(self, db: Session, user_id: int) -> List[NotificationSchema]:
        "
        Fetch all unread notifications for a user.
        "
        try:
            unread_notifications = (
                db.query(Notification)
                .filter(Notification.user_id == user_id, Notification.is_read == False)
                .all()
            )
            return [NotificationSchema.model_validate(notification) for notification in unread_notifications]
        except Exception as e:
            db.rollback()
            raise HTTPException(status_code=400, detail=str(e))


    def mark_notifications_read(self, db: Session, user_id: int):
        "
        Mark all notifications as read for a user.
        "
        db.query(Notification).filter(Notification.user_id == user_id).update(
            {"is_read": True}, synchronize_session=False
        )
        db.commit()



    def mark_notification(self, id:int, db: Session, user: user_schema.User):
        "
        Mark all notifications as read for a user.
        "
        db.query(Notification).filter(Notification.id == id,
                                      Notification.user_id == user.id
                                      ).update(
            {"is_read": True}, synchronize_session=False
        )
        db.commit()
    """


class NotificationManger:
    def __init__(self):
        self.connection_manager = ConnectionManager()
        self.notification_service = NotificationService()
        self.redis_client = None
        self.pubsub = None

        self.notification_service = NotificationService()
        self.redis_client: Optional[aioredis.Redis] = None # Use async type hint
        self.pubsub: Optional[aioredis.client.PubSub] = None # Use async type hint
        self._redis_listener_task: Optional[asyncio.Task] = None

    async def initialize(self):
        """Initialize Redis connection and subscribe to notifications"""
        try:
            #self.redis_client = redis.Redis.from_url(REDIS_URL)
            self.redis_client = aioredis.Redis.from_url(REDIS_URL, decode_responses=True)
            await self.redis_client.ping()

            self.pubsub = self.redis_client.pubsub(ignore_subscribe_messages=True)
            #self.pubsub = self.redis_client.pubsub()
            await self.pubsub.subscribe(NOTIFICATION_CHANNEL)
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {str(e)}")

    async def listen_for_notifications(self):
        """Listen for notifications from Redis"""
        try:
            async for message in self.pubsub.listen():
                if message["type"] == "message":
                    try:
                        data = json.loads(message["data"])
                        user_id = data.get("user_id")
                        notification_message = data.get("message")
                        notification_id = data.get("notification_id")

                        if user_id and notification_message:
                            await self.connection_manager.send_notification(
                                user_id=user_id,
                                notifications=notification_message,
                                notification_id=notification_id
                            )
                            logger.info(f"Delivered notification to connections for user {user_id}")
                    except Exception as e:
                        logger.error(f"Error processing notification: {str(e)}")
        except Exception as e:
            logger.error(f"Redis subscription error: {str(e)}")
        finally:
            if self.pubsub:
                await self.pubsub.unsubscribe(NOTIFICATION_CHANNEL)



    async def handle_websocket(self, websocket: WebSocket, user_id: int):
        """Handle individual WebSocket connections"""
        try:
            await self.connection_manager.connect(websocket, user_id)
            #await websocket.send_text(json.dumps("Connected"))
            await websocket.send_json({
                "type": "connection_status",
                "status": "connected",
                "timestamp": datetime.now().isoformat()
            })

            while True:
                try:
                    data = await websocket.receive_json()
                    message_type = data.get("type")

                    if message_type == "notification:new":
                        async with SessionLocal() as new_db:
                            notifications = await self.notification_service.get_user_notifications(
                                db=new_db,
                                user_id=user_id,
                                skip=data.get("skip", 0),
                                limit=data.get("limit", 50),
                                include_read=data.get("include_read", False)
                            )

                            for notification in notifications:
                                notification_dict = {
                                    "title": notification.title,
                                    "message": notification.message,
                                    "type": notification.type.value if hasattr(notification.type, 'value') else notification.type,
                                    "priority": notification.priority.value if hasattr(notification.priority, 'value') else notification.priority,
                                    "is_read": notification.is_read,
                                    "created_at": notification.created_at.isoformat() if notification.created_at else None,
                                }

                                await self.connection_manager.send_notification(
                                    user_id=user_id,
                                    notifications=notification_dict,
                                    notification_id=notification.id
                                )

                            # Send summary after all notifications
                            await websocket.send_json({
                                "type": "notification_summary",
                                "count": len(notifications),
                                "timestamp": datetime.now().isoformat()
                            })

                    if message_type == "notification:read":
                        #with SessionLocal() as new_db:
                            #self.notification_service.mark_notifications_read(new_db, user_id)
                        #notification_id = data.get("notification_id")
                        async with SessionLocal() as new_db:
                            if notification_id := data.get("notification_id"):
                                # Mark specific notification as read
                                await self.notification_service.update_notification(
                                    db=new_db,
                                    notification_id=notification_id,
                                    user_id=user_id,
                                    update_data=NotificationUpdate(is_read=True)
                                )
                            else:
                                # Mark all as read
                                count = await self.notification_service.mark_all_read(
                                    db=new_db,
                                    user_id=user_id
                                )
                                await websocket.send_json({
                                    "type": "notifications_marked_read",
                                    "count": count,
                                    "timestamp": datetime.now().isoformat()
                                })

                    if message_type == "notification:delete":
                        async with SessionLocal() as new_db:
                            if notification_ids := data.get("notification_ids"):
                                count = await self.notification_service.bulk_delete_notifications(
                                    new_db, user_id, notification_ids
                                )
                                await websocket.send_json({
                                    "type": "notifications_deleted",
                                    "count": count,
                                    "ids": notification_ids,
                                    "timestamp": datetime.now().isoformat()
                                })
                            elif notification_id := data.get("notification_id"):
                                deleted = await self.notification_service.delete_notification(
                                    new_db, notification_id, user_id
                                )
                                await websocket.send_json({
                                    "type": "notification_deleted",
                                    "success": deleted,
                                    "id": notification_id,
                                    "timestamp": datetime.now().isoformat()
                                })


                    elif message_type == "ping":
                        # Handle heartbeat
                        await websocket.send_json({
                            "type": "pong",
                            "timestamp": datetime.now().isoformat()
                        })

                except json.JSONDecodeError:
                    await websocket.send_json({
                        "type": "error",
                        "message": "Invalid JSON format",
                        "timestamp": datetime.now().isoformat()
                    })


        except WebSocketDisconnect:
            logger.info(f"WebSocket disconnected for user {user_id}")
            await self.connection_manager.disconnect(websocket)
        except Exception as e:
            logger.error(f"WebSocket error for user {user_id}: {e}")
            # Send error message to client before disconnecting
            try:
                await websocket.send_json({
                    "type": "error",
                    "message": "Internal server error",
                    "timestamp": datetime.now().isoformat()
                })
            except:
                pass
            await self.connection_manager.disconnect(websocket)


notification_manager = NotificationManger()