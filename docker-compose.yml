services:
  web:
    build: .
    restart: always  # Add restart policy
    ports:
      - "8000:8000"
    env_file: .env
    depends_on:
      - redis
      - celery-worker
      - celery-beat
    volumes:
      - ./logs:/atlas-api/logs

  redis:
    image: redis:alpine
    restart: always  # Add restart policy to Redis
    ports:
      - "6379:6379"

  celery-worker:
    build: .
    command: celery -A api.core.tasks worker --loglevel=INFO --pool=gevent --concurrency=${CELERY_WORKER_CONCURRENCY:-100} --max-tasks-per-child=1000
        
    env_file: .env
    depends_on:
      - redis
    volumes:
      - ./logs:/atlas-api/logs
    restart: always

  celery-beat:
    build: .
    command: celery -A api.core.tasks beat --loglevel=INFO

    env_file: .env
    depends_on:
      - redis
    volumes:
      - ./logs:/atlas-api/logs
    restart: always

volumes:
  logs: