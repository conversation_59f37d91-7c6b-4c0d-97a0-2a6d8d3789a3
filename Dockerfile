# Use an official Python runtime as a parent image
FROM python:3.12-slim

# Install PostgreSQL client tools
RUN apt-get update && \
    apt-get install -y postgresql-client && \
    rm -rf /var/lib/apt/lists/*

# Set the working directory in the container
WORKDIR /atlas-api

# Copy requirements first to leverage Docker cache
COPY requirements.txt /atlas-api/

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy the current directory contents into the container at /atlas-api
COPY . /atlas-api

# Create necessary directories
RUN mkdir -p /atlas-api/services /atlas-api/logs

# Make service scripts executable
#RUN chmod +x /atlas-api/services/gunicorn.sh && \
    #chmod +x /atlas-api/services/celery-worker.sh && \
    #chmod +x /atlas-api/services/celery-beat.sh

# Make port 8000 available to the world outside the container
EXPOSE 8000

# Default to running the web server if no other command is specified
#CMD ["./services/gunicorn.sh"]


RUN chmod +x /atlas-api/startup.sh

# Run the start script
CMD ["/atlas-api/startup.sh"]