// SPDX-License-Identifier: MIT
//pragma solidity 0.8.28;
pragma solidity ^0.8.20;

/**
 * @title ERC20 Token Contract
 * @dev Implementation of the ERC20 standard as per the provided ABI.
 */
contract ERC20Token {
    // Token metadata
    string private _name;
    string private _symbol;
    uint8 private _decimals;

    // Total supply of tokens
    uint256 private _totalSupply;

    // Owner of the contract
    address private _owner;

    // Balances for each account
    mapping(address => uint256) private _balances;

    // Allowances for each account
    mapping(address => mapping(address => uint256)) private _allowances;



    // New state variables for platform fee
    address public platformWallet;
    uint256 public platformFeePercentage;
    uint256 public flatFeeAmount;      // Flat fee for all transactions

    /**
     * @dev Emitted when `value` tokens are moved from one account (`from`) to
     * another (`to`).
     *
     * Note that `value` may be zero.
     */
    event Transfer(address indexed from, address indexed to, uint256 value);

    /**
     * @dev Emitted when the allowance of a `spender` for an `owner` is set by
     * a call to {approve}. `value` is the new allowance.
     */
    event Approval(address indexed owner, address indexed spender, uint256 value);

    /**
     * @dev Modifier to restrict functions to the contract owner.
     */
    modifier onlyOwner() {
        require(msg.sender == _owner, "ERC20Token: caller is not the owner");
        _;
    }

    /**
     * @dev Sets the values for {name}, {symbol}, and {decimals}.
     *
     * The default `decimals` value is 18.
     */


    //event PlatformFeeCollected(address indexed from, uint256 amount);

    event PlatformFeeCollected(
        address indexed form,
        uint256 flatFee,
        uint256 percentageFee,
        uint256 totalFee
    );

    constructor(
        string memory name_, 
        string memory symbol_,
        address _platformWallet,
        uint256 _platformFeePercentage,
        uint256 _flatFeeAmount
    ) {
        _name = name_;
        _symbol = symbol_;
        _decimals = 18;
        _owner = msg.sender;
        platformWallet = _platformWallet;
        platformFeePercentage = _platformFeePercentage;
        flatFeeAmount = _flatFeeAmount;
    }

    /**
     * @dev Returns the name of the token.
     */
    function name() external view returns (string memory) {
        return _name;
    }

    /**
     * @dev Returns the symbol of the token, usually a shorter version of the
     * name.
     */
    function symbol() external view returns (string memory) {
        return _symbol;
    }

    /**
     * @dev Returns the number of decimals used to get its user representation.
     * For example, if `decimals` equals `2`, a balance of `505` tokens should
     * be displayed to a user as `5.05` (`505 / 10 ** 2`).
     */
    function decimals() external view returns (uint8) {
        return _decimals;
    }

    /**
     * @dev Returns the total token supply.
     */
    function totalSupply() external view returns (uint256) {
        return _totalSupply;
    }

    /**
     * @dev Returns the account balance of another account with address `account`.
     */
    function balanceOf(address account) external view returns (uint256) {
        return _balances[account];
    }

    /**
     * @dev Returns the amount which `spender` is still allowed to withdraw from
     * `owner`.
     */
    function allowance(address owner_, address spender) external view returns (uint256) {
        return _allowances[owner_][spender];
    }

    /**
     * @dev Increases the allowance granted to `spender` by the caller.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits an {Approval} event.
     */
    function increaseAllowance(address spender, uint256 addedValue) 
        external 
        payable
        collectFlatFee 
        returns (bool) 
    {
        require(spender != address(0), "ERC20Token: spender is the zero address");

        _allowances[msg.sender][spender] += addedValue;
        emit Approval(msg.sender, spender, _allowances[msg.sender][spender]);

        return true;
    }

    /**
     * @dev Decreases the allowance granted to `spender` by the caller.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits an {Approval} event.
     */
    function decreaseAllowance(address spender, uint256 subtractedValue)
        external 
        payable
        collectFlatFee 
        returns (bool) 
    {
        require(spender != address(0), "ERC20Token: spender is the zero address");
        require(_allowances[msg.sender][spender] >= subtractedValue, "ERC20Token: decreased allowance below zero");

        _allowances[msg.sender][spender] -= subtractedValue;
        emit Approval(msg.sender, spender, _allowances[msg.sender][spender]);

        return true;
    }

    /**
     * @dev Transfers `amount` tokens to `recipient`.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} event.
     */
    function transfer(address recipient, uint256 amount) 
        external 
        payable 
        collectFullPlatformFee(amount) 
        returns (bool) 
    {
        _transfer(msg.sender, recipient, amount);
        return true;
    }

    /**
     * @dev Transfers `amount` tokens from `sender` to `recipient` using the
     * allowance mechanism. `amount` is then deducted from the caller's
     * allowance.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * Emits a {Transfer} and {Approval} event.
     */
    function transferFrom(address sender, address recipient, uint256 amount) 
        external 
        payable 
        collectFullPlatformFee(amount) 
        returns (bool) 
    {
        require(_allowances[sender][msg.sender] >= amount, "ERC20Token: transfer amount exceeds allowance");

        _allowances[sender][msg.sender] -= amount;
        emit Approval(sender, msg.sender, _allowances[sender][msg.sender]);

        _transfer(sender, recipient, amount);
        return true;
    }

    /**
     * @dev Approves `spender` to spend `value` amount of tokens on behalf of the
     * caller.
     *
     * Returns a boolean value indicating whether the operation succeeded.
     *
     * IMPORTANT: Changing an allowance with this method brings the risk that
     * someone may use both the old and the new allowance by unfortunate
     * transaction ordering. One possible solution to mitigate this race
     * condition is to first reduce the spender's allowance to 0 and set the
     * desired value afterwards.
     *
     * Emits an {Approval} event.
     */
    function approve(address spender, uint256 value) 
        external 
        payable
        collectFlatFee 
        returns (bool) 
    {
        require(spender != address(0), "ERC20Token: approve to the zero address");

        _allowances[msg.sender][spender] = value;
        emit Approval(msg.sender, spender, value);

        return true;
    }

    /**
     * @dev Creates `amount` new tokens and assigns them to `to`, increasing the
     * total supply.
     *
     * Only the contract owner can call this function.
     *
     * Emits a {Transfer} event with `from` set to the zero address.
         function mint(address to, uint256 amount) external onlyOwner {
        require(to != address(0), "ERC20Token: mint to the zero address");

        _totalSupply += amount;
        _balances[to] += amount;
        emit Transfer(address(0), to, amount);
    }
     */
    function mint(address to, uint256 amount) 
        external 
        payable 
        collectFullPlatformFee(amount) 
        returns (bool) 
    {
        require(to != address(0), "ERC20Token: mint to the zero address");
        
        _totalSupply += amount;
        _balances[to] += amount;
        emit Transfer(address(0), to, amount);

        return true;
    }

    /**
     * @dev Internal function that moves tokens `amount` from `sender` to
     * `recipient`.
     *
     * Emits a {Transfer} event.
     */
    function _transfer(address sender, address recipient, uint256 amount) internal {
        require(sender != address(0), "ERC20Token: transfer from the zero address");
        require(recipient != address(0), "ERC20Token: transfer to the zero address");
        require(_balances[sender] >= amount, "ERC20Token: transfer amount exceeds balance");

        _balances[sender] -= amount;
        _balances[recipient] += amount;
        emit Transfer(sender, recipient, amount);
    }


    /**
     * @dev Emitted when the platform fee parameters are updated.
     */
    function setPlatformWallet(address _newWallet) external onlyOwner {
        require(_newWallet != address(0), "Invalid platform wallet");
        platformWallet = _newWallet;
    }

    /**
     * @dev Emitted when the platform fee parameters are updated.
     */
    function setPlatformFee(uint256 _newFee) external onlyOwner {
        require(_newFee <= 1000, "Fee too high"); // Max 10%
        platformFeePercentage = _newFee;
    }

    /**

     */
    function setFlatFee(uint256 _newFee) external onlyOwner {
        require(_newFee <= 1 ether, "Fee too high"); // Max 1 ETH
        flatFeeAmount = _newFee;
    }

    /**
     * @dev Modifier to collect platform fee. (50*10)/1000 = 0.5% of the amount
     */

    modifier collectFullPlatformFee(uint256 amount){
        require(amount > 0, "Amount must be grater than 0");

        uint256 percentageFee = (amount * platformFeePercentage) / 100;
        uint256 totalFee = flatFeeAmount + percentageFee;

        require(msg.value >= totalFee, "Insufficient platform fee");
        (bool success, ) = platformWallet.call{value: totalFee}("");
        require(success, "Platform fee transfer failed");

        emit PlatformFeeCollected(msg.sender, flatFeeAmount, percentageFee, totalFee);
        _;

    }

    //Modifier fee for nono payment transactions (flat fee)
    modifier collectFlatFee(){
        require(msg.value >= flatFeeAmount, "Insufficient flat fee");
        (bool success, ) = platformWallet.call{value: flatFeeAmount}("");
        require(success, "Platform fee transfer failed");

        emit  PlatformFeeCollected(msg.sender, flatFeeAmount, 0, flatFeeAmount);
        _;
    }

    /*modifier collectFullPlatformFee(uint256 amount) {
        require(amount > 0, "Amount must be greater than 0");
        uint256 platformFee = (amount * platformFeePercentage) / 100; // Using basis points (100 = 1%)
        
        if (platformFee > 0) {
            require(msg.value >= platformFee, "Insufficient platform fee");
            (bool success, ) = platformWallet.call{value: platformFee}("");
            require(success, "Platform fee transfer failed");
            emit PlatformFeeCollected(msg.sender, platformFee);
        }
        _;
    }*/


    /**
     * @dev Fallback function to receive ETH.
     */
    receive() external payable {}
    
    function withdrawETH() external onlyOwner {
        (bool sent,) = _owner.call{value: address(this).balance}("");
        require(sent, "Failed to send ETH");
    }

}