import pytest
import pytest_asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from web3 import AsyncWeb3, Web3
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from api.v1.deploy_contracts.models import ContractDeployment, TransactionType
from api.v1.contracts.services import ChainID
from api.v1.contracts.services_events import BlockEvents
from api.v1.contracts.services_contract_chart import ContractChat
from tests.v1.test_account import TEST_PROVIDER

# Test constants
TEST_CONTRACT_ADDRESS = "0x742d35Cc6634C0532925a3b844Bc454e4438f44e"
TEST_BLOCK_NUMBER = 1000000

@pytest_asyncio.fixture
async def mock_redis():
    """Mock Redis for testing"""
    class MockRedis:
        def __init__(self):
            self.data = {}
            self.get = AsyncMock()
            self.set = AsyncMock()
            self.expire = AsyncMock()
            self.delete = AsyncMock()

        async def get(self, key):
            return self.data.get(key)

        async def set(self, key, value, ex=None):
            self.data[key] = value
            return True

        async def delete(self, key):
            if key in self.data:
                del self.data[key]
            return True

        async def expire(self, key, time):
            return True

    return MockRedis()

@pytest_asyncio.fixture
async def mock_web3():
    """Mock Web3 for testing"""
    mock = AsyncMock()
    mock.eth = AsyncMock()
    mock.eth.chain_id = 137  # Polygon testnet
    mock.eth.get_block_number = AsyncMock(return_value=TEST_BLOCK_NUMBER + 1000)
    mock.eth.get_block = AsyncMock(return_value={'timestamp': int(datetime.now().timestamp())})
    mock.eth.get_logs = AsyncMock(return_value=[])
    mock.eth.get_transaction = AsyncMock(return_value={'from': TEST_CONTRACT_ADDRESS})
    mock.eth.get_balance = AsyncMock(return_value=Web3.to_wei(1, 'ether'))
    mock.to_hex = lambda x: f"0x{x.hex() if isinstance(x, bytes) else x}"
    return mock

@pytest_asyncio.fixture
async def test_contract(async_db: AsyncSession, test_verified_user):
    """Create a test contract deployment"""
    contract = ContractDeployment(
        user_id=test_verified_user["id"],
        contract_address=TEST_CONTRACT_ADDRESS,
        contract_type=TransactionType.ERC20,
        block_number=TEST_BLOCK_NUMBER,
        created_at=datetime.now(timezone.utc)
    )
    async_db.add(contract)
    await async_db.commit()
    await async_db.refresh(contract)
    return contract

@pytest.mark.asyncio
async def test_get_network_id(test_verified_user, mock_web3, mock_redis):
    """Test getting network chain ID"""
    # Mock the web3 chain_id call
    mock_web3.eth.chain_id = 137  # Polygon mainnet chain ID
    
    chain_id_service = ChainID(
        user_id=test_verified_user["id"],
        web3=mock_web3,
        redis=mock_redis
    )
    
    network_id = await chain_id_service.get_network_id()
    assert network_id == 137

@pytest.mark.asyncio
async def test_get_contract_events(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test getting contract events"""
    # Mock web3 get_logs and other contract calls
    mock_logs = [
        {
            'address': TEST_CONTRACT_ADDRESS,
            'blockHash': '0x...', 
            'blockNumber': TEST_BLOCK_NUMBER,
            'data': '0x...',
            'logIndex': 0,
            'removed': False,
            'topics': ['0x...'],
            'transactionHash': '0x...',
            'transactionIndex': 0
        }
    ]
    
    mock_web3.eth.get_logs.return_value = mock_logs
    mock_web3.eth.get_block.return_value = {'timestamp': int(datetime.now().timestamp())}
    mock_web3.eth.get_transaction.return_value = {'from': TEST_CONTRACT_ADDRESS}

    response = await client.get(
        f"/contract-events?contract_id={test_contract.id}&order=desc&page=1&page_size=10",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    assert "events" in data
    assert "pagination" in data

@pytest.mark.asyncio
async def test_get_contract_chart(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test getting contract chart data"""
    # Mock blockchain calls
    mock_web3.eth.get_block_number.return_value = TEST_BLOCK_NUMBER + 1000
    mock_web3.eth.get_logs.return_value = []
    
    response = await client.get(
        f"/contract/chart?freq=d&contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "data" in data
    assert "weekly_rate_change" in data

@pytest.mark.asyncio
async def test_get_analytics_chart(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test getting contract analytics"""
    mock_web3.eth.get_block_number.return_value = TEST_BLOCK_NUMBER + 1000
    mock_web3.eth.get_logs.return_value = []
    
    response = await client.get(
        f"/contract/analytics/?freq=d&contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "uniqueWallets_data" in data
    assert "overall_totals" in data

@pytest.mark.asyncio
async def test_get_contract_metrics(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test getting contract metrics"""
    # Mock the necessary web3 calls
    mock_web3.eth.get_balance.return_value = Web3.to_wei(1, 'ether')
    mock_web3.eth.get_block_number.return_value = TEST_BLOCK_NUMBER + 1000
    mock_web3.eth.get_logs.return_value = []
    
    response = await client.get(
        f"/contract/metrics/?contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, dict)
    assert "total_net_worth" in data
    assert "total_transactions" in data
    assert "total_gas_spent" in data
    assert "unique_users" in data

@pytest.mark.asyncio
async def test_invalid_contract_id(client, test_verified_user, async_db, mock_web3, mock_redis):
    """Test accessing contract endpoints with invalid contract ID"""
    invalid_contract_id = 99999
    
    endpoints = [
        f"/contract-events?contract_id={invalid_contract_id}&order=desc&page=1&page_size=10",
        f"/contract/chart?freq=d&contract_id={invalid_contract_id}",
        f"/contract/analytics/?freq=d&contract_id={invalid_contract_id}",
        f"/contract/metrics/?contract_id={invalid_contract_id}"
    ]
    
    for endpoint in endpoints:
        response = await client.get(
            endpoint,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        assert response.status_code == 404

@pytest.mark.asyncio
async def test_unauthorized_access(client, test_contract):
    """Test accessing contract endpoints without authentication"""
    endpoints = [
        f"/contract-events?contract_id={test_contract.id}&order=desc&page=1&page_size=10",
        f"/contract/chart?freq=d&contract_id={test_contract.id}",
        f"/contract/analytics/?freq=d&contract_id={test_contract.id}",
        f"/contract/metrics/?contract_id={test_contract.id}"
    ]
    
    for endpoint in endpoints:
        response = await client.get(endpoint)
        assert response.status_code == 401
        assert "detail" in response.json()

@pytest.mark.asyncio
async def test_get_contract_users(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test getting contract users data"""
    # Mock blockchain data
    mock_web3.eth.get_block_number.return_value = TEST_BLOCK_NUMBER + 1000
    mock_web3.eth.get_logs.return_value = [
        {
            'address': TEST_CONTRACT_ADDRESS,
            'blockHash': '0x...', 
            'blockNumber': TEST_BLOCK_NUMBER,
            'data': '0x...',
            'logIndex': 0,
            'removed': False,
            'topics': ['0x...'],
            'transactionHash': '0x...',
            'transactionIndex': 0
        }
    ]
    mock_web3.eth.get_block.return_value = {'timestamp': int(datetime.now().timestamp())}
    mock_web3.eth.get_transaction.return_value = {'from': TEST_CONTRACT_ADDRESS}
    
    response = await client.get(
        f"/contract/users/?freq=d&contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "uniqueWallets_data" in data
    assert "overall_totals" in data

@pytest.mark.asyncio
async def test_get_unique_users(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test getting unique users who have interacted with the contract"""
    mock_web3.eth.get_block_number.return_value = TEST_BLOCK_NUMBER + 1000
    mock_web3.eth.get_logs.return_value = [
        {
            'address': TEST_CONTRACT_ADDRESS,
            'blockNumber': TEST_BLOCK_NUMBER,
            'transactionHash': '0x...',
            'logIndex': 0,
            'topics': ['0x...']
        }
    ]
    mock_web3.eth.get_transaction.return_value = {'from': TEST_CONTRACT_ADDRESS}
    
    response = await client.get(
        f"/contract/unique-users?contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "total_unique_users" in data
    assert "users" in data

@pytest.mark.asyncio
async def test_get_history_chart(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test getting contract history chart"""
    mock_web3.eth.get_block_number.return_value = TEST_BLOCK_NUMBER + 1000
    mock_web3.eth.get_logs.return_value = []
    
    response = await client.get(
        f"/contract/users/history?freq=d&contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "data" in data
    assert "weekly_rate_change" in data

@pytest.mark.asyncio
async def test_error_handling_blockchain_unavailable(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test error handling when blockchain is unavailable"""
    mock_web3.eth.get_block_number.side_effect = Exception("Blockchain connection error")
    
    endpoints = [
        f"/contract/chart?freq=d&contract_id={test_contract.id}",
        f"/contract/analytics/?freq=d&contract_id={test_contract.id}",
        f"/contract/users/?freq=d&contract_id={test_contract.id}",
        f"/contract/metrics/?contract_id={test_contract.id}"
    ]
    
    for endpoint in endpoints:
        response = await client.get(
            endpoint,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        assert response.status_code in [502, 500]  # Depending on error handling configuration
        assert "detail" in response.json()

@pytest.mark.asyncio
async def test_cache_behavior(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test caching behavior of contract endpoints"""
    # First call should cache the result
    response1 = await client.get(
        f"/contract/chart?freq=d&contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    # Modify mock data to ensure we're getting cached result
    mock_web3.eth.get_block_number.return_value = TEST_BLOCK_NUMBER + 2000
    
    # Second call should return cached data
    response2 = await client.get(
        f"/contract/chart?freq=d&contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response1.json() == response2.json()
    
    # Verify Redis was used
    assert await mock_redis.get.call_count > 0

@pytest.mark.asyncio
async def test_invalid_frequency_parameter(client, test_verified_user, test_contract):
    """Test handling of invalid frequency parameter"""
    endpoints = [
        f"/contract/chart?freq=invalid&contract_id={test_contract.id}",
        f"/contract/analytics/?freq=invalid&contract_id={test_contract.id}",
        f"/contract/users/?freq=invalid&contract_id={test_contract.id}",
        f"/contract/users/history?freq=invalid&contract_id={test_contract.id}"
    ]
    
    for endpoint in endpoints:
        response = await client.get(
            endpoint,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        assert response.status_code == 422  # Validation error

@pytest.mark.asyncio
async def test_large_dataset_handling(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test handling of large datasets"""
    # Generate large number of mock logs
    mock_logs = []
    for i in range(1000):  # Simulate 1000 events
        mock_logs.append({
            'address': TEST_CONTRACT_ADDRESS,
            'blockNumber': TEST_BLOCK_NUMBER + i,
            'transactionHash': f'0x{i:064x}',
            'logIndex': i,
            'topics': ['0x...'],
            'data': '0x...',
            'removed': False
        })
    
    mock_web3.eth.get_logs.return_value = mock_logs
    mock_web3.eth.get_block.return_value = {'timestamp': int(datetime.now().timestamp())}
    mock_web3.eth.get_transaction.return_value = {'from': TEST_CONTRACT_ADDRESS}
    
    response = await client.get(
        f"/contract/analytics/?freq=d&contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    # Verify pagination and data chunking worked
    assert len(data["uniqueWallets_data"]) > 0

@pytest.mark.asyncio
async def test_contract_concurrent_requests(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test handling of concurrent requests to contract endpoints"""
    import asyncio
    
    # Prepare multiple concurrent requests
    async def make_request():
        return await client.get(
            f"/contract/chart?freq=d&contract_id={test_contract.id}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
    
    # Make 5 concurrent requests
    responses = await asyncio.gather(*[make_request() for _ in range(5)])
    
    # Verify all requests were successful
    for response in responses:
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"

@pytest.mark.asyncio
async def test_empty_contract_handling(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test handling of contracts with no events"""
    mock_web3.eth.get_logs.return_value = []
    
    endpoints = [
        f"/contract/chart?freq=d&contract_id={test_contract.id}",
        f"/contract/analytics/?freq=d&contract_id={test_contract.id}",
        f"/contract/users/?freq=d&contract_id={test_contract.id}",
        f"/contract/unique-users?contract_id={test_contract.id}"
    ]
    
    for endpoint in endpoints:
        response = await client.get(
            endpoint,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        # Verify proper empty state handling
        if "data" in data:
            assert len(data["data"]) == 0
        if "users" in data:
            assert len(data["users"]) == 0

@pytest.mark.asyncio
async def test_redis_failure_handling(client, test_verified_user, test_contract, async_db, mock_web3, mock_redis):
    """Test system behavior when Redis is unavailable"""
    # Make Redis operations fail
    mock_redis.get.side_effect = Exception("Redis connection error")
    mock_redis.set.side_effect = Exception("Redis connection error")
    
    # System should still work without Redis
    response = await client.get(
        f"/contract/chart?freq=d&contract_id={test_contract.id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"