from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from . import models, schemas
from fastapi import HTT<PERSON>Ex<PERSON>, status
from datetime import datetime, timedelta, timezone
from .models import SubscriptionPlan, SubscriptionTier
from api.v1.user import schemas as user_schema
from api.core import responses
from typing import Optional, List
from api.v1.auth.models import BlackListToken, APIKey



class Subscription():

    async def create_subscription_plan(self, tier: schemas.SubscriptionTier, plan: schemas.SubscriptionPlanCreate, db: AsyncSession) -> schemas.SubscriptionPlanBase:

        try:
            # Use SQLAlchemy's future select() instead of db.query
            result = await db.execute(
                select(SubscriptionPlan).filter(SubscriptionPlan.tier == tier.value)
            )
            existing_plan = result.scalar_one_or_none()

            if existing_plan:
                raise ValueError(f"A plan for {tier.value} tier already exists")

            new_plan = SubscriptionPlan(
                tier=tier.value,
                name=plan.name,
                description=plan.description or f"{tier.value.capitalize()} Tier Subscription",
                price_monthly=plan.price_monthly,
                price_yearly=plan.price_yearly,
                api_rate_limit=plan.api_rate_limit,
                daily_request_limit=plan.daily_request_limit,
                monthly_request_limit=plan.monthly_request_limit,
                allowed_endpoints=plan.allowed_endpoints or [],
                max_payload_size=plan.max_payload_size,
                has_priority_support=plan.has_priority_support or False,
                has_advanced_features=plan.has_advanced_features or False,
                concurrent_requests=plan.concurrent_requests,
                max_response_time=plan.max_response_time
            )

            db.add(new_plan)
            await db.commit()
            await db.refresh(new_plan)

            return new_plan

        except HTTPException as e:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating subscription: {str(e)}"
            )


    async def update_subscription_plan(self, plan_data: schemas.SubscriptionPlanUpdate, tier: schemas.SubscriptionTier, db: AsyncSession
                                 ) -> SubscriptionPlan:
        try:
            # Use SQLAlchemy's future select() instead of db.query
            result = await db.execute(
                select(SubscriptionPlan).filter(SubscriptionPlan.tier == tier)
            )
            plan = result.scalar_one_or_none()

            if not plan:
                raise ValueError("Subscription plan not found.")

            if tier is not None:
                plan.tier = tier.value

            if plan_data.name is not None:
                plan.name = plan_data.name

            if plan_data.description is not None:
                plan.description = plan_data.description

            if plan_data.price_monthly is not None:
                plan.price_monthly = plan_data.price_monthly

            if plan_data.price_yearly is not None:
                plan.price_yearly = plan_data.price_yearly

            if plan_data.api_rate_limit is not None:
                plan.api_rate_limit = plan_data.api_rate_limit

            if plan_data.daily_request_limit is not None:
                plan.daily_request_limit = plan_data.daily_request_limit

            if plan_data.monthly_request_limit is not None:
                plan.monthly_request_limit = plan_data.monthly_request_limit

            if plan_data.allowed_endpoints is not None:
                plan.allowed_endpoints = plan_data.allowed_endpoints

            if plan_data.max_payload_size is not None:
                plan.max_payload_size = plan_data.max_payload_size

            if plan_data.has_priority_support is not None:
                plan.has_priority_support = plan_data.has_priority_support

            if plan_data.has_advanced_features is not None:
                plan.has_advanced_features = plan_data.has_advanced_features

            if plan_data.concurrent_requests is not None:
                plan.concurrent_requests = plan_data.concurrent_requests

            if plan_data.max_response_time is not None:
                plan.max_response_time = plan_data.max_response_time

            await db.commit()
            await db.refresh(plan)

            return schemas.SubscriptionPlanBase.model_validate(plan)

        except HTTPException as e:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error updating subscription: {str(e)}"
            )



    async def get_subscription_plans(self, db: AsyncSession, skip: int = 0, limit: int = 100) -> List[SubscriptionPlan]:

        try:
            # Use SQLAlchemy's future select() instead of db.query
            result = await db.execute(
                select(models.SubscriptionPlan).offset(skip).limit(limit)
            )
            plans = result.scalars().all()
            if not plans:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Subscription plan not found")

            all_plans = [
                schemas.SubscriptionPlanResponse.model_validate(plan) for plan in plans
            ]

            return all_plans
        except HTTPException as e:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting subscription: {str(e)}"
            )


    @staticmethod
    async def get_subscription_plan(tier: schemas.SubscriptionTier, db: AsyncSession):
        try:
            # Use SQLAlchemy's future select() instead of db.query
            result = await db.execute(
                select(models.SubscriptionPlan).filter(models.SubscriptionPlan.tier == tier)
            )
            plan = result.scalar_one_or_none()
            if not plan:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Subscription plan not found")

            return schemas.SubscriptionPlanResponse.model_validate(plan)
        except HTTPException as e:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting subscription: {str(e)}"
            )


    @classmethod
    async def subscribe_user(cls, tier: schemas.SubscriptionTier, user: user_schema.User, db: AsyncSession) -> schemas.UserSubscription:

        try:
            plan = await cls.get_subscription_plan(tier=tier, db=db)

            # Use SQLAlchemy's future select() instead of db.query
            result = await db.execute(
                select(models.user_subscription).filter(models.user_subscription.user_id == user.id)
            )
            if_subed = result.scalar_one_or_none()

            if if_subed:
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Already Subscribed")

            end_date = datetime.now(timezone.utc) + timedelta(days=30)  # Default to 30 days if plan.duration_days is not available

            db_subscription = models.user_subscription(
                user_id = user.id,
                subscription_plan_id = plan.id,
                start_date=datetime.now(timezone.utc),
                end_date=end_date,
                status='active'
            )
            db.add(db_subscription)
            await db.commit()
            await db.refresh(db_subscription)

            api_keys = schemas.UserSubscription(
                    plan_id=db_subscription.subscription_plan_id,
                    plan_tier=plan.tier,
                    plan_name=plan.name,
                    start_date=db_subscription.start_date,
                    end_date=db_subscription.end_date
                )
            return api_keys
        except HTTPException as e:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error finding user subscription: {str(e)}"
            )



    async def get_user_subscriptions(self, user: user_schema.ShowUser, db: AsyncSession) -> schemas.UserSubscriptionResponse:
        try:
            # Get free plan
            free_plan_result = await db.execute(
                select(models.SubscriptionPlan).filter(models.SubscriptionPlan.tier == SubscriptionTier.BASIC)
            )
            free_plan = free_plan_result.scalar_one_or_none()

            # Get user's non-free plan subscription
            if free_plan:
                plan_result = await db.execute(
                    select(models.user_subscription).filter(
                        models.user_subscription.user_id == user.id,
                        models.user_subscription.subscription_plan_id != free_plan.id
                    )
                    .order_by(models.user_subscription.created_at.desc())
                    .limit(1)
                )
                plan = plan_result.scalar_one_or_none()

                # If no non-free plan, get free plan subscription
                if not plan:
                    free_plan_result = await db.execute(
                        select(models.user_subscription).filter(
                            models.user_subscription.user_id == user.id,
                            models.user_subscription.subscription_plan_id == free_plan.id
                        )
                        .order_by(models.user_subscription.created_at.desc())
                        .limit(1)
                    )
                    plan = free_plan_result.scalar_one_or_none()

            else:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User has no subscription plan")
                # If no free plan defined, just get any subscription
                #plan_result = await db.execute(
                    #select(models.user_subscription).filter(models.user_subscription.user_id == user.id)
                #)
                #plan = plan_result.scalar_one_or_none()

            if not plan:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User has no subscription plan")

            return schemas.UserSubscriptionResponse.model_validate(plan)
        except HTTPException as e:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error finding user subscription: {str(e)}"
            )



    async def cancel_user_subscription(self, user: user_schema.User, db: AsyncSession) -> str:
        # Get free plan
        free_plan_result = await db.execute(
            select(models.SubscriptionPlan).filter(models.SubscriptionPlan.tier == SubscriptionTier.BASIC)
        )
        free_plan = free_plan_result.scalar_one_or_none()

        if not free_plan:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Basic subscription plan not found")

        # Get user's non-free subscription
        subscription_result = await db.execute(
            select(models.user_subscription).filter(
                models.user_subscription.user_id == user.id,
                models.user_subscription.subscription_plan_id != free_plan.id
            )
        )
        subscription = subscription_result.scalar_one_or_none()

        try:
            if not subscription:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User has no subscription plan")

            # Delete the API key associated with the subscription
            api_key_result = await db.execute(
                select(APIKey).filter(
                    APIKey.subscription_plan_id == subscription.id,
                    APIKey.user_id == user.id
                )
            )
            api_key = api_key_result.scalar_one_or_none()

            if api_key:
                await db.delete(api_key)

            await db.delete(subscription)
            await db.commit()

            return {"message": "Unsubscribed successfully"}
        except HTTPException as e:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error cancelling subscription: {str(e)}"
            )






    async def delete_subscription_plan(
        self,
        db: AsyncSession,
        plan_id: int
    ) -> bool:
        # Get the plan
        plan_result = await db.execute(
            select(SubscriptionPlan).filter(SubscriptionPlan.id == plan_id)
        )
        plan = plan_result.scalar_one_or_none()

        if not plan:
            raise ValueError("Subscription plan not found")

        # Count plans with the same tier
        tier_plans_result = await db.execute(
            select(SubscriptionPlan).filter(SubscriptionPlan.tier == plan.tier)
        )
        tier_plans = len(tier_plans_result.scalars().all())

        if tier_plans <= 1:
            raise ValueError(f"Cannot delete the last plan for {plan.tier} tier")

        await db.delete(plan)
        await db.commit()
        return True


