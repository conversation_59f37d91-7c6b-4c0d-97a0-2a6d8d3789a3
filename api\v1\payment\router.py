from sqlalchemy.ext.asyncio import AsyncSession
from api.v1.user import schemas as user_schema
from api.db.database import get_db
from api.core.dependencies import is_authenticated
from . import schemas
from api.v1.subscription import schemas as subscription_schema
from fastapi import Depends, HTTPException, APIRouter, Request
from .services import PaymentService, handle_paystack_webhook, handle_stripe_webhook, handle_monnify_webhook
from .exceptions import handle_payment_notification


app = APIRouter(tags=["Payment"])

paymentService = PaymentService()


@app.post("/subscriptions/initiate-payment", response_model=schemas.PaymentResponse)
async def initiate_subscription_payment(
    tier: subscription_schema.SubscriptionTier,
    provider: schemas.PaymentProvider,
    duration: schemas.Duration,
    db: AsyncSession = Depends(get_db),
    user: user_schema.User = Depends(is_authenticated)
):

    try:

        payment_data = schemas.PaymentCreate(
            provider=provider,
            subscription_tier=tier
        )

        payment_response = await paymentService.initiate_payment(
            payment_data=payment_data,
            duration=duration,
            user=user,
            db=db
        )

        #handle_payment_notification(user_id=user.id, action="initiate_payment")

        return payment_response

    except Exception as e:
        raise HTTPException(status_code=500, detail={"status": "error", "message": str(e)})



@app.get("/subscriptions/verify-payment/{reference}")
async def verify_payment_and_subscribe(
    reference: str,
    db: AsyncSession = Depends(get_db)
):

    try:
        user_id = reference.split('-')[-1]
        subscription = await paymentService.verify_payment_and_subscribe(
            reference=reference,
            db=db
        )
    except Exception as e:
        handle_payment_notification(user_id=user_id, action="payment_failed")
        return {"message": f"An error occurred while verifying payment - {e}."}

    handle_payment_notification(user_id=user_id, action="verify_payment")
    return {"message": "Subscribed successfully.", "detail": subscription}




@app.post("/webhooks/paystack")
async def paystack_webhook(request: Request, db: AsyncSession = Depends(get_db)):
    response = await handle_paystack_webhook(request, db)

    # Add notification for Paystack webhook
    # await handle_auth_notification(db=db, user_id=user_id, action="paystack_webhook")

    return response

@app.post("/webhooks/stripe")
async def stripe_webhook(request: Request, db: AsyncSession = Depends(get_db)):
    response = await handle_stripe_webhook(request, db)

    # Add notification for Stripe webhook
    # await handle_auth_notification(db=db, user_id=user_id, action="stripe_webhook")

    return response

@app.post("/webhooks/monnify")
async def monnify_webhook(request: Request, db: AsyncSession = Depends(get_db)):
    response = await handle_monnify_webhook(request, db)

    # Add notification for Monnify webhook
    # await handle_auth_notification(db=db, user_id=user_id, action="monnify_webhook")

    return response
