from fastapi import HTT<PERSON>Exception, status, UploadFile, File
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException, status
from datetime import datetime, timezone
from config import config
from api.core.general_dep import upload_image
from api.v1.user.models import User as UserModel, <PERSON>ter as UserAvater, NotificationPreference as UserNotification
from api.v1.user import schemas as user_schema
from api.v1.auth.services import Auth
from api.v1.auth.services_email import EmailVerification
from datetime import datetime, timedelta
from api.core import responses
import os
from .exceptions import (
    UserAlreadyExistsError,
    DatabaseError,
    logger,
    FileUploadError,
    FileStorageError,
    UpdateError,
    NotificationError
)
import json
from typing import Optional, Any, Dict
from redis.asyncio import Redis
from sqlalchemy.future import select
import asyncio
from pathlib import Path
import aiofiles

AVATER_FOLDER = "./uploads/avatars"
BASE_URL = config.BASE_URL
#CACHE_TIME = 600
CACHE_TIME = 1200
VERIFICATION_TOKEN_LENGTH = 32
VERIFICATION_TOKEN_EXPIRY_HOURS = 24
PASSWORD_RESET_TOKEN_EXPIRY_HOURS = 1

class UserService():

    def __init__(self, redis: Optional[Redis] = None):
        self.redis = redis
        self.cache_time = CACHE_TIME


    async def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache with error handling"""
        if not self.redis:
            return None
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Cache data corruption detected: {str(e)}")
            await self.redis.delete(cache_key)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval failed: {str(e)}")
            return None

    async def _cache_data(self, cache_key: str, data: Any, cache_time: int) -> None:
        """Cache data with error handling"""
        if not self.redis:
            return None
        try:
            await self.redis.set(cache_key, json.dumps(data))
            await self.redis.expire(cache_key, cache_time)
        except Exception as e:
            logger.warning(f"Failed to cache data: {str(e)}")


    async def _validate_email(self, email: str, db: AsyncSession):
        result = await db.execute(select(UserModel).filter(UserModel.email == email))
        user_email = result.scalar_one_or_none()
        return bool(user_email)


    async def create(self, user: user_schema.CreateUser, db: AsyncSession):
        try:
            email_exists = await self._validate_email(user.email, db)
            if email_exists:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=responses.EMAIL_IN_USE
                )

            email = EmailVerification()
            verification_code = await asyncio.to_thread(email.generate_verification_code) #email.generate_verification_code()
            expiry = datetime.now(timezone.utc) + timedelta(hours=VERIFICATION_TOKEN_EXPIRY_HOURS)
            
            
            #current_time = datetime.now()
            #expiry = current_time + timedelta(hours=VERIFICATION_TOKEN_EXPIRY_HOURS)
            hashed_password = await asyncio.to_thread(Auth.hash_password, user.password)

            created_user = UserModel(
                first_name=user.first_name,
                last_name=user.last_name,
                email=user.email,
                verification_token=verification_code,
                verification_token_expiry=expiry,
                password=hashed_password
                )
            db.add(created_user)

            # Send the verification email:
            try:
                #email.send_verification_email(created_user.email, verification_code)
                await asyncio.to_thread(
                    email.send_verification_email,
                    created_user.email,
                    verification_code
                )
                logger.info(f"Verification email queued for {user.email}")
            except HTTPException as email_error:
                
                logger.error(f"Email sending failed: {str(email_error)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to send verification email"
                )

            await db.commit()
            await db.refresh(created_user)
            return {"message": "Email verification code sent!"}

        except HTTPException as e:
            
            raise
        except SQLAlchemyError as e:
            
            logger.error(f"Database error creating user: {str(e)}")
            raise DatabaseError(f"Failed to create user: {str(e)}")
        except Exception as e:
            
            logger.error(f"Unexpected error creating user: {str(e)}")
            raise DatabaseError(f"Failed to create user: {str(e)}")



    async def email_verification(self, user: user_schema.CreateUser, db: AsyncSession):
        try:

            if user.is_active:
                return HTTPException(status_code=400, detail="User already verified")

            email = EmailVerification()
            verification_code = await asyncio.to_thread(email.generate_verification_code) #email.generate_verification_code()
            expiry = datetime.now(timezone.utc) + timedelta(hours=VERIFICATION_TOKEN_EXPIRY_HOURS)

            user.verification_token = verification_code
            user.verification_token_expiry = expiry

            # Send the verification email:
            try:
                #email.send_verification_email(user.email, verification_code)
                await asyncio.to_thread(
                    email.send_verification_email,
                    user.email,
                    verification_code
                )
                logger.info(f"Verification email re-queued for {user.email}")

            except HTTPException as email_error:
                
                logger.error(f"Email sending failed: {str(email_error)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to send verification email"
                )

            await db.commit()
            await db.refresh(user)
            return {"message": "Email verification code sent!"}

        except HTTPException as e:
            
            raise
        except SQLAlchemyError as e:
            
            logger.error(f"Database error creating user: {str(e)}")
            raise DatabaseError(f"Failed to create user: {str(e)}")
        except Exception as e:
            
            logger.error(f"Unexpected error creating user: {str(e)}")
            raise DatabaseError(f"Failed to create user: {str(e)}")




    async def verify_email(self, email: str, verification_code: str, db: AsyncSession):
        """Verify user's email using the 6-digit verification code"""
        try:
            result = await db.execute(select(UserModel).filter(UserModel.email == email))
            user = result.scalar_one_or_none()
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            if user.is_active:
                return user  # User already verified

            if not user.verification_token:
                raise HTTPException(status_code=400, detail="Verification code not found")

            if user.verification_token != verification_code:
                raise HTTPException(status_code=400, detail="Invalid verification code")

            if not user.verification_token_expiry:
                raise HTTPException(status_code=400, detail="Verification code has expired")

            if user.verification_token_expiry < datetime.now(timezone.utc):
                raise HTTPException(status_code=400, detail="Verification code has expired")

            user.is_active = True
            user.verification_token = None  # Clear the code
            user.verification_token_expiry = None  # Clear expiry
            await db.commit()
            await db.refresh(user)
            return user

        except HTTPException:
            raise
        except SQLAlchemyError as e:
            
            logger.error(f"Database error verifying user email: {str(e)}")
            raise DatabaseError(f"Failed to verify user email: {str(e)}")
        except Exception as e:
            
            logger.error(f"Unexpected error verifying email: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to verify email"
            )


    async def initiate_password_reset(self, email: str, db: AsyncSession) -> dict:
        """Initiates the password reset process for a given email."""
        try:
            result = await db.execute(select(UserModel).filter(
                UserModel.email == email,
                UserModel.is_deleted == False
            ))
            user = result.scalar_one_or_none()

            if not user:
                raise HTTPException(status_code=404, detail="Email does not exist in our system")

            email_service = EmailVerification()
            reset_code = await asyncio.to_thread(email_service.generate_verification_code) #email_service.generate_verification_code()
            expiry = datetime.now(timezone.utc) + timedelta(hours=PASSWORD_RESET_TOKEN_EXPIRY_HOURS)

            user.reset_token = reset_code
            user.reset_token_expiry = expiry
            user.last_updated = datetime.now(timezone.utc)

            # Send reset email
            try:
                #email_service.send_password_reset_email(user.email, reset_code)
                await asyncio.to_thread(
                    email_service.send_password_reset_email,
                    user.email,
                    reset_code
                )
                logger.info(f"Password reset email queued for {email}")

            except Exception as e:
                logger.error(f"Failed to send password reset email: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to send password reset email"
                )

            await db.commit()
            return {"detail": "Password reset code sent. Please check your email."}

        except HTTPException as e:
            
            raise
        except SQLAlchemyError as e:
            
            logger.error(f"Database error in password reset: {str(e)}")
            raise DatabaseError(f"Password reset failed: {str(e)}")
        except Exception as e:
            
            logger.error(f"Unexpected error in password reset: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Password reset failed"
            )

    async def reset_password(self, email: str, code: str, new_password: str, db: AsyncSession) -> dict:
        """Resets the user's password using the reset code."""
        try:
            result = await db.execute(select(UserModel).filter(
                UserModel.email == email,
                UserModel.is_deleted == False
            ))
            user = result.scalar_one_or_none()

            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            if not user.reset_token or user.reset_token != code:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid reset code"
                )

            if user.reset_token_expiry < datetime.now(timezone.utc):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Reset code has expired"
                )

            # Update password

            hashed_password = await asyncio.to_thread(Auth.hash_password, new_password)
            user.password = hashed_password
            #user.password = Auth.hash_password(new_password)
            user.reset_token = None
            user.reset_token_expiry = None
            user.last_updated = datetime.now(timezone.utc)

            await db.commit()
            return user.id

        except HTTPException:
            raise
        except SQLAlchemyError as e:
            
            logger.error(f"Database error in password reset: {str(e)}")
            raise DatabaseError(f"Password reset failed: {str(e)}")
        except Exception as e:
            
            logger.error(f"Unexpected error in password reset: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Password reset failed"
            )




    async def upload_avater(self, user: user_schema.User, db: AsyncSession, file: UploadFile = File(...)):
        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=responses.FILE_REQUIRED)
        try:
            if file:
                unique_filename, full_file_path = await upload_image(file, AVATER_FOLDER)
            else:
                pass
            try:
                result = await db.execute(select(UserAvater).filter(UserAvater.user_id == user.id))
                avatar = result.scalar_one_or_none()

                if avatar:
                    #old_file_path = os.path.join(AVATER_FOLDER, avatar.avatar_url)
                    if avatar.avatar_url:
                        old_file_path = Path(AVATER_FOLDER) / avatar.avatar_url
                    else:
                        old_file_path = None

                    avatar.avatar_url = unique_filename
                    avatar.last_updated = datetime.now(timezone.utc)
                else:
                    new_avatar = UserAvater(
                        user_id=user.id,
                        avatar_url=unique_filename,
                        date_created=datetime.now(timezone.utc),
                        last_updated=datetime.now(timezone.utc)
                    )

                    db.add(new_avatar)
                    await db.commit()
                    await db.refresh(new_avatar)


                if old_file_path:
                    try:
                        if await asyncio.to_thread(old_file_path.exists):
                            await asyncio.to_thread(old_file_path.unlink)
                    except Exception as del_err:
                        logger.warning(f"Could not delete old avatar file {old_file_path}: {del_err}")

                return {"avatar_url": f"{BASE_URL}/avatars/{unique_filename}"}

            except OSError as e:
                logger.error(f"File operation error: {str(e)}")
                raise FileStorageError(f"Failed to store avatar file: {str(e)}")
            except SQLAlchemyError as e:
                
                if os.path.exists(full_file_path):
                    try:
                        os.remove(full_file_path)
                    except:
                        pass
                logger.error(f"Database error while uploading avatar: {str(e)}")
                raise DatabaseError(f"Failed to save avatar information: {str(e)}")

        except OSError as e:
            raise
        except SQLAlchemyError as e:
            raise
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error uploading avatar: {str(e)}")
            raise FileUploadError(f"Failed to upload avatar: {str(e)}")


    async def get_avater(self, user: user_schema.User, db: AsyncSession) -> Dict[str, str]:
        cache_key = f"user_avater:{user.id}"
        try:
            user_avater = await self._get_cached_data(cache_key)
            if user_avater:
                return user_avater

            result = await db.execute(select(UserAvater).filter(UserAvater.user_id == user.id))
            avater = result.scalar_one_or_none()
            if not avater or not avater.avatar_url:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=responses.NOT_AVATAR_FOUND)

            #file_path = os.path.join(AVATER_FOLDER, avater.avatar_url)
            file_path = Path(AVATER_FOLDER) / avater.avatar_url
            if not await asyncio.to_thread(file_path.exists):
                logger.warning(f"Avatar file not found on disk: {file_path}")

            avatar_url_response = f"{BASE_URL}/avatars/{avater.avatar_url}"
            response_data = {"avatar_url": avatar_url_response}

            await self._cache_data(
                cache_key,
                response_data,
                self.cache_time
            )

            return response_data

        except HTTPException:
            raise
        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving avatar: {str(e)}")
            raise DatabaseError(f"Failed to retrieve avatar: {str(e)}")
        except Exception as e:
            logger.error(f"Error retrieving avatar: {str(e)}")
            raise DatabaseError(f"Failed to retrieve avatar: {str(e)}")


    async def delete_avater(self, user: user_schema.User, db: AsyncSession):
        try:
            result = await db.execute(select(UserAvater).filter(UserAvater.user_id==user.id))
            avater = result.scalar_one_or_none()

            if not avater or not avater.avatar_url:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=responses.NOT_AVATAR_FOUND)

            #file_path = os.path.join(AVATER_FOLDER, avater.avatar_url)
            file_path = Path(AVATER_FOLDER) / avater.avatar_url

            if await asyncio.to_thread(file_path.exists):
                try:
                    await asyncio.to_thread(file_path.unlink)
                except OSError as e:
                    logger.warning(f"Could not delete avatar file: {str(e)}")
            else:
                logger.warning(f"Avatar file not found for deletion: {file_path}")

            try:
                await db.delete(avater)
                await db.commit()
            except SQLAlchemyError as e:
                
                logger.error(f"Database error while deleting avatar: {str(e)}")
                raise DatabaseError(f"Failed to delete avatar record: {str(e)}")

            return {"detail": responses.DELETED_SUCCESSFULLY}

        except OSError as e:
            raise
        except SQLAlchemyError as e:
            raise
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Unexpected error in delete_avatar: {str(e)}")
            raise DatabaseError(f"Failed to delete avatar: {str(e)}")


    """
    async def update_avater(self, user: user_schema.User, db: AsyncSession, file: UploadFile = File(...)):
        ""
        NOT IN USE!
        ""

        if not file:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=responses.FILE_REQUIRED)

        result = await db.execute(select(UserAvater).filter(UserAvater.user_id==user.id))
        avater = result.scalar_one_or_none()
        if not avater:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=responses.NOT_FOUND)

        try:
            #with open(os.path.join(AVATER_FOLDER, file.filename), "wb") as buffer:
                #buffer.write(file.file.read())

            destination_path = Path(AVATER_FOLDER) /file.filename
            async with aiofiles.open(destination_path, mode="wb") as buffer:
                await buffer.write(await file.read())

            avater.avatar_url = file.filename

        except Exception as e:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

        await db.commit()
        await db.refresh(avater)

        return FileResponse(os.path.join(AVATER_FOLDER, avater.avatar_url))
    """


    @staticmethod
    async def fetch(db: AsyncSession, id: int = None) -> user_schema.ShowUser:
        if id is None:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=responses.ID_REQUIRED)
        try:
            result = await db.execute(select(UserModel).filter(UserModel.id==id).filter(UserModel.is_deleted==False))
            user = result.scalar_one_or_none()
            if user is None:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=responses.NOT_FOUND)

            return user
        except HTTPException:
            raise
        except SQLAlchemyError as e:
            logger.error(f"Database error fetching user: {str(e)}")
            raise DatabaseError(f"Failed to fetch user: {str(e)}")

    @staticmethod
    def fetch_all():
        """
        NOT IN USE!
        """
        pass


    @staticmethod
    async def fetch_by_email(email: str, db: AsyncSession) -> user_schema.User:
        """
        NOT IN USE!
        """
        result = await db.execute(select(UserModel).filter(UserModel.email == email, UserModel.is_deleted==False))
        user = result.scalar_one_or_none()
        return user_schema.ShowUser.model_validate(user)


    async def get_user(cls, user_id:int, db: AsyncSession) -> user_schema.User:
        cache_key = f"user_data:{user_id}"
        try:
            user_data = await cls._get_cached_data(cache_key)
            if user_data:
                return user_data

            user_obj = await cls.fetch(id=user_id, db=db)
            show_user_data = user_schema.ShowUser.model_validate(user_obj).model_dump()
            show_user_data["date_created"] = user_obj.date_created.isoformat()
            show_user_data["last_updated"] = user_obj.last_updated.isoformat()

            await cls._cache_data(
                cache_key,
                show_user_data,
                cls.cache_time
            )

            return show_user_data
        except HTTPException:
            raise
        except SQLAlchemyError as e:
            raise
        except Exception as e:
            logger.error(f"Error getting user: {str(e)}")
            raise DatabaseError(f"Failed to get user: {str(e)}")

    @classmethod
    async def update(cls, id: int, user: user_schema.UpdateUser, db: AsyncSession):
        try:

            user_obj = await cls.fetch(id=id, db=db)

            if user and user.email and user.email != user_obj.email:
                result = await db.execute(select(UserModel).filter(
                    UserModel.email == user.email,
                    UserModel.id != id,
                    UserModel.is_deleted == False
                ))
                existing_user = result.scalar_one_or_none()
                if existing_user:
                    raise UserAlreadyExistsError(f"User with email {user.email} already exists")

            if user.first_name:
                user_obj.first_name = user.first_name
            if user.last_name:
                user_obj.last_name = user.last_name
            if user.email:
                user_obj.email = user.email

            user_obj.last_updated = datetime.now(timezone.utc)

            await db.commit()
            await db.refresh(user_obj)
            return user_schema.ShowUser.model_validate(user_obj)

        except HTTPException:
            raise
        except UserAlreadyExistsError:
            raise
        except SQLAlchemyError as e:
            logger.error(f"Database error updating user: {str(e)}")
            raise DatabaseError(f"Failed to update user: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error updating user: {str(e)}")
            raise UpdateError(f"Failed to update user: {str(e)}")


    @classmethod
    async def delete(cls, db: AsyncSession, id: int=None,) -> user_schema.User:
        try:
            user = await cls.fetch(id=id, db=db)
            if user.is_deleted:
                logger.warning(f"User {id} already marked as deleted.")
                return {"detail": responses.DELETED_SUCCESSFULLY}

            user.is_deleted = True
            user.last_updated = datetime.now(timezone.utc)
            await db.commit()
            logger.info(f"User {id} marked as deleted in session, pending commit.")
            return user
        except HTTPException:
            
            raise
        except SQLAlchemyError as e:
            
            logger.error(f"Database error deleting user: {str(e)}")
            raise DatabaseError(f"Failed to delete user: {str(e)}")
        except Exception as e:
            
            logger.error(f"Unexpected error deleting user: {str(e)}")
            raise DatabaseError(f"Failed to delete user: {str(e)}")



    async def notification(self, user: user_schema.User, db: AsyncSession, notification_settings: user_schema.NotificationSettings):
        try:
            stmt = select(UserNotification.id).filter_by(user_id=user.id)
            result = await db.execute(stmt)
            existing = result.scalar_one_or_none()
            if existing:
                logger.warning(f"Notification settings already exist for user {user.id}. Use update instead.")
                raise 

            notification = UserNotification(
                user_id=user.id,
                email_notification=notification_settings.email_notification,
                push_notification=notification_settings.push_notification,
                sms_notification=notification_settings.sms_notification,
                date_created=datetime.now(timezone.utc),
                last_updated=datetime.now(timezone.utc)
            )

            db.add(notification)
            await db.commit()
            await db.refresh(notification)
            return user_schema.NotificationSettings.model_validate(notification)

        except HTTPException:
            
            raise
        except SQLAlchemyError as e:
            
            logger.error(f"Database error creating notification settings: {str(e)}")
            raise DatabaseError(f"Failed to create notification settings: {str(e)}")
        except Exception as e:
            
            logger.error(f"Unexpected error creating notification settings: {str(e)}")
            raise NotificationError(f"Failed to create notification settings: {str(e)}")


    async def update_notification(self, user: user_schema.User, db: AsyncSession, notification_settings: user_schema.NotificationSettingsUpdate):
        try:
            # First check if notification settings exist
            result = await db.execute(select(UserNotification).filter(UserNotification.user_id==user.id))
            existing_notification = result.scalar_one_or_none()

            if existing_notification:
                # Update existing notification
                if notification_settings.email_notification is not None:
                    existing_notification.email_notification = notification_settings.email_notification
                if notification_settings.push_notification is not None:
                    existing_notification.push_notification = notification_settings.push_notification
                if notification_settings.sms_notification is not None:
                    existing_notification.sms_notification = notification_settings.sms_notification

                existing_notification.last_updated = datetime.now(timezone.utc)
                notification = existing_notification
            else:
                # Create new notification
                notification = UserNotification(
                    user_id=user.id,
                    date_created=datetime.now(timezone.utc),
                    last_updated=datetime.now(timezone.utc)
                )

                if notification_settings.email_notification is not None:
                    notification.email_notification = notification_settings.email_notification
                if notification_settings.push_notification is not None:
                    notification.push_notification = notification_settings.push_notification
                if notification_settings.sms_notification is not None:
                    notification.sms_notification = notification_settings.sms_notification

                db.add(notification)

            await db.commit()
            await db.refresh(notification)

            return user_schema.NotificationSettings.model_validate(notification)

        except HTTPException:
            
            raise
        except SQLAlchemyError as e:
            
            logger.error(f"Database error updating notification settings: {str(e)}")
            raise DatabaseError(f"Failed to update notification settings: {str(e)}")
        except Exception as e:
            
            logger.error(f"Unexpected error updating notification settings: {str(e)}")
            raise NotificationError(f"Failed to update notification settings: {str(e)}")
        #return notification






"""


    def create(self, user: user_schema.CreateUser, db: Session):
        try:
            email = EmailVerification()
            token = email.generate_verification_token()
            expiry = datetime.now() + timedelta(hours=VERIFICATION_TOKEN_EXPIRY_HOURS)

            created_user = UserModel(
                first_name=user.first_name,
                last_name=user.last_name,
                email=user.email,
                verification_token = token,
                verification_token_expiry = expiry,
                password=Auth.hash_password(user.password))
            db.add(created_user)

            verification_data = {"email": created_user.email, "token": token}
            encoded_data = email.encode_verification_data(verification_data)
            verification_link = urljoin(BASE_URL, f"/auth/verify/confirm?data={encoded_data}")

            # Send the verification email:
            try:
                email.send_verification_email(created_user.email, verification_link)
            except HTTPException as email_error:
                db.rollback()
                logger.error(f"Email sending failed: {str(email_error)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to send verification email"
                )

            db.commit()
            db.refresh(created_user)
            return {"message": "Email verification sent!"}
            #return user_schema.ShowUser.model_validate(created_user)

        except HTTPException as e:
            db.rollback()
            raise
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Database error creating user: {str(e)}")
            raise DatabaseError(f"Failed to create user: {str(e)}")
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error creating user: {str(e)}")
            raise DatabaseError(f"Failed to create user: {str(e)}")



    def email_verification(self, user: user_schema.CreateUser, db: Session):
        try:

            if user.is_active:
                return HTTPException(status_code=400, detail="User already verified")

            email = EmailVerification()
            token = email.generate_verification_token()
            expiry = datetime.now() + timedelta(hours=VERIFICATION_TOKEN_EXPIRY_HOURS)

            user.verification_token = token,
            user.verification_token_expiry = expiry,

            verification_data = {"email": user.email, "token": token}
            encoded_data = email.encode_verification_data(verification_data)
            verification_link = urljoin(BASE_URL, f"/auth/verify/confirm?data={encoded_data}")

            # Send the verification email:
            try:
                email.send_verification_email(user.email, verification_link)
            except HTTPException as email_error:
                db.rollback()
                logger.error(f"Email sending failed: {str(email_error)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to send verification email"
                )

            db.commit()
            db.refresh(user)
            return {"message": "Email verification sent!"}

        except HTTPException as e:
            db.rollback()
            raise
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Database error creating user: {str(e)}")
            raise DatabaseError(f"Failed to create user: {str(e)}")
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error creating user: {str(e)}")
            raise DatabaseError(f"Failed to create user: {str(e)}")




    def verify_email(cls, data:str, db: Session):
        try:
            verify = EmailVerification()
            verification_data = verify.decode_verification_data(data)
            email = verification_data.get('email')
            token = verification_data.get('token')

            if not email or not token:
                raise HTTPException(status_code=400, detail="Invalid verification link")

            user = select(UserModel).filter(UserModel.email == email).first()
            if not user:
                raise HTTPException(status_code=404, detail="User not found")

            if user.is_active:
                return user # Redirect to a success page

            if not user.verification_token:
                raise HTTPException(status_code=400, detail="Verification token not found")

            if user.verification_token != token:
                raise HTTPException(status_code=400, detail="Invalid verification token")

            if not user.verification_token_expiry:
                raise HTTPException(status_code=400, detail="Verification token has expired")

            if user.verification_token_expiry < datetime.now():
                raise HTTPException(status_code=400, detail="Verification token expired")

            user.is_active = True
            user.verification_token = None  # Clear the token
            user.verification_token_expiry = None  # Clear expiry
            db.commit()
            db.refresh(user)
            return user

        except HTTPException:
            raise
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Database error verifying user email: {str(e)}")
            raise DatabaseError(f"Failed to verify user email: {str(e)}")
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error verifying email: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to verify email"
            )


    def initiate_password_reset(self, email: str, db: Session) -> dict:
        ""Initiates the password reset process for a given email.""
        try:
            user = select(UserModel).filter(
                UserModel.email == email,
                UserModel.is_deleted == False
            ).first()

            if not user:
                raise HTTPException(status_code=404, detail="Email does not exist in our system")

            email_service = EmailVerification()
            reset_token = email_service.generate_verification_token()
            expiry = datetime.now() + timedelta(hours=PASSWORD_RESET_TOKEN_EXPIRY_HOURS)  # 1 hour expiry for reset token

            user.reset_token = reset_token
            user.reset_token_expiry = expiry
            user.last_updated = datetime.now()

            reset_data = {"email": user.email, "token": reset_token}
            encoded_data = email_service.encode_verification_data(reset_data)
            reset_link = urljoin(BASE_URL, f"/auth/reset-password?data={encoded_data}")

            # Send reset email
            try:
                email_service.send_password_reset_email(user.email, reset_link)
            except Exception as e:
                logger.error(f"Failed to send password reset email: {str(e)}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to send password reset email"
                )

            db.commit()
            return {"detail": "We have successfully sent password reset email. Please check your email."}

        except HTTPException as e:
            db.rollback()
            raise
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Database error in password reset: {str(e)}")
            raise DatabaseError(f"Password reset failed: {str(e)}")
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error in password reset: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Password reset failed"
            )

    def reset_password(self, data: str, new_password: str, db: Session) -> dict:
        ""Resets the user's password using the reset token.""
        try:
            verify = EmailVerification()
            reset_data = verify.decode_verification_data(data)
            email = reset_data.get('email')
            token = reset_data.get('token')

            if not email or not token:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid reset link"
                )

            user = select(UserModel).filter(
                UserModel.email == email,
                UserModel.is_deleted == False
            ).first()

            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            if not user.reset_token or user.reset_token != token:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid reset token"
                )

            if user.reset_token_expiry < datetime.now():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Reset token has expired"
                )

            # Update password
            user.password = Auth.hash_password(new_password)
            user.reset_token = None
            user.reset_token_expiry = None
            user.last_updated = datetime.now()

            db.commit()
            return {"detail": "Password reset successful"}

        except HTTPException:
            raise
        except SQLAlchemyError as e:
            db.rollback()
            logger.error(f"Database error in password reset: {str(e)}")
            raise DatabaseError(f"Password reset failed: {str(e)}")
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error in password reset: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Password reset failed"
            )


"""
