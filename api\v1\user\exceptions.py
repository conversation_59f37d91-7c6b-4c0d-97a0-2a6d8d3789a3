import logging
from api.core.logging_config import get_logger
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from sqlalchemy.orm import Session
from api.v1.websockets.models import Notification, NotificationType, NotificationPriority
from api.v1.websockets.services import NotificationService
from api.v1.websockets.schemas import Notification<PERSON>reate
from datetime import datetime

logger = get_logger(__name__)


def handle_user_notification(user_id: int, action: str):
    """Handle user-related notifications"""
    messages = {
        "upload_avatar": "Profile picture uploaded successfully.",
        "get_avatar": "Profile picture retrieved.",
        "get_user": "User information retrieved.",
        "delete_user": "Account deleted successfully.",
        "delete_avatar": "Profile picture deleted successfully.",
        "update_user": "Profile information updated successfully.",
        "update_avatar": "Profile picture updated successfully.",
        "update_notification_settings": "Notification settings updated successfully.",
        "create_api_key": "New API key created successfully.",
        "create_api_key_failed": "Failed to create API key.",
        "list_api_keys": "API keys retrieved successfully.",
        "regenerate_api_key": "API key regenerated successfully.",
        "delete_api_key": "API key deleted successfully."
    }
    
    titles = {
        "upload_avatar": "Profile Picture Uploaded",
        "get_avatar": "Profile Picture Retrieved",
        "get_user": "User Information",
        "delete_user": "Account Deleted",
        "delete_avatar": "Profile Picture Deleted",
        "update_user": "Profile Updated",
        "update_avatar": "Profile Picture Updated",
        "update_notification_settings": "Notification Settings Updated",
        "create_api_key": "API Key Created",
        "create_api_key_failed": "Failed to create API key",
        "list_api_keys": "API Keys Retrieved",
        "regenerate_api_key": "API Key Regenerated",
        "delete_api_key": "API Key Deleted"
    }
    
    if action in messages:
        try:
            message = messages[action]
            title = titles.get(action, "User Notification")
            
            # Set priority based on action criticality
            priority = NotificationPriority.HIGH if action == "delete_user" else NotificationPriority.NORMAL
            
            # Create notification with enhanced structure
            notification = NotificationCreate(
                title=title,
                message=message,
                type=NotificationType.USER,
                priority=priority,
                metadata={
                    "timestamp": datetime.now().isoformat(),
                    "action": action,
                    "user_id": user_id,
                    "transaction_details": {
                        "action_type": action,
                        "user_id": user_id,
                        "status": "success"
                    }
                },
                action_url=None
            )
            
            notification_service = NotificationService()
            notification_service.publish_notification(user_id=user_id, notification=notification)
        except Exception as e:
            logger.error(f"Failed to handle user notification: {str(e)}")






class UserError(Exception):
    """Base exception for user-related errors"""
    pass

class UserNotFoundError(UserError):
    """Exception raised when user is not found"""
    pass

class AvatarNotFoundError(UserError):
    """Exception raised when avatar is not found"""
    pass

class FileUploadError(UserError):
    """Exception raised when file upload fails"""
    pass

class InvalidFileError(UserError):
    """Exception raised when file type or format is invalid"""
    pass

class DatabaseError(UserError):
    """Exception raised for database-related errors"""
    pass

class UserAlreadyExistsError(UserError):
    """Exception raised when trying to create a duplicate user"""
    pass

class NotificationError(UserError):
    """Exception raised for notification-related errors"""
    pass

class InvalidParameterError(UserError):
    """Exception raised for invalid parameters"""
    pass

class UpdateError(UserError):
    """Exception raised for update operation failures"""
    pass

class FileRequiredError(UserError):
    """Exception raised when a required file is missing"""
    pass

class FileStorageError(UserError):
    """Exception raised when there's an error storing a file"""
    pass

class AuthorizationError(UserError):
    """Exception raised for authorization issues"""
    pass

class CacheError(UserError):
    """Exception raised for caching-related errors"""
    pass


def handle_user_error(error: Exception) -> None:
    """Convert user errors to appropriate HTTP exceptions"""
    
    if isinstance(error, HTTPException):
        raise error
        
    elif isinstance(error, UserNotFoundError):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User not found: {str(error)}"
        )
        
    elif isinstance(error, AvatarNotFoundError):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Avatar not found: {str(error)}"
        )
        
    elif isinstance(error, FileUploadError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File upload failed: {str(error)}"
        )
        
    elif isinstance(error, InvalidFileError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid file: {str(error)}"
        )
        
    elif isinstance(error, DatabaseError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(error)}"
        )
        
    elif isinstance(error, UserAlreadyExistsError):
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"User already exists: {str(error)}"
        )
        

    elif isinstance(error, NotificationError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Notification error: {str(error)}"
        )
        
    elif isinstance(error, InvalidParameterError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid parameter: {str(error)}"
        )
        
    elif isinstance(error, UpdateError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Update operation failed: {str(error)}"
        )
        
    elif isinstance(error, FileRequiredError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File is required: {str(error)}"
        )
        
    elif isinstance(error, FileStorageError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error storing file: {str(error)}"
        )
        
    elif isinstance(error, AuthorizationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Authorization error: {str(error)}"
        )
        

    elif isinstance(error, CacheError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service temporarily unavailable"
        )

    else:
        logger.error(f"Unhandled error: {str(error)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(error)}"
        )