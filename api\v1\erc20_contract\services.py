from typing import Optional, Any, Dict
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import json
from config import config
from api.v1.deploy_contracts.models import ToBeSigned, make_json_serializable, TransactionType
from api.core.blockchain_dep import class_exponential_backoff
import time
import json
from datetime import datetime, timedelta, timezone
#from .models import ToBeSigned, make_json_serializable
from .exceptions import (
    handle_erc20_error, TokenTransferError, InsufficientTokenBalanceError,
    TokenApprovalError, TokenMintError, AllowanceError, GasEstimationError,
    TokenDataFetchError, ContractInteractionError,
    logger
)
from api.v1.deploy_contracts.services_sign import TokenBase
from api.v1.deploy_contracts.exceptions import CacheError
from decimal import Decimal
from config import config
from web3 import AsyncWeb3
from redis.asyncio import Redis
from api.v1.websockets.models import NotificationStatus, NotificationType, NotificationPriority

PLATFORM_FEE_PERCENTAGE = config.PLATFORM_FEE_PERCENTAGE
GAS_BUFFER_PERCENT = 15



class ERC20Base(TokenBase):
    """Base class for token interactions"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 web3: AsyncWeb3, redis: Optional[Redis] = None):
        try:
            #super().__init__(db, user_id, contract_id, TransactionType.ER20, web3, redis)
            super().__init__(db, user_id, contract_id, web3, redis)

        except Exception as e:
            logger.error(f"Error initializing ERC20 base: {e}")
            raise


class TokenViewOperations(ERC20Base):
    """Handles read-only token operations"""

    async def get_name(self) -> str:
        """Get the name of the ERC20 token"""
        try:
            cache_key = f"erc20_name:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.name().call()

            await self._cache_data(cache_key, response_data, self.longest_cache_time)
            return response_data

        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Error getting token name: {e}")
            raise TokenDataFetchError(f"Failed to get token name: {str(e)}")


    async def get_symbol(self) -> str:
        """Get the symbol of the ERC20 token"""
        try:
            cache_key = f"erc20_symbol:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.symbol().call()

            await self._cache_data(cache_key, response_data, self.longest_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Error getting token symbol: {e}")
            raise TokenDataFetchError(f"Failed to get token symbol: {str(e)}")


    async def get_decimals(self) -> int:
        """Get the number of decimals of the ERC20 token"""
        try:
            cache_key = f"erc20_decimals:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.decimals().call()

            await self._cache_data(cache_key, response_data, self.longest_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Error getting token decimals: {e}")
            raise TokenDataFetchError(f"Failed to get token decimals: {str(e)}")


    async def get_total_supply(self) -> int:
        """Get the total supply of the ERC20 token"""
        try:
            cache_key = f"erc20_total_supply:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_wei = await self.contract_instance.functions.totalSupply().call()
            response_data = self.w3.from_wei(response_wei, 'ether')

            await self._cache_data(cache_key, response_data, self.general_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Error getting total supply: {e}")
            raise TokenDataFetchError(f"Failed to get token total supply: {str(e)}")



    #NEW ENDPOINT TO BE DONE FOR THIS=======================
    async def owned_by_you(self) -> int:
        """Get the token balance of an address"""
        try:
            cache_key = f"erc20_owned_by_you:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_wei = await self.contract_instance.functions.balanceOf(
                self.w3.to_checksum_address(self.wallet)
            ).call()

            response_data = self.w3.from_wei(response_wei, 'ether')

            await self._cache_data(cache_key, response_data, self.general_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Error getting token balance: {e}")
            raise TokenDataFetchError(f"Failed to get token balance: {str(e)}")


    async def get_balance_of(self, account_address: str) -> int:
        """Get the token balance of an address"""
        try:
            cache_key = f"erc20_balance_of:{self.user_id}:{account_address}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_wei = await self.contract_instance.functions.balanceOf(
                self.w3.to_checksum_address(account_address)
            ).call()
            response_data = self.w3.from_wei(response_wei, 'ether')

            await self._cache_data(cache_key, response_data, self.general_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Error getting token balance: {e}")
            raise TokenDataFetchError(f"Failed to get token balance: {str(e)}")

    #NEW ENDPOINT TO BE DONE FOR THIS=======================


    async def get_allowance(self, spender_address: str) -> int:
        """Get allowance for a spender"""
        try:
            cache_key = f"erc20_allowance:{self.user_id}:{spender_address}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_wei = await self.contract_instance.functions.allowance(
                self.w3.to_checksum_address(self.wallet),
                self.w3.to_checksum_address(spender_address)
            ).call()

            response_data = self.w3.from_wei(response_wei, 'ether')

            await self._cache_data(cache_key, response_data, self.shortest_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Error getting allowance: {e}")
            raise TokenDataFetchError(f"Failed to get token allowance: {str(e)}")



class TokenTransactionBuilder(ERC20Base):
    """Handles building and preparing token transactions"""

    async def prepare_transaction(self, construct, platform_fee: int = None,
                                  default_wallet: bool = True, operation: json = None) -> Dict:
        """Prepare and save a transaction for later signing"""
        try:
            # Build transaction based on wallet type
            if not default_wallet:
                chain_id = await self.w3.eth.chain_id
                gas_price = hex(await self.w3.eth.gas_price)
                value = hex(platform_fee) if platform_fee else '0x0'

                constructor_args = {
                    'chainId': chain_id,
                    'gasPrice': gas_price,
                    'value': value
                }

                transaction = {
                    'chainId': chain_id,
                    'to': self.contract_instance.address,
                    'data': construct._encode_transaction_data(),
                    'gasPrice': gas_price,
                    'value': value
                }
            else:
                constructor_args = {
                    'chainId': await self.w3.eth.chain_id,
                    'from': self.wallet,
                    'gasPrice': await self.w3.eth.gas_price,
                    'nonce': await self.w3.eth.get_transaction_count(self.wallet),
                    'value': platform_fee
                }

                transaction = await construct.build_transaction(constructor_args)

            try:
                gas_estimate = await construct.estimate_gas(constructor_args)
                gas_buffer = 1 + (GAS_BUFFER_PERCENT / 100)  # Add buffer (e.g., 15%)
                buffered_gas = int(gas_estimate * gas_buffer)
            except Exception as gas_error:
                logger.error(f"Gas estimation error: {gas_error}")
                raise GasEstimationError(f"Failed to estimate gas: {str(gas_error)}")

            # Save transaction for signing
            tx_details = await self._save_unsigned_transaction(
                buffered_gas,
                transaction,
                platform_fee,
                default_wallet,
                operation
            )

            return tx_details
        except GasEstimationError:
            raise
        except Exception as e:
            logger.error(f"Constructor error: {e}")
            raise ContractInteractionError(f"Failed to construct transaction: {str(e)}")



    async def _save_unsigned_transaction(self, gas_estimate: int,
                                         unsigned_tx: dict, platform_fee: int = 0,
                                         default_wallet: bool = True, operation: json = None) -> Dict:
        """Save unsigned transaction and return details"""
        try:
            # Calculate transaction costs
            if not default_wallet:
                gas_price = int(unsigned_tx['gasPrice'], 16)
            else:
                gas_price = unsigned_tx['gasPrice']

            total_gas_wei = gas_estimate * gas_price
            total_gas_eth = self.w3.from_wei(total_gas_wei, 'ether')
            total_cost_wei = total_gas_wei + platform_fee
            total_cost_eth = self.w3.from_wei(total_cost_wei, 'ether')

            # Save transaction data
            transaction_data = json.dumps(unsigned_tx, default=make_json_serializable)
            try:
                pending_tx = ToBeSigned(
                    user_id=self.user_id,
                    transaction_data=transaction_data,
                    total_cost_wei=total_cost_wei,
                    total_cost_eth=total_cost_eth,
                    operation=json.dumps(operation) if operation else None,
                    expires_at=datetime.now(timezone.utc) + timedelta(minutes=15)
                )
                self.db.add(pending_tx)
                await self.db.commit()
                await self.db.refresh(pending_tx)
            except Exception as db_error:
                await self.db.rollback()
                logger.error(f"Database error: {db_error}")
                raise ContractInteractionError(f"Failed to save transaction: {str(db_error)}")

            # Prepare response based on wallet type
            if not default_wallet:
                tx_details = {
                    "status": "pending",
                    "transaction_data": unsigned_tx,
                    "gas_cost": {
                        "wei": str(total_gas_wei),
                        "pol": float(total_gas_eth)
                    },
                    "platform_fee": {
                        "wei": str(platform_fee),
                        "pol": float(self.w3.from_wei(platform_fee, 'ether'))
                    },
                    "total_cost": {
                        "wei": str(total_cost_wei),
                        "pol": float(total_cost_eth)
                    },
                    "signature_id": pending_tx.id
                }
            else:
                tx_details = {
                    "status": "pending",
                    "expires_at": pending_tx.expires_at.isoformat(),
                    "gas_cost": {
                        "wei": str(total_gas_wei),
                        "pol": float(total_gas_eth)
                    },
                    "platform_fee": {
                        "wei": str(platform_fee),
                        "pol": float(self.w3.from_wei(platform_fee, 'ether'))
                    },
                    "total_cost": {
                        "wei": str(total_cost_wei),
                        "pol": float(total_cost_eth)
                    },
                    "signature_id": pending_tx.id
                }

            return tx_details
        except Exception as e:
            logger.error(f"Error saving unsigned transaction: {e}")
            raise ContractInteractionError(f"Failed to save unsigned transaction: {str(e)}")




class TokenWriteOperations(TokenTransactionBuilder):
    """Handles token write operations (transfers, approvals, etc.)"""

    async def mint(self, recipient_address: str, amount: Decimal, default_wallet: bool = True) -> Dict:
        """Mint new tokens to a specified address"""
        try:

            chain_amount = self.w3.to_wei(amount, 'ether')

            platform_fee = self._calculate_platform_fee(
                amount=chain_amount,
                fee_percentage=PLATFORM_FEE_PERCENTAGE
            )
            construct = self.contract_instance.functions.mint(
                self.w3.to_checksum_address(recipient_address),
                chain_amount
            )

            operation = {
                "type": "erc20",
                "transaction": "mint",
                "metadata": {
                    "contract_type": "erc20",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "contract_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "to_address": recipient_address,
                        "amount": str(amount),
                        "amount_formatted": f"{str(amount)} {self.symbol}"
                    },
                    #"action": "token_mint"
                },
                "notification": {
                    "title": f"Token Minting - {self.symbol}",
                    "from_description": f"Minted {str(amount)} {self.symbol} tokens to {recipient_address}",
                    "to_description": f"You received {amount} tokens from minting by {self.wallet}",
                    "action_url": None
                }
            }

            tx_details = await self.prepare_transaction(
                construct,
                platform_fee,
                default_wallet,
                operation
            )
            return tx_details
        except Exception as e:
            logger.error(f"Mint error: {e}")
            if "execution reverted" in str(e):
                raise TokenMintError(f"Token minting failed: {str(e)}")




    async def transfer(self, recipient_address: str, amount: Decimal, default_wallet: bool = True) -> Dict:
        """Transfer tokens to a recipient"""
        try:
            chain_amount = self.w3.to_wei(amount, 'ether')
            # Check balance first
            try:
                balance = await self.contract_instance.functions.balanceOf(self.wallet).call()
                if balance < chain_amount:
                    raise InsufficientTokenBalanceError(
                        f"Insufficient token balance. Required: {amount}."
                    )
            except InsufficientTokenBalanceError:
                raise
            except Exception as balance_error:
                if "execution reverted" in str(balance_error):
                    raise TokenDataFetchError(f"Failed to fetch token balance: {str(balance_error)}")
                raise

            platform_fee = self._calculate_platform_fee(
                amount=chain_amount,
                fee_percentage=PLATFORM_FEE_PERCENTAGE
            )
            construct = self.contract_instance.functions.transfer(
                self.w3.to_checksum_address(recipient_address),
                chain_amount
            )

            operation = {
                "type": "erc20",
                "transaction": "transfer",
                "metadata": {
                    "contract_type": "erc20",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "to_address": recipient_address,
                        "amount": str(amount),
                        "amount_formatted": f"{str(amount)} {self.symbol}",
                    },
                    #"action": "token_transfer"
                },
                "notification": {
                    "title": f"Token Transfer - {self.symbol}",
                    "from_description": f"Sent {str(amount)} {self.symbol} tokens to {recipient_address}",
                    "to_description": f"You received {amount} {self.symbol} tokens from {self.wallet}",
                    "action_url": None
                }
            }

            tx_details = await self.prepare_transaction(
                construct,
                platform_fee,
                default_wallet,
                operation
            )
            return tx_details
        except (InsufficientTokenBalanceError, TokenDataFetchError):
            raise
        except Exception as e:
            logger.error(f"Transfer error: {e}")
            if "execution reverted" in str(e):
                raise TokenTransferError(f"Token transfer failed: {str(e)}")



    async def transfer_from(self, recipient_address: str, amount: Decimal,
                            sender_address: Optional[str] = None,
                            default_wallet: bool = True) -> Dict:
        """Transfer tokens from a sender to a recipient (requires prior approval)"""
        try:

            chain_amount = self.w3.to_wei(amount, 'ether')
            sender = sender_address if sender_address else self.wallet

            # Check allowance and balance
            try:
                # Check allowance
                allowance = await self.contract_instance.functions.allowance(
                    self.w3.to_checksum_address(sender),
                    self.wallet
                ).call()

                if allowance < chain_amount:
                    raise AllowanceError(
                        f"Insufficient allowance. Required: {amount}."
                    )

                # Check sender balance
                balance = await self.contract_instance.functions.balanceOf(
                    self.w3.to_checksum_address(sender)
                ).call()

                if balance < chain_amount:
                    raise InsufficientTokenBalanceError(
                        f"Sender has insufficient token balance. Required: {amount}."
                    )
            except (AllowanceError, InsufficientTokenBalanceError):
                raise
            except Exception as check_error:
                if "execution reverted" in str(check_error):
                    raise TokenDataFetchError(f"Failed to fetch token data: {str(check_error)}")
                raise

            construct = self.contract_instance.functions.transferFrom(
                self.w3.to_checksum_address(sender),
                self.w3.to_checksum_address(recipient_address),
                chain_amount
            )

            platform_fee = self._calculate_platform_fee(
                amount=chain_amount,
                fee_percentage=PLATFORM_FEE_PERCENTAGE
            )

            operation = {
                "type": "erc20",
                "transaction": "transfer_from",
                "metadata": {
                    "contract_type": "erc20",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": sender,
                        "to_address": recipient_address,
                        "executor_address": self.wallet,
                        "amount": str(amount),
                        "amount_formatted": f"{str(amount)} {self.symbol}",
                    },
                    #"action": "token_transfer_from"
                },
                "notification": {
                    "title": f"Token Transfer - {self.symbol}",
                    "from_description": f"Transferred {str(amount)} {self.symbol} tokens from {sender} to {recipient_address}",
                    "to_description": f"You received {amount} {self.symbol} tokens from {sender} via {self.wallet}",
                    "action_url": None
                }
            }

            tx_details = await self.prepare_transaction(
                construct,
                platform_fee,
                default_wallet,
                operation
            )

            return tx_details
        except (AllowanceError, InsufficientTokenBalanceError, TokenDataFetchError, AllowanceError):
            raise
        except Exception as e:
            logger.error(f"TransferFrom error: {e}")
            if "execution reverted" in str(e):
                raise TokenTransferError(f"Token transfer_from failed: {str(e)}")



    async def approve(self, spender_address: str, amount: Decimal, default_wallet: bool = True) -> Dict:
        """Approve a spender to spend tokens on behalf of the owner"""
        try:
            chain_amount = self.w3.to_wei(amount, 'ether')

            construct = self.contract_instance.functions.approve(
                self.w3.to_checksum_address(spender_address),
                chain_amount
            )
            platform_fee = self._calculate_platform_fee(
                amount=0,
                fee_percentage=PLATFORM_FEE_PERCENTAGE
            )

            operation = {
                "type": "erc20",
                "transaction": "approve",
                "metadata": {
                    "contract_type": "erc20",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "spender_address": spender_address,
                        "amount": str(amount),
                        "amount_formatted": f"{str(amount)} {self.symbol}",
                    },
                    #"action": "token_approval"
                },
                "notification": {
                    "title": f"Token Approval - {self.symbol}",
                    "from_description": f"Approved {spender_address} to spend up to {str(amount)} {self.symbol} tokens",
                    "to_description": f"You received approval to spend up to {amount} {self.symbol} tokens from {self.wallet}",
                    "action_url": None
                }
            }

            tx_details = await self.prepare_transaction(
                construct,
                platform_fee,
                default_wallet,
                operation
            )
            return tx_details
        except Exception as e:
            logger.error(f"Approve error: {e}")
            if "execution reverted" in str(e):
                raise TokenApprovalError(f"Token approval failed: {str(e)}")


    async def increase_allowance(self, spender_address: str, added_value: Decimal,
                                default_wallet: bool = True) -> Dict:
        """Increase the allowance for a spender"""
        try:
            chain_amount = self.w3.to_wei(added_value, 'ether')

            construct = self.contract_instance.functions.increaseAllowance(
                self.w3.to_checksum_address(spender_address),
                chain_amount
            )
            platform_fee = self._calculate_platform_fee(
                amount=0,
                fee_percentage=PLATFORM_FEE_PERCENTAGE
            )

            operation = {
                "type": "erc20",
                "transaction": "increase_allowance",
                "metadata": {
                    "contract_type": "erc20",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "to_address": spender_address,
                        "amount": str(added_value),
                        "amount_formatted": f"{str(added_value)} {self.symbol}",
                    },
                    #"action": "increase_token_allowance"
                },
                "notification": {
                    "title": f"Allowance Increase - {self.symbol}",
                    "from_description": f"Increased allowance for {spender_address} by {str(added_value)} {self.symbol} tokens",
                    "to_description": f"Your allowance to spend {self.symbol} tokens from {self.wallet} was increased by {added_value}",
                    "action_url": None
                }
            }

            tx_details = await self.prepare_transaction(
                construct,
                platform_fee,
                default_wallet,
                operation
            )
            return tx_details
        except Exception as e:
            logger.error(f"Increase allowance error: {e}")
            if "execution reverted" in str(e):
                raise AllowanceError(f"Increasing allowance failed: {str(e)}")


    async def decrease_allowance(self, spender_address: str, subtracted_value: Decimal,
                                default_wallet: bool = True) -> Dict:
        """Decrease the allowance for a spender"""
        try:
            # Check current allowance to prevent underflow
            chain_amount = self.w3.to_wei(subtracted_value, 'ether')

            try:
                current_allowance = await self.contract_instance.functions.allowance(
                    self.wallet,
                    self.w3.to_checksum_address(spender_address)
                ).call()
                print(current_allowance)
                if current_allowance < chain_amount:
                    raise AllowanceError(
                        f"Cannot decrease allowance below zero."
                    )
            except AllowanceError:
                raise
            except Exception as check_error:
                if "execution reverted" in str(check_error):
                    raise TokenDataFetchError(f"Failed to fetch allowance data: {str(check_error)}")
                raise

            construct = self.contract_instance.functions.decreaseAllowance(
                self.w3.to_checksum_address(spender_address),
                chain_amount
            )

            platform_fee = self._calculate_platform_fee(
                amount=0,
                fee_percentage=PLATFORM_FEE_PERCENTAGE
            )

            operation = {
                "type": "erc20",
                "transaction": "decrease_allowance",
                "metadata": {
                    "contract_type": "erc20",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "to_address": spender_address,
                        "amount": str(subtracted_value),
                        "amount_formatted": f"{str(subtracted_value)} {self.symbol}",
                    },
                    #"action": "decrease_token_allowance"
                },
                "notification": {
                    "title": f"Allowance Decrease - {self.symbol}",
                    "from_description": f"Decreased allowance for {spender_address} by {str(subtracted_value)} {self.symbol} tokens",
                    "to_description": f"Your allowance to spend {self.symbol} tokens from {self.wallet} was decreased by {subtracted_value}",
                    "action_url": None
                }
            }

            tx_details = await self.prepare_transaction(
                construct,
                platform_fee,
                default_wallet,
                operation
            )
            return tx_details
        except (AllowanceError, TokenDataFetchError):
            raise
        except Exception as e:
            logger.error(f"Decrease allowance error: {e}")
            if "execution reverted" in str(e):
                raise AllowanceError(f"Decreasing allowance failed: {str(e)}")



@class_exponential_backoff()
class ERC20Interaction(TokenViewOperations, TokenWriteOperations):
    """Main class for ERC20 token interactions"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 web3: AsyncWeb3, redis: Optional[Redis] = None):
        """Initialize the interaction with the ERC20 contract"""
        try:
            super().__init__(db, user_id, contract_id, web3, redis)
        except Exception as e:
            logger.error(f"Error initializing ERC20Interaction: {e}")
            handle_erc20_error(e)


    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass