from typing import Optional, Any, Dict, List, Union
from fastapi import UploadFile, File, Query
from web3 import Web3
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from sqlalchemy.orm import selectinload
from datetime import datetime, timedelta, timezone
import time
import json
import math
from api.core.general_dep import upload_image
from api.v1.deploy_contracts.models import ToBeSigned, make_json_serializable, TransactionType
from .schemas import Metadata, NFTMetadataSchema, NFTSchemaList, NFTSchema
from .ipfs import IPFSUploader
from .models import NFTMetadata, NFT
from api.core.blockchain_dep import class_exponential_backoff
from api.v1.contracts.schemas import Order

# Import exception handling
from .exceptions import (
    handle_erc721_error, NFTMintError, NFTTransferError, NFTApprovalError,
    TokenURIError, OwnershipError, TokenNotFoundError, NotTokenOwnerError,
    UnauthorizedOperatorError, MetadataError, IPFSUploadError, FileProcessingError,
    GasEstimationError, ContractInteractionError, InvalidTokenIdError, InvalidAddressError,
    handle_contract_notification, logger
)
from api.v1.deploy_contracts.services_sign import TokenBase
from api.v1.deploy_contracts.exceptions import CacheError
from web3 import AsyncWeb3
from config import config
from redis.asyncio import Redis
from api.v1.websockets.models import NotificationStatus, NotificationType, NotificationPriority

# Configuration constants
PLATFORM_FEE_PERCENTAGE = config.PLATFORM_FEE_PERCENTAGE
GAS_BUFFER_PERCENT = 15
ERC721_MINT = "./uploads/mint_erc721"
#os.makedirs(ERC721_MINT, exist_ok=True)
BASE_URL = config.BASE_URL



class NFTBase(TokenBase):
    """Base class for NFT operations"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 web3: AsyncWeb3, redis: Optional[Redis] = None):
        try:
            super().__init__(db, user_id, contract_id, web3, redis)
        except Exception as e:
            logger.error(f"Error initializing NFT base: {e}")
            raise


    async def _validate_token_ownership(self, token_id: int) -> str:
        """Validate token exists and get owner"""
        try:
            owner = await self.contract_instance.functions.ownerOf(token_id).call()
            return owner
        except Exception as e:
            if "execution reverted" in str(e):
                raise TokenNotFoundError(f"Token ID {token_id} does not exist")
            logger.error(f"Token ownership validation error: {e}")
            raise ContractInteractionError(f"Failed to validate token ownership: {str(e)}")

    async def _check_authorization(self, token_id: int, owner: str) -> None:
        """Check if caller is authorized to operate on token"""
        if owner.lower() != self.wallet.lower():
            # Check if caller is approved
            approved = await self.contract_instance.functions.getApproved(token_id).call()
            is_operator = await self.contract_instance.functions.isApprovedForAll(owner, self.wallet).call()

            if approved.lower() != self.wallet.lower() and not is_operator:
                raise UnauthorizedOperatorError(
                    f"You are not authorized to operate on token ID {token_id}"
                )



class NFTTransactionBuilder(NFTBase):
    """Handles building and preparing NFT transactions"""

    """
    @classmethod
    async def create(cls, db: AsyncSession, user_id: int, contract_id: int,
                    web3: AsyncWeb3, redis: Optional[Redis] = None):
        Factory method to create and initialize an NFTTransactionBuilder instance
        instance = await super().create(db, user_id, contract_id, web3, redis)
        return instance
    """

    async def prepare_transaction(self, construct, platform_fee: int = None,
                                  is_nft: int = None, default_wallet: bool = True,
                                  operation: json = None) -> Dict:
        """Prepare and save a transaction for later signing"""
        try:
            if platform_fee is None:
                platform_fee = self._calculate_platform_fee(
                    amount=0,
                    fee_percentage=PLATFORM_FEE_PERCENTAGE)

            # Build transaction based on wallet type
            try:
                if not default_wallet:
                    chain_id = await self.w3.eth.chain_id
                    gas_price = hex(await self.w3.eth.gas_price)
                    value = hex(platform_fee) if platform_fee else '0x0'

                    constructor_args = {
                        'chainId': chain_id,
                        'gasPrice': gas_price,
                        'value': value
                    }

                    transaction = {
                        'chainId': chain_id,
                        'to': self.contract_instance.address,
                        'data': construct._encode_transaction_data(),
                        'gasPrice': gas_price,
                        'value': value
                    }
                else:
                    constructor_args = {
                        'chainId': await self.w3.eth.chain_id,
                        'from': self.wallet,
                        'gasPrice': await self.w3.eth.gas_price,
                        'nonce': await self.w3.eth.get_transaction_count(self.wallet),
                        'value': platform_fee
                    }

                    transaction = await construct.build_transaction(constructor_args)
            except Exception as chain_error:
                logger.error(f"Transaction build failed: {str(chain_error)}")
                raise

            # Estimate gas with buffer
            try:
                gas_estimate = await construct.estimate_gas(constructor_args)
            except Exception as gas_error:
                logger.error(f"Gas estimation error: {gas_error}")
                raise GasEstimationError(f"Failed to estimate gas: {str(gas_error)}")

            # Save transaction for signing
            tx_details = await self._save_unsigned_transaction(
                gas_estimate=gas_estimate,
                unsigned_tx=transaction,
                platform_fee=platform_fee,
                is_nft=is_nft,
                default_wallet=default_wallet,
                operation=operation
            )

            return tx_details
        except (GasEstimationError, ContractInteractionError):
            raise
        except Exception as e:
            logger.error(f"Transaction preparation error: {e}")
            raise ContractInteractionError(f"{str(e)}")

    async def _save_unsigned_transaction(self, gas_estimate: int, unsigned_tx: dict,
                                        platform_fee: int = 0, is_nft: int = None,
                                        default_wallet: bool = True, operation: json = None) -> Dict:
        """Save unsigned transaction and return details"""
        try:
            # Calculate gas with buffer
            gas_buffer = 1 + (GAS_BUFFER_PERCENT / 100)
            buffered_gas = int(gas_estimate * gas_buffer)

            # Calculate costs
            if not default_wallet:
                gas_price = int(unsigned_tx['gasPrice'], 16)
            else:
                gas_price = unsigned_tx['gasPrice']

            total_gas_wei = buffered_gas * gas_price
            total_gas_eth = self.w3.from_wei(total_gas_wei, 'ether')
            total_cost_wei = total_gas_wei + platform_fee
            total_cost_eth = self.w3.from_wei(total_cost_wei, 'ether')

            # Save transaction data
            transaction_data = json.dumps(unsigned_tx, default=make_json_serializable)

            try:
                pending_tx = ToBeSigned(
                    user_id=self.user_id,
                    transaction_data=transaction_data,
                    total_cost_wei=total_cost_wei,
                    total_cost_eth=total_cost_eth,
                    nft_id=is_nft,
                    operation=json.dumps(operation) if operation else None,
                    expires_at=datetime.now(timezone.utc) + timedelta(minutes=15)
                )
                self.db.add(pending_tx)
                await self.db.commit()
                await self.db.refresh(pending_tx)
            except Exception as db_error:
                await self.db.rollback()
                logger.error(f"Database error: {db_error}")
                raise ContractInteractionError(f"Failed to save transaction: {str(db_error)}")

            # Prepare response based on wallet type
            if not default_wallet:
                tx_details = {
                    "status": "pending",
                    "transaction_data": unsigned_tx,
                    "gas_cost": {
                        "wei": str(total_gas_wei),
                        "pol": float(total_gas_eth)
                    },
                    "platform_fee": {
                        "wei": str(platform_fee),
                        "pol": float(self.w3.from_wei(platform_fee, 'ether'))
                    },
                    "total_cost": {
                        "wei": str(total_cost_wei),
                        "pol": float(total_cost_eth)
                    },
                    "signature_id": pending_tx.id
                }
            else:
                tx_details = {
                    "status": "pending",
                    "expires_at": pending_tx.expires_at.isoformat(),
                    "gas_cost": {
                        "wei": str(total_gas_wei),
                        "pol": float(total_gas_eth)
                    },
                    "platform_fee": {
                        "wei": str(platform_fee),
                        "pol": float(self.w3.from_wei(platform_fee, 'ether'))
                    },
                    "total_cost": {
                        "wei": str(total_cost_wei),
                        "pol": float(total_cost_eth)
                    },
                    "signature_id": pending_tx.id
                }

            return tx_details
        except ContractInteractionError:
            raise
        except Exception as e:
            logger.error(f"Save unsigned error: {e}")
            raise ContractInteractionError(f"Failed to save unsigned transaction: {str(e)}")



class NFTQueryOperations(NFTBase):
    """Handles NFT query operations"""

    async def get_all_nfts(self, page: int = Query(1, ge=1), page_size: int = Query(10, ge=1), order: Optional[Order] = Order.DESC) -> Dict:
        """Get all NFTs for a contract with pagination"""
        try:
            cache_key = f"get_all_nfts:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data
        
            count_stmt = select(func.count(NFT.id)).select_from(NFT).where(
                NFT.contract_id == self.contract_id,
                NFT.status == "success" 
            )
            count_result = await self.db.execute(count_stmt)
            total_nfts = count_result.scalar_one_or_none() or 0 

            if total_nfts == 0:
                raise TokenNotFoundError(f"No successful NFTs found for contract {self.contract_id}")

            start = (page - 1) * page_size
            total_pages = math.ceil(total_nfts / page_size)

            if page > total_pages:
                 raise TokenNotFoundError(f"Page {page} requested, but only {total_pages} pages available.")


            if order == Order.DESC:
                order_by = NFT.id.desc()
            else:
                order_by = NFT.id.asc()

            stmt = (
                select(NFT)
                .where(
                    NFT.contract_id == self.contract_id,
                    NFT.status == "success"
                )
                .options(selectinload(NFT.nft_metadata))
                .order_by(order_by)
                .offset(start)
                .limit(page_size)
            )

            result = await self.db.execute(stmt)
            nfts = result.unique().scalars().all()

            all_nfts_schema = [
                NFTSchemaList(
                    token_id=nft.token_id,
                    image=nft.nft_metadata.image if nft.nft_metadata else None,
                ) for nft in nfts
            ]

            await self._cache_data(cache_key, all_nfts_schema, self.longest_cache_time)
            return {
                "nfts": all_nfts_schema,
                "pagination": {
                    "total_items": total_nfts,
                    "total_pages": total_pages,
                    "current_page": page,
                    "page_size": page_size,
                    "has_next": page < total_pages,
                    "has_previous": page > 1,
                }
            }
        except TokenNotFoundError as e:
             raise e
        except Exception as e:
            logger.exception(f"Failed to get NFTs for contract {self.contract_id}: {e}")
            raise ContractInteractionError(f"Failed to retrieve NFT list: An unexpected error occurred.") from e
        

    async def get_one_nft(self, token_id: int) -> NFTSchema:
        """Get details for a specific NFT"""
        try:
            cache_key = f"get_one_nft:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data
        
            if not isinstance(token_id, int) or token_id < 0:
                raise InvalidTokenIdError(f"Invalid token ID: {token_id}")

            stmt = select(NFT).where(
                NFT.contract_id == self.contract_id,
                NFT.token_id == token_id
            ).options(selectinload(NFT.nft_metadata))
            result = await self.db.execute(stmt)
            nft = result.unique().scalars().one_or_none()

            if not nft:
                raise TokenNotFoundError(f"NFT with token ID {token_id} not found")

            await self._cache_data(cache_key, nft, self.longest_cache_time)
            return NFTSchema(
                metadata=NFTMetadataSchema.model_validate(nft.nft_metadata),
                contract_id=nft.contract_id,
                token_id=nft.token_id,
                metadata_uri=nft.metadata_uri,
                created_by=nft.created_by,
                created_at=nft.created_at
            )
        except (InvalidTokenIdError, TokenNotFoundError):
            raise
        except Exception as e:
            logger.error(f"Get one NFT error: {e}")
            raise ContractInteractionError(f"Failed to get NFT: {str(e)}")


    async def nft_metadata(self, metadata: Optional[Metadata] = None) -> int:
        """Store NFT metadata and return its ID"""
        try:
            cache_key = f"get_nft_metadata:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data
        
            if not metadata:
                raise MetadataError("Metadata is required")

            metedata = NFTMetadata(
                name=metadata.name,
                description=metadata.description,
                attributes=[attribute.model_dump() for attribute in metadata.attributes]
            )

            self.db.add(metedata)
            await self.db.commit()
            await self.db.refresh(metedata)

            logger.info(f"Created metadata record with ID {metedata.id}")

            await self._cache_data(cache_key, metedata.id, self.longest_cache_time)
            return metedata.id
        except Exception as e:
            logger.error(f"NFT metadata error: {e}")
            if isinstance(e, MetadataError):
                raise
            raise MetadataError(f"Failed to create metadata: {str(e)}")


class NFTViewOperations(NFTBase):
    """Handles read-only NFT operations"""

    async def get_nfts_by_wallet(self) -> List[int]:
        """Get all NFTs owned by the user"""
        try:
            return await self.contract_instance.functions.tokensOfOwner(self.wallet).call()
        except Exception as e:
            logger.error(f"Get NFTs by wallet error: {e}")
            raise ContractInteractionError(f"Failed to get NFTs by wallet: {str(e)}")

    async def get_contract_name(self) -> str:
        """Get the name of the ERC721 token"""
        try:
            cache_key = f"nft_name:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.name().call()

            await self._cache_data(cache_key, response_data, self.longest_cache_time)
            return response_data

        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Get contract name error: {e}")
            raise ContractInteractionError(f"Failed to get contract name: {str(e)}")



    async def owned_by_you(self) -> int:
        """Get the token balance of the current user"""
        try:
            cache_key = f"nft_owned_by_you:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.balanceOf(
                self.w3.to_checksum_address(self.wallet)
            ).call()

            await self._cache_data(cache_key, response_data, self.general_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Get balance error: {e}")
            raise ContractInteractionError(f"Failed to get balance: {str(e)}")


    async def get_balance_of(self, account_address:str) -> int:
        """Get the token balance of the current user"""
        try:
            cache_key = f"nft_balance_of:{self.user_id}:{account_address}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.balanceOf(
                self.w3.to_checksum_address(account_address)
            ).call()

            await self._cache_data(cache_key, response_data, self.general_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Get balance error: {e}")
            raise ContractInteractionError(f"Failed to get balance: {str(e)}")




    async def get_approved(self, token_id: int) -> str:
        """Get the address approved for a specific token"""
        try:
            cache_key = f"nft_approved:{self.user_id}:{token_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            if not isinstance(token_id, int) or token_id < 0:
                raise InvalidTokenIdError(f"Invalid token ID: {token_id}")

            response_data =  await self.contract_instance.functions.getApproved(token_id).call()

            await self._cache_data(cache_key, response_data, self.longest_cache_time)
            return response_data

        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Get approved error: {e}")
            if "execution reverted" in str(e):
                raise TokenNotFoundError(f"Token ID {token_id} does not exist")
            raise ContractInteractionError(f"Failed to get approved address: {str(e)}")


    async def is_approved_for_all(self, operator: str) -> bool:
        """Check if an operator is approved for all tokens"""
        try:
            cache_key = f"nft_is_approved_for_all:{self.user_id}:{operator}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data


            if not Web3.is_address(operator):
                raise InvalidAddressError(f"Invalid operator address: {operator}")

            response_data = await self.contract_instance.functions.isApprovedForAll(
                self.w3.to_checksum_address(self.wallet),
                self.w3.to_checksum_address(operator)
            ).call()

            await self._cache_data(cache_key, response_data, self.longest_cache_time)
            return response_data

        except CacheError:
            raise
        except InvalidAddressError:
            raise
        except Exception as e:
            logger.error(f"Is approved for all error: {e}")
            raise ContractInteractionError(f"Failed to check approval status: {str(e)}")



    async def get_contract_symbol(self) -> str:
        """Get the symbol of the ERC721 token"""
        try:

            cache_key = f"nft_symbol:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.symbol().call()

            await self._cache_data(cache_key, response_data, self.longest_cache_time)
            return response_data

        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Get contract symbol error: {e}")
            raise ContractInteractionError(f"Failed to get contract symbol: {str(e)}")



    async def get_contract_owner(self) -> str:
        """Get the owner of the contract"""
        try:
            cache_key = f"nft_owner:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.owner().call()

            await self._cache_data(cache_key, response_data, self.general_cache_time)
            return response_data

        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Get contract owner error: {e}")
            raise ContractInteractionError(f"Failed to get contract owner: {str(e)}")


    async def get_owner_of_token(self, token_id: int) -> str:
        """Get the owner of a specific token"""
        try:
            cache_key = f"nft_owner_of_token:{self.user_id}:{token_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            if not isinstance(token_id, int) or token_id < 0:
                raise InvalidTokenIdError(f"Invalid token ID: {token_id}")

            response_data = await self.contract_instance.functions.ownerOf(token_id).call()
            await self._cache_data(cache_key, response_data, self.general_cache_time)
            return response_data

        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Get owner of token error: {e}")
            if "execution reverted" in str(e):
                raise TokenNotFoundError(f"Token ID {token_id} does not exist")
            raise ContractInteractionError(f"Failed to get token owner: {str(e)}")


    async def get_token_id(self) -> int:
        """Get the current token ID"""
        try:
            return await self.contract_instance.functions.getCurrentTokenId().call()
        except Exception as e:
            logger.error(f"Get token ID error: {e}")
            raise ContractInteractionError(f"Failed to get current token ID: {str(e)}")


    async def get_token_uri(self, token_id: int) -> str:
        """Get the token URI"""
        try:
            cache_key = f"nft_token_uri:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            if not isinstance(token_id, int) or token_id < 0:
                raise InvalidTokenIdError(f"Invalid token ID: {token_id}")

            response_data = await self.contract_instance.functions.tokenURI(token_id).call()

            await self._cache_data(cache_key, response_data, self.longest_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Get token URI error: {e}")
            if "execution reverted" in str(e):
                raise TokenNotFoundError(f"Token ID {token_id} does not exist")
            raise TokenURIError(f"Failed to get token URI: {str(e)}")


    async def supports_interface(self, interface_id: str) -> bool:
        """Check if contract supports an interface"""
        try:
            cache_key = f"supports_interface:{self.user_id}:{interface_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.supportsInterface(interface_id).call()

            await self._cache_data(cache_key, response_data, self.longest_cache_time)
            return response_data

        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Supports interface error: {e}")
            raise ContractInteractionError(f"Failed to check interface support")


    async def get_total_supply(self) -> int:
        """Get the total supply of tokens"""
        try:
            cache_key = f"nft_total_supply:{self.user_id}:{self.contract_id}"
            cached_data = await self._get_cached_data(cache_key)
            if cached_data:
                return cached_data

            response_data = await self.contract_instance.functions.totalSupply().call()

            await self._cache_data(cache_key, response_data, self.general_cache_time)
            return response_data
        except CacheError:
            raise
        except Exception as e:
            logger.error(f"Get total supply error: {e}")
            raise ContractInteractionError(f"Failed to get total supply: {str(e)}")



class NFTWriteOperations(NFTTransactionBuilder):
    """Handles NFT write operations (minting, transfers, approvals, etc.)"""

    async def mint_nft(self, metadata_id: int = None, file: UploadFile = File(...),
                      default_wallet: bool = True) -> Dict:
        """Mint a new NFT"""
        try:
            if not file:
                raise FileProcessingError("File is required for NFT minting")

            try:
                unique_filename, full_file_path = await upload_image(file, ERC721_MINT)
                logger.info(f"Image uploaded: {unique_filename}")
            except Exception as upload_error:
                logger.error(f"File upload error: {upload_error}")
                raise FileProcessingError(f"Failed to upload NFT image: {str(upload_error)}")

            # Get next token ID
            try:
                token_id = await self.contract_instance.functions.getCurrentTokenId().call()
                logger.info(f"Next token ID will be: {token_id}")
            except Exception as contract_error:
                logger.error(f"Error getting token ID: {contract_error}")
                raise ContractInteractionError(f"Failed to get next token ID: {str(contract_error)}")

            # Get metadata if provided
            if metadata_id:
                # Use select() from sqlalchemy.future
                stmt = select(NFTMetadata).where(NFTMetadata.id == metadata_id)
                result = await self.db.execute(stmt)
                metadata = result.scalars().one_or_none()
                if not metadata:
                    raise MetadataError(f"Metadata with ID {metadata_id} not found")
            else:
                metadata = None

            # Upload to IPFS
            try:
                uploader = IPFSUploader()
                metadata_cids = await uploader.generate_and_upload_collection(
                    token_id,
                    full_file_path,
                    metadata
                )
                logger.info(f"Uploaded to IPFS with CID: {metadata_cids}")
            except Exception as ipfs_error:
                logger.error(f"IPFS upload error: {ipfs_error}")
                raise IPFSUploadError(f"Failed to upload to IPFS: {str(ipfs_error)}")

            # Update or create metadata record
            try:
                if metadata:
                    metadata.image = f"{BASE_URL}/mint_erc721/{unique_filename}"
                else:
                    metadata = NFTMetadata(
                        image=f"{BASE_URL}/mint_erc721/{unique_filename}"
                    )
                    self.db.add(metadata)
                    await self.db.commit()
                    await self.db.refresh(metadata)
            except Exception as db_error:
                logger.error(f"Database error: {db_error}")
                raise MetadataError(f"Failed to update metadata: {str(db_error)}")

            # Create NFT record
            try:
                nft = NFT(
                    created_by=self.user_id,
                    contract_id=self.contract_id,
                    metadata_id=metadata.id if metadata is not None else None,
                    token_id=token_id,
                    status="pending",
                    metadata_uri=f"ipfs://{metadata_cids}"
                )
                self.db.add(nft)
                await self.db.commit()
                await self.db.refresh(nft)
                logger.info(f"Created NFT record with ID {nft.id}")
            except Exception as nft_error:
                logger.error(f"NFT record creation error: {nft_error}")
                raise NFTMintError(f"Failed to create NFT record: {str(nft_error)}")

            # Create contract transaction
            try:
                construct = self.contract_instance.functions.mint(
                    self.w3.to_checksum_address(self.wallet),
                    f"ipfs://{metadata_cids}"
                )

                operation = {
                    "type": "erc721",
                    "transaction": "mint",
                    "metadata": {
                        "contract_type": "erc721",
                        "token_name": self.name,
                        "token_symbol": self.symbol,
                        "token_address": self.contract_instance.address,
                        "transaction_details": {
                            "hash": None,
                            "from_address": self.wallet,
                            "to_address": self.wallet,
                            "token_id": token_id,
                            "metadata_uri": f"ipfs://{metadata_cids}"
                        },
                        #"action": "nft_mint"
                    },
                    "notification": {
                        "title": f"NFT Minting - {self.symbol}",
                        "from_description": f"Minted NFT token #{token_id}",
                        "to_description": f"You received NFT token #{token_id} from minting",
                        "action_url": None
                    }
                }

                details = await self.prepare_transaction(
                    construct=construct,
                    is_nft=nft.id,
                    default_wallet=default_wallet,
                    operation=operation
                )

                return details
            except Exception as tx_error:
                logger.error(f"Transaction creation error: {tx_error}")
                raise NFTMintError(f"Failed to create mint transaction: {str(tx_error)}")
        except (FileProcessingError, IPFSUploadError, MetadataError, NFTMintError, ContractInteractionError):
            raise
        except Exception as e:
            logger.error(f"Mint NFT error: {e}")
            raise NFTMintError(f"NFT minting failed: {str(e)}")


    async def approve_nft(self, to_address: str, token_id: int, default_wallet: bool = True) -> Dict:
        """Approve a spender for a specific NFT"""
        try:
            if not Web3.is_address(to_address):
                raise InvalidAddressError(f"Invalid address: {to_address}")

            try:
                owner = await self._validate_token_ownership(token_id)
                if owner.lower() != self.wallet.lower():
                    raise NotTokenOwnerError(f"You are not the owner of token ID {token_id}")
                if owner.lower() == to_address.lower():
                    raise NFTApprovalError(f"Cannot approve the token to its current owner")

            except (TokenNotFoundError, NotTokenOwnerError, NFTApprovalError):
                raise

            construct = self.contract_instance.functions.approve(
                self.w3.to_checksum_address(to_address),
                token_id
            )

            operation = {
                "type": "erc721",
                "transaction": "approve",
                "metadata": {
                    "contract_type": "erc721",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "to_address": to_address,
                        "token_id": token_id
                    },
                    #"action": "nft_approval"
                },
                "notification": {
                    "title": f"NFT Approval - {self.symbol}",
                    "from_description": f"Approved {to_address} to manage your NFT token #{token_id}",
                    "to_description": f"You received approval to manage NFT token #{token_id} from {self.wallet}",
                    "action_url": None
                }
            }

            tx_details = await self.prepare_transaction(
                construct=construct,
                default_wallet=default_wallet,
                operation=operation
            )

            return tx_details
        except (InvalidAddressError, TokenNotFoundError, NotTokenOwnerError, NFTApprovalError):
            raise
        except Exception as e:
            logger.error(f"Approve NFT error: {e}")
            if "execution reverted" in str(e):
                raise NFTApprovalError(f"NFT approval failed: {str(e)}")
            raise NFTApprovalError(f"Failed to approve NFT: {str(e)}")


    async def set_approval_for_all(self, operator: str, approved: bool, default_wallet: bool = True) -> Dict:
        """Set approval for all NFTs to an operator"""
        try:
            # Validate parameters
            if not Web3.is_address(operator):
                raise InvalidAddressError(f"Invalid operator address: {operator}")

            if not isinstance(approved, bool):
                raise ValueError("Approved parameter must be a boolean")

            try:
                owner = await self.contract_instance.functions.owner().call()
                if owner.lower() != self.wallet.lower():
                    raise NotTokenOwnerError(f"You are not the owner of the contract")
                if owner.lower() == operator.lower():
                    raise NFTApprovalError(f"Cannot approve contract tokens to the contract owner")

            except (TokenNotFoundError, NotTokenOwnerError, NFTApprovalError):
                raise

            # Prepare setApprovalForAll function
            construct = self.contract_instance.functions.setApprovalForAll(
                self.w3.to_checksum_address(operator),
                approved
            )

            approval_status = "granted" if approved else "revoked"

            operation = {
                "type": "erc721",
                "transaction": "approve_for_all",
                "metadata": {
                    "contract_type": "erc721",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "to_address": operator,
                        "approved": approved
                    },
                    #"action": "nft_approval_for_all"
                },
                "notification": {
                    "title": f"NFT Collection Approval - {self.symbol}",
                    "from_description": f"{approval_status.capitalize()} approval for {operator} to manage all your NFTs",
                    "to_description": f"You were {approval_status} approval to manage all NFTs owned by {self.wallet}",
                    "action_url": None
                }
            }

            # Create transaction
            tx_details = await self.prepare_transaction(
                construct=construct,
                default_wallet=default_wallet,
                operation=operation
            )

            return tx_details
        except (InvalidAddressError, TokenNotFoundError, NotTokenOwnerError, NFTApprovalError):
            raise
        except Exception as e:
            logger.error(f"Set approval for all error: {e}")
            if "execution reverted" in str(e):
                raise NFTApprovalError(f"Setting approval for all failed: {str(e)}")
            raise NFTApprovalError(f"Failed to set approval for all: {str(e)}")

    async def transfer_from(self, to_address: str, token_id: int, default_wallet: bool = True) -> Dict:
        """Transfer NFT without safety checks"""
        try:

            if not Web3.is_address(to_address):
                raise InvalidAddressError(f"Invalid address: {to_address}")

            try:
                owner = await self._validate_token_ownership(token_id)
                await self._check_authorization(token_id, owner)

                if owner.lower() == to_address.lower():
                    raise NFTApprovalError(f"Cannot transfer the token to its current owner")

            except (TokenNotFoundError, UnauthorizedOperatorError, NFTApprovalError):
                raise

            # Prepare transferFrom function
            construct = self.contract_instance.functions.transferFrom(
                self.w3.to_checksum_address(self.wallet),
                self.w3.to_checksum_address(to_address),
                token_id
            )

            operation = {
                "type": "erc721",
                "transaction": "transfer",
                "metadata": {
                    "contract_type": "erc721",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "to_address": to_address,
                        "token_id": token_id
                    },
                    #"action": "nft_transfer"
                },
                "notification": {
                    "title": f"NFT Transfer - {self.symbol}",
                    "from_description": f"Transferred NFT token #{token_id} to {to_address}",
                    "to_description": f"You received NFT token #{token_id} from {self.wallet}",
                    "action_url": None
                }
            }

            tx_details = await self.prepare_transaction(
                construct=construct,
                default_wallet=default_wallet,
                operation=operation
            )

            return tx_details
        except (InvalidAddressError, TokenNotFoundError, UnauthorizedOperatorError, NFTApprovalError):
            raise
        except Exception as e:
            logger.error(f"Transfer error: {e}")
            if "execution reverted" in str(e):
                raise NFTTransferError(f"NFT transfer failed: {str(e)}")
            raise NFTTransferError(f"Failed to transfer NFT: {str(e)}")


    async def safe_transfer_from(self, to_address: str, token_id: int, default_wallet: bool = True) -> Dict:
        """Safely transfer NFT with receiver checks"""
        try:

            if not Web3.is_address(to_address):
                raise InvalidAddressError(f"Invalid address: {to_address}")

            try:
                owner = await self._validate_token_ownership(token_id)
                await self._check_authorization(token_id, owner)

                if owner.lower() == to_address.lower():
                    raise NFTApprovalError(f"Cannot transfer the token to its current owner")
            except (TokenNotFoundError, UnauthorizedOperatorError, NFTApprovalError):
                raise

            hex_data = '0x'

            construct = self.contract_instance.functions.safeTransferFrom(
                self.w3.to_checksum_address(self.wallet),
                self.w3.to_checksum_address(to_address),
                token_id,
                hex_data
            )

            operation = {
                "type": "erc721",
                "transaction": "safe_transfer",
                "metadata": {
                    "contract_type": "erc721",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "to_address": to_address,
                        "token_id": token_id,
                        "transfer_type": "safe"
                    },
                    #"action": "nft_safe_transfer"
                },
                "notification": {
                    "title": f"Safe NFT Transfer - {self.symbol}",
                    "from_description": f"Safely transferred NFT token #{token_id} to {to_address}",
                    "to_description": f"You safely received NFT token #{token_id} from {self.wallet}",
                    "action_url": None
                }
            }

            # Create transaction
            tx_details = await self.prepare_transaction(
                construct=construct,
                default_wallet=default_wallet,
                operation=operation
            )

            return tx_details
        except (InvalidAddressError, TokenNotFoundError, UnauthorizedOperatorError, NFTApprovalError):
            raise
        except Exception as e:
            logger.error(f"Safe transfer error: {e}")
            if "execution reverted" in str(e):
                raise NFTTransferError(f"NFT safe transfer failed: {str(e)}")
            raise NFTTransferError(f"Failed to safely transfer NFT: {str(e)}")

    async def safe_transfer_from_with_data(self, to_address: str, token_id: int, data: Any, default_wallet: bool = True) -> Dict:
        """Safely transfer NFT with data and receiver checks"""
        try:

            if not Web3.is_address(to_address):
                raise InvalidAddressError(f"Invalid address: {to_address}")

            # Prepare the data parameter
            if isinstance(data, str):
                if data.startswith('0x'):  # Already hex
                    hex_data = data
                else:  # Convert string to hex
                    hex_data = '0x' + data.encode().hex()
            elif isinstance(data, dict):  # For structured data
                hex_data = '0x' + bytes(str(data).encode()).hex()
            elif isinstance(data, bytes):  # Already bytes
                hex_data = '0x' + data.hex()
            else:
                hex_data = '0x'  # Empty data if none provided

            logger.info(f"Data for transfer: {hex_data}")


            try:
                owner = await self._validate_token_ownership(token_id)
                await self._check_authorization(token_id, owner)
            except (TokenNotFoundError, UnauthorizedOperatorError):
                raise


            construct = self.contract_instance.functions.safeTransferFrom(
                self.w3.to_checksum_address(self.wallet),
                self.w3.to_checksum_address(to_address),
                token_id,
                hex_data
            )

            operation = {
                "type": "erc721",
                "transaction": "safe_transfer_with_data",
                "metadata": {
                    "contract_type": "erc721",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "to_address": to_address,
                        "token_id": token_id,
                        "transfer_type": "safe_with_data",
                        "data": hex_data if len(hex_data) < 100 else f"{hex_data[:97]}..."
                    },
                    #"action": "nft_safe_transfer_with_data"
                },
                "notification": {
                    "title": f"Safe NFT Transfer with Data - {self.symbol}",
                    "from_description": f"Safely transferred NFT token #{token_id} with additional data to {to_address}",
                    "to_description": f"You safely received NFT token #{token_id} with additional data from {self.wallet}",
                    "action_url": None
                }
            }

            # Create transaction
            tx_details = await self.prepare_transaction(
                construct=construct,
                default_wallet=default_wallet,
                operation=operation
            )

            return tx_details
        except (InvalidAddressError, TokenNotFoundError, UnauthorizedOperatorError):
            raise
        except Exception as e:
            logger.error(f"Safe transfer with data error: {e}")
            if "execution reverted" in str(e):
                raise NFTTransferError(f"NFT safe transfer with data failed: {str(e)}")
            raise NFTTransferError(f"Failed to safely transfer NFT with data: {str(e)}")

    async def renounce_ownership(self, default_wallet: bool = True) -> Dict:
        """Renounce contract ownership"""
        try:

            try:
                owner = await self.contract_instance.functions.owner().call()
                if owner.lower() != self.wallet.lower():
                    raise OwnershipError("You are not the contract owner")
            except Exception as owner_error:
                logger.error(f"Owner check error: {owner_error}")
                raise ContractInteractionError(f"Failed to check contract ownership: {str(owner_error)}")

            # Prepare renounceOwnership functionz
            construct = self.contract_instance.functions.renounceOwnership()

            operation = {
                "type": "erc721",
                "transaction": "renounce_ownership",
                "metadata": {
                    "contract_type": "erc721",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet
                    },
                    #"action": "renounce_contract_ownership"
                },
                "notification": {
                    "title": f"Contract Ownership Renounced - {self.symbol}",
                    "from_description": f"You renounced ownership of the {self.symbol} NFT contract",
                    "to_description": f"Contract ownership was renounced by {self.wallet}",
                    "action_url": None
                }
            }

            # Create transaction Failed to get chain information
            tx_details = await self.prepare_transaction(
                construct=construct,
                default_wallet=default_wallet,
                operation=operation
            )
            return tx_details
        except OwnershipError:
            raise
        except Exception as e:
            logger.error(f"Renounce ownership error: {e}")
            raise OwnershipError(f"{str(e)}")

    async def transfer_ownership(self, new_owner_address: str, default_wallet: bool = True) -> Dict:
        """Transfer contract ownership"""
        try:
            # Validate parameters
            if not Web3.is_address(new_owner_address):
                raise InvalidAddressError(f"Invalid new owner address: {new_owner_address}")


            try:
                owner = await self.contract_instance.functions.owner().call()
                if owner.lower() != self.wallet.lower():
                    raise OwnershipError("You are not the contract owner")
            except Exception as owner_error:
                logger.error(f"Owner check error: {owner_error}")
                raise ContractInteractionError(f"Failed to check contract ownership: {str(owner_error)}")

            # Prepare transferOwnership function
            construct = self.contract_instance.functions.transferOwnership(
                self.w3.to_checksum_address(new_owner_address)
            )

            operation = {
                "type": "erc721",
                "transaction": "transfer_ownership",
                "metadata": {
                    "contract_type": "erc721",
                    "token_name": self.name,
                    "token_symbol": self.symbol,
                    "token_address": self.contract_instance.address,
                    "transaction_details": {
                        "hash": None,
                        "from_address": self.wallet,
                        "new_owner_address": new_owner_address
                    },
                    #"action": "transfer_contract_ownership"
                },
                "notification": {
                    "title": f"Contract Ownership Transfer - {self.symbol}",
                    "from_description": f"You transferred ownership of the {self.symbol} NFT contract to {new_owner_address}",
                    "to_description": f"You received ownership of the {self.symbol} NFT contract from {self.wallet}",
                    "action_url": None
                }
            }

            # Create transaction
            tx_details = await self.prepare_transaction(
                construct=construct,
                default_wallet=default_wallet,
                operation=operation
            )

            return tx_details
        except (InvalidAddressError, OwnershipError):
            raise
        except Exception as e:
            logger.error(f"Transfer ownership error: {e}")
            raise OwnershipError(f"{str(e)}")

@class_exponential_backoff()
class ERC721Interaction(NFTQueryOperations, NFTViewOperations, NFTWriteOperations):
    """Main class for ERC721 token interactions"""


    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 web3: AsyncWeb3, redis: Optional[Redis] = None):
        try:
            super().__init__(db, user_id, contract_id, web3, redis)
        except Exception as e:
            logger.error(f"Error initializing ERC721Interaction: {e}")
            handle_erc721_error(e)


    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

    # Alias constructor for backward compatibility
    async def constructor(self, construct, is_nft=None, default_wallet=True):
        """Legacy compatibility method for transaction construction"""
        return await self.prepare_transaction(
            construct=construct,
            is_nft=is_nft,
            default_wallet=default_wallet
        )