# Alembic Database Migration Guide for Atlas API

This guide explains how to use Alembic for database migrations in the Atlas API project.

## Overview

Alembic is a database migration tool for SQLAlchemy. It allows you to:
- Track database schema changes
- Upgrade and downgrade your database schema
- Automatically generate migrations based on model changes

## Prerequisites

Make sure you have Alembic installed:

```bash
pip install alembic
```

## Basic Commands

### Create a New Migration

To create a new migration after changing your models:

```bash
# Create a blank migration
python manage_migrations.py create "Description of your changes"

# Create an auto-generated migration based on model changes
python manage_migrations.py create "Description of your changes" --auto
```

### Apply Migrations

To upgrade your database to the latest version:

```bash
python manage_migrations.py upgrade
```

To upgrade to a specific version:

```bash
python manage_migrations.py upgrade <revision_id>
```

### Downgrade Migrations

To downgrade your database by one version:

```bash
python manage_migrations.py downgrade
```

To downgrade to a specific version:

```bash
python manage_migrations.py downgrade <revision_id>
```

### View Migration Information

To see the current migration version:

```bash
python manage_migrations.py current
```

To see the migration history:

```bash
python manage_migrations.py history
```

### Stamp the Database

If you need to mark the database as being at a specific revision without running migrations:

```bash
python manage_migrations.py stamp <revision_id>
```

## Working with Async SQLAlchemy

This project uses SQLAlchemy's async features, but Alembic doesn't directly support async operations. To work around this:

1. We've created a synchronous engine in `api/db/database.py` specifically for Alembic
2. The `migrations/env.py` file is configured to use this synchronous engine

## Troubleshooting

### Migration Not Detecting Changes

If Alembic isn't detecting your model changes:
- Make sure your model is imported in `migrations/env.py`
- Ensure your model inherits from `Base` defined in `api/db/database.py`
- Check that your model has `__tablename__` defined

### Migration Fails to Apply

If a migration fails to apply:
1. Check the error message for details
2. Fix any issues in your models or migration file
3. If needed, downgrade and then apply a fixed migration

## Project Structure

- `alembic.ini` - Alembic configuration file
- `migrations/` - Directory containing all migration files
  - `env.py` - Environment configuration for Alembic
  - `script.py.mako` - Template for migration files
  - `versions/` - Directory containing individual migration files
- `manage_migrations.py` - Helper script for running Alembic commands

## Best Practices

1. **Always review generated migrations** before applying them to ensure they do what you expect.
2. **Test migrations** in a development environment before applying to production.
3. **Commit migration files** to version control along with your model changes.
4. **Don't modify existing migrations** that have been applied - create new ones instead.
5. **Include meaningful messages** when creating migrations to document what changed.
6. **Use the `--auto` flag with caution** - always review the generated migrations.
7. **Keep migrations small and focused** - one migration per logical change is best.

## Common Workflows

### Adding a New Model

1. Create your model in the appropriate file
2. Import the model in `migrations/env.py`
3. Create a migration: `python manage_migrations.py create "Add new model" --auto`
4. Review the generated migration
5. Apply the migration: `python manage_migrations.py upgrade`

### Modifying an Existing Model

1. Make changes to your model
2. Create a migration: `python manage_migrations.py create "Update model fields" --auto`
3. Review the generated migration
4. Apply the migration: `python manage_migrations.py upgrade`

### Handling Complex Changes

For complex changes that Alembic might not handle correctly with autogeneration:

1. Create a blank migration: `python manage_migrations.py create "Complex schema change"`
2. Edit the migration file manually to include the necessary changes
3. Apply the migration: `python manage_migrations.py upgrade`
