import requests
#from decouple import config
from . import schemas
from api.v1.user import schemas as user_schema
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from . import models
from fastapi import HTTPException, Request
from datetime import datetime, timedelta
from .models import PaymentProvider, Payment, PaymentStatus
from ..subscription.models import user_subscription, SubscriptionPlan, SubscriptionTier
from .schemas import PaymentCreate, PaymentResponse
from ..subscription.services import Subscription
from ..subscription.schemas import UserSubscription
import stripe
import hmac
import hashlib
import json
import asyncio
from config import config


class PaymentService:

    def __init__(self) -> None:
        self.paymentMethod = PaymentMethodService()
        self.subscriptionService = Subscription()

    async def initiate_payment(self,
                        payment_data: schemas.PaymentCreate,
                        user: user_schema.User,
                        duration: schemas.Duration,
                        db: AsyncSession) -> schemas.PaymentResponse:

        plan = await self.subscriptionService.get_subscription_plan(tier=payment_data.subscription_tier, db=db)

        # Use SQLAlchemy's future select() instead of db.query
        result = await db.execute(
            select(user_subscription).join(SubscriptionPlan).filter(
                user_subscription.user_id == user.id,
                user_subscription.subscription_plan_id == plan.id,
                user_subscription.status == 'active',
                user_subscription.end_date > datetime.now(datetime.timezone.utc),
                SubscriptionPlan.tier != SubscriptionTier.BASIC
            )
        )
        active_sub = result.scalar_one_or_none()

        if active_sub:
            raise HTTPException(
                status_code=400,
                detail="User already has an active subscription"
            )

        # Get price based on duration
        if duration == schemas.Duration.MONTHLY:
            price = plan.price_monthly
            duration_days = 31
        elif duration == schemas.Duration.YEARLY:
            price = plan.price_yearly
            duration_days = 365
        else:
            raise HTTPException(
                status_code=400,
                detail="Invalid duration selected"
            )

        # Initialize payment with the appropriate provider
        if payment_data.provider == PaymentProvider.PAYSTACK:
            payment_info = await self.paymentMethod.initialize_paystack_payment(user, plan, price)
        elif payment_data.provider == PaymentProvider.STRIPE:
            payment_info = await self.paymentMethod.initialize_stripe_payment(user, plan, price)
        elif payment_data.provider == PaymentProvider.MONNIFY:
            payment_info = await self.paymentMethod.initialize_monnify_payment(user, plan, price)
        else:
            raise HTTPException(
                status_code=400,
                detail="Invalid payment provider"
            )

        payment = Payment(
            user_id=user.id,
            subscription_plan_id=plan.id,
            amount=price,
            provider=payment_data.provider,
            provider_reference=payment_info["reference"],
            status=PaymentStatus.PENDING,
            duration_days=duration_days
        )
        db.add(payment)
        await db.commit()
        return PaymentResponse(
            payment_url=payment_info["payment_url"],
            reference=payment.provider_reference,
            amount=price
        )

    async def verify_payment_and_subscribe(self,
                                   reference: str,
                                   db: AsyncSession) -> UserSubscription:
        try:
            # Use SQLAlchemy's future select() instead of db.query
            result = await db.execute(
                select(Payment).filter(Payment.provider_reference == reference)
            )
            payment = result.scalar_one_or_none()

            if not payment:
                raise HTTPException(
                    status_code=404,
                    detail="Payment not found"
                )

            # Verify payment with the appropriate provider
            if payment.provider == PaymentProvider.PAYSTACK:
                is_verified = await self.paymentMethod.verify_paystack_payment(reference)
            elif payment.provider == PaymentProvider.STRIPE:
                is_verified = await self.paymentMethod.verify_stripe_payment(reference)
            elif payment.provider == PaymentProvider.MONNIFY:
                is_verified = await self.paymentMethod.verify_monnify_payment(reference)
            else:
                is_verified = False

            if not is_verified:
                payment.status = PaymentStatus.FAILED
                await db.commit()
                raise HTTPException(
                    status_code=400,
                    detail="Payment verification failed"
                )

            # Update payment status
            payment.status = PaymentStatus.SUCCESS

            # Get subscription plan
            plan_result = await db.execute(
                select(SubscriptionPlan).filter(SubscriptionPlan.id == payment.subscription_plan_id)
            )
            plan = plan_result.scalar_one_or_none()

            # Use timezone-aware datetime
            current_time = datetime.now(datetime.timezone.utc)
            end_date = current_time + timedelta(days=payment.duration_days)

            # Create subscription
            subscription = user_subscription(
                user_id=payment.user_id,
                subscription_plan_id=plan.id,
                start_date=current_time,
                end_date=end_date,
                status='active'
            )

            db.add(subscription)
            await db.commit()
            await db.refresh(subscription)

            # Create response
            sub_success = UserSubscription(
                plan_id=subscription.subscription_plan_id,
                plan_tier=plan.tier,
                plan_name=plan.name,
                start_date=subscription.start_date,
                end_date=subscription.end_date
            )
            return sub_success

        except HTTPException:
            raise
        except Exception as e:
            await db.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"Error finding user subscription: {str(e)}"
            )





class PaymentMethodService:

    @staticmethod
    async def initialize_paystack_payment(user, plan, price):
        #PAYSTACK_SECRET_KEY = config('PAYSTACK_SECRET_KEY')
        PAYSTACK_SECRET_KEY = config.PAYSTACK_SECRET_KEY

        PAYSTACK_INIT_URL = "https://api.paystack.co/transaction/initialize"

        headers = {
            "Authorization": f"Bearer {PAYSTACK_SECRET_KEY}",
            "Content-Type": "application/json"
        }
        reference = f"PAY-{datetime.now(datetime.timezone.utc).strftime('%Y%m%d%H%M%S')}-{user.id}"

        payload = {
            "email": user.email,
            "amount": int(price * 100),  # Paystack expects amount in kobo
            "callback_url": f"http://127.0.0.1:8000/subscriptions/verify-payment/{reference}",
            "reference": reference,
            "metadata": {
                "user_id": user.id,
                "plan_id": plan.id
            }
        }

        try:
            # Use asyncio to run the synchronous requests call in a thread pool
            response = await asyncio.to_thread(requests.post, PAYSTACK_INIT_URL, json=payload, headers=headers)
            data = response.json()


            if not data.get('status'):
                raise HTTPException(
                    status_code=400,
                    detail="Failed to initialize Paystack payment"
                )

            return {
                "payment_url": data['data']['authorization_url'],
                "reference": reference
            }
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Payment initialization failed: {str(e)}"
            )

    @staticmethod
    async def verify_paystack_payment(reference: str) -> bool:
        #PAYSTACK_SECRET_KEY = config('PAYSTACK_SECRET_KEY')
        PAYSTACK_SECRET_KEY = config.PAYSTACK_SECRET_KEY

        VERIFY_URL = f"https://api.paystack.co/transaction/verify/{reference}"

        headers = {
            "Authorization": f"Bearer {PAYSTACK_SECRET_KEY}",
            "Content-Type": "application/json"
        }

        try:
            # Use asyncio to run the synchronous requests call in a thread pool
            response = await asyncio.to_thread(requests.get, VERIFY_URL, headers=headers)
            data = response.json()
            print(data)
            return data.get('status') and data.get('data', {}).get('status') == 'success'
        except Exception:
            return False

    # Stripe Implementation
    @staticmethod
    async def initialize_stripe_payment(user, plan, price):
        #stripe.api_key = config('STRIPE_SECRET_KEY')
        stripe.api_key = config.STRIPE_SECRET_KEY

        try:
            # Use asyncio to run the synchronous stripe call in a thread pool
            checkout_session = await asyncio.to_thread(
                stripe.checkout.Session.create,
                customer_email=user.email,
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': 'usd',
                        'unit_amount': int(price * 100),  # Stripe expects amount in cents
                        'product_data': {
                            'name': f'{plan.name} Subscription'
                        },
                    },
                    'quantity': 1,
                }],
                mode='payment',
                success_url=config.STRIPE_SUCCESS_URL + "?session_id={CHECKOUT_SESSION_ID}",
                cancel_url=config.STRIPE_CANCEL_URL,
                metadata={
                    'user_id': str(user.id),
                    'plan_id': str(plan.id)
                }
            )

            return {
                "payment_url": checkout_session.url,
                "reference": checkout_session.id
            }
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Stripe payment initialization failed: {str(e)}"
            )

    @staticmethod
    async def verify_stripe_payment(reference: str) -> bool:
        stripe.api_key = config.STRIPE_SECRET_KEY

        try:
            # Use asyncio to run the synchronous stripe call in a thread pool
            session = await asyncio.to_thread(stripe.checkout.Session.retrieve, reference)
            return session.payment_status == 'paid'
        except Exception:
            return False

    # Monnify Implementation
    @staticmethod
    async def initialize_monnify_payment(user, plan, price):
        #MONNIFY_API_KEY = config('MONNIFY_API_KEY')
        #MONNIFY_SECRET_KEY = config('MONNIFY_SECRET_KEY')
        #MONNIFY_BASE_URL = config('MONNIFY_BASE_URL')

        MONNIFY_API_KEY = config.MONNIFY_API_KEY
        MONNIFY_SECRET_KEY = config.MONNIFY_SECRET_KEY
        MONNIFY_BASE_URL = config.MONNIFY_BASE_URL

        # Use asyncio to run the synchronous requests call in a thread pool
        auth_response = await asyncio.to_thread(
            requests.post,
            f"{MONNIFY_BASE_URL}/api/v1/auth/login",
            auth=(MONNIFY_API_KEY, MONNIFY_SECRET_KEY)
        )

        if not auth_response.ok:
            raise HTTPException(
                status_code=500,
                detail="Failed to authenticate with Monnify"
            )

        access_token = auth_response.json()['responseBody']['accessToken']

        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }

        # Use timezone-aware datetime
        current_time = datetime.now(datetime.timezone.utc)

        payload = {
            "amount": price,
            "customerName": f"{user.first_name} {user.last_name}",
            "customerEmail": user.email,
            "paymentReference": f"SUB-{current_time.strftime('%Y%m%d%H%M%S')}-{user.id}",
            "paymentDescription": f"Subscription Payment - {plan.name}",
            "currencyCode": "NGN",
            #"contractCode": config('MONNIFY_CONTRACT_CODE'),
            #"redirectUrl": config('MONNIFY_REDIRECT_URL'),

            "contractCode": config.MONNIFY_CONTRACT_CODE,
            "redirectUrl": config.MONNIFY_REDIRECT_URL,
            "paymentMethods": ["CARD", "ACCOUNT_TRANSFER"]
        }

        try:
            # Use asyncio to run the synchronous requests call in a thread pool
            response = await asyncio.to_thread(
                requests.post,
                f"{MONNIFY_BASE_URL}/api/v1/merchant/transactions/init-transaction",
                json=payload,
                headers=headers
            )

            data = response.json()

            if data.get('requestSuccessful'):
                return {
                    "payment_url": data['responseBody']['checkoutUrl'],
                    "reference": payload['paymentReference']
                }
            else:
                raise HTTPException(
                    status_code=400,
                    detail="Failed to initialize Monnify payment"
                )
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"Monnify payment initialization failed: {str(e)}"
            )

    @staticmethod
    async def verify_monnify_payment(reference: str) -> bool:
        #MONNIFY_API_KEY = config('MONNIFY_API_KEY')
        #MONNIFY_SECRET_KEY = config('MONNIFY_SECRET_KEY')
        #MONNIFY_BASE_URL = config('MONNIFY_BASE_URL')

        MONNIFY_API_KEY = config.MONNIFY_API_KEY
        MONNIFY_SECRET_KEY = config.MONNIFY_SECRET_KEY
        MONNIFY_BASE_URL = config.MONNIFY_BASE_URL

        # Use asyncio to run the synchronous requests call in a thread pool
        auth_response = await asyncio.to_thread(
            requests.post,
            f"{MONNIFY_BASE_URL}/api/v1/auth/login",
            auth=(MONNIFY_API_KEY, MONNIFY_SECRET_KEY)
        )

        if not auth_response.ok:
            return False

        access_token = auth_response.json()['responseBody']['accessToken']

        headers = {
            "Authorization": f"Bearer {access_token}"
        }

        try:
            # Use asyncio to run the synchronous requests call in a thread pool
            response = await asyncio.to_thread(
                requests.get,
                f"{MONNIFY_BASE_URL}/api/v2/transactions/{reference}",
                headers=headers
            )

            data = response.json()
            return data.get('requestSuccessful') and data.get('responseBody', {}).get('paymentStatus') == 'PAID'
        except Exception:
            return False








# Webhook handlers
async def handle_paystack_webhook(request: Request, db: AsyncSession):
    #PAYSTACK_SECRET_KEY = config('PAYSTACK_SECRET_KEY')
    PAYSTACK_SECRET_KEY = config.PAYSTACK_SECRET_KEY
    payload = await request.json()

    signature = request.headers.get("x-paystack-signature")
    computed_hmac = hmac.new(
        PAYSTACK_SECRET_KEY.encode('utf-8'),
        str(payload).encode('utf-8'),
        hashlib.sha512
    ).hexdigest()

    if signature != computed_hmac:
        raise HTTPException(status_code=400, detail="Invalid signature")

    if payload.get('event') == 'charge.success':
        reference = payload['data']['reference']
        # Call verify_payment_and_subscribe
        paymentService = PaymentService()
        await paymentService.verify_payment_and_subscribe(reference, db)

    return {"status": "success"}


async def handle_stripe_webhook(request: Request, db: AsyncSession):
    #STRIPE_WEBHOOK_SECRET = config('STRIPE_WEBHOOK_SECRET')
    STRIPE_WEBHOOK_SECRET = config.STRIPE_WEBHOOK_SECRET

    payload = await request.body()
    sig_header = request.headers.get('stripe-signature')

    try:
        # Use asyncio to run the synchronous stripe call in a thread pool
        event = await asyncio.to_thread(
            stripe.Webhook.construct_event,
            payload, sig_header, STRIPE_WEBHOOK_SECRET
        )
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except stripe.error.SignatureVerificationError as e:
        raise HTTPException(status_code=400, detail=str(e))

    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        reference = session.id

        paymentService = PaymentService()
        await paymentService.verify_payment_and_subscribe(reference, db)

    return {"status": "success"}



async def handle_monnify_webhook(request: Request, db: AsyncSession):
    #MONNIFY_SECRET_KEY = config('MONNIFY_SECRET_KEY')
    MONNIFY_SECRET_KEY = config.MONNIFY_SECRET_KEY
    payload = await request.json()

    signature = request.headers.get("monnify-signature")
    computed_hash = hashlib.sha512(
        f"{MONNIFY_SECRET_KEY}{json.dumps(payload)}".encode()
    ).hexdigest()

    if signature != computed_hash:
        raise HTTPException(status_code=400, detail="Invalid signature")

    if payload.get('eventType') == 'SUCCESSFUL_TRANSACTION':
        reference = payload['eventData']['paymentReference']

        paymentService = PaymentService()
        await paymentService.verify_payment_and_subscribe(reference, db)


    return {"status": "success"}