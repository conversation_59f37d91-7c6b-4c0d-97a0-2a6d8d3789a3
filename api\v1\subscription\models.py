from datetime import datetime, date, timezone
from api.db.database import Base
import passlib.hash as _hash
from enum import Enum
from sqlalchemy import Column, BIGINT, String, Text, Boolean, DateTime, ForeignKey, Integer, JSON, Enum as SQLEnum
from sqlalchemy.orm import relationship

class SubscriptionTier(str, Enum):
    BASIC = "BASIC"
    PROFESSIONAL = "PROFESSIONAL"
    ENTERPRISE = "ENTERPRISE"


class SubscriptionPlan(Base):
    __tablename__ = "subscription_plan"

    id = Column(BIGINT, primary_key=True, index=True, autoincrement=True, nullable=False)
    tier = Column(SQLEnum(SubscriptionTier), nullable=False, default=SubscriptionTier.BASIC)
    name = Column(String(255), nullable=False, unique=True, index=True)
    description = Column(Text, nullable=True)

    # Separate prices for different durations
    price_monthly = Column(BIGINT, nullable=False, default=0)
    price_yearly = Column(BIGINT, nullable=False, default=0)

    # API limits and permissions remain the same
    api_rate_limit = Column(Integer, nullable=False)
    daily_request_limit = Column(Integer, nullable=False)
    monthly_request_limit = Column(Integer, nullable=False)
    allowed_endpoints = Column(JSON, nullable=False)
    max_payload_size = Column(Integer, nullable=False)

    # Rest of the columns remain the same
    has_priority_support = Column(Boolean, default=False)
    has_advanced_features = Column(Boolean, default=False)
    concurrent_requests = Column(Integer, nullable=False)
    max_response_time = Column(Integer, nullable=False)

    created_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    api_keys = relationship("APIKey", back_populates="subscription_plan")

class user_subscription(Base):
    __tablename__ = "user_subscription"

    id = Column(BIGINT, primary_key=True, index=True, autoincrement=True, nullable=False)
    user_id = Column(BIGINT, ForeignKey('user.id'), index=True, nullable=False)
    subscription_plan_id = Column(BIGINT, ForeignKey('subscription_plan.id'), index=True, nullable=False)
    start_date = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    end_date = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    status = Column(String(255), nullable=False, default='active')
    created_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))