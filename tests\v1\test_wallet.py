import pytest
import pytest_asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from httpx import AsyncClient
from web3 import Web3, AsyncWeb3
import asyncio
import io

from api.v1.wallet.models import WalletAddress
from api.v1.wallet.services import WalletManager
from api.v1.wallet.schemas import WalletAddressResponse
from api.core.logging_config import get_logger

logger = get_logger(__name__)

# Test constants
TEST_WALLET_ADDRESS = "******************************************"
TEST_INVALID_WALLET_ADDRESS = "0xInvalidAddress"
TEST_BALANCE_WEI = Web3.to_wei(1, 'ether')  # 1 MATIC
TEST_BALANCE_MATIC = 1.0

class MockRedis:
    """Mock Redis for testing"""
    def __init__(self):
        self.data = {}

    async def get(self, key):
        return self.data.get(key)

    async def set(self, key, value, ex=None):
        self.data[key] = value
        return True

    async def delete(self, key):
        if key in self.data:
            del self.data[key]
        return True

    async def expire(self, key, time):
        return True

@pytest_asyncio.fixture
async def mock_redis():
    return MockRedis()

@pytest_asyncio.fixture
async def mock_web3():
    """Mock Web3 for testing"""
    mock = AsyncMock()
    mock.eth = AsyncMock()
    mock.eth.chain_id = 137  # Polygon Mainnet
    mock.eth.get_balance = AsyncMock(return_value=TEST_BALANCE_WEI)
    mock.from_wei = MagicMock(return_value=TEST_BALANCE_MATIC)
    mock.to_checksum_address = MagicMock(lambda addr: addr if addr == TEST_WALLET_ADDRESS else ValueError("Invalid address"))
    
    # Mock for transaction history
    mock.eth.get_transaction_count = AsyncMock(return_value=10)
    mock.eth.get_block = AsyncMock(return_value={
        'timestamp': int(datetime.now().timestamp()),
        'number': 1000000
    })
    
    # Mock for transaction details
    mock_transaction = {
        'hash': '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
        'from': TEST_WALLET_ADDRESS,
        'to': '0x0987654321fedcba0987654321fedcba0987654321',
        'value': Web3.to_wei(0.1, 'ether'),
        'gas': 21000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 5,
        'blockHash': '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890',
        'blockNumber': 1000000,
        'transactionIndex': 0
    }
    mock.eth.get_transaction = AsyncMock(return_value=mock_transaction)
    
    return mock

@pytest_asyncio.fixture
async def test_wallet(async_db, test_verified_user):
    """Create a test wallet address for a user"""
    wallet = WalletAddress(
        user_id=test_verified_user["id"],
        address=TEST_WALLET_ADDRESS,
        chain_id=137,  # Polygon Mainnet
        is_verified=True,
        created_at=datetime.now(timezone.utc),
        last_verified=datetime.now(timezone.utc)
    )
    async_db.add(wallet)
    await async_db.commit()
    await async_db.refresh(wallet)
    return wallet

@pytest.mark.asyncio
async def test_connect_wallet(client, test_verified_user, async_db, mock_web3):
    """Test connecting a wallet address to a user account"""
    with patch('api.v1.wallet.services.web3', mock_web3), \
         patch('api.v1.wallet.services.asyncio.to_thread') as mock_to_thread:
        
        # Mock asyncio.to_thread to return the balance values directly
        mock_to_thread.side_effect = [TEST_BALANCE_WEI, TEST_BALANCE_MATIC]
        
        response = await client.post(
            "/wallet/connect",
            params={"wallet_address": TEST_WALLET_ADDRESS},
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "user_id" in data
        assert "address" in data
        assert "balance" in data
        assert data["address"] == TEST_WALLET_ADDRESS
        assert data["user_id"] == test_verified_user["id"]
        assert data["balance"] == TEST_BALANCE_MATIC
        
        # Verify the wallet was created in the database
        result = await async_db.execute(
            select(WalletAddress).filter(
                WalletAddress.user_id == test_verified_user["id"],
                WalletAddress.address == TEST_WALLET_ADDRESS
            )
        )
        wallet = result.scalar_one_or_none()
        assert wallet is not None
        assert wallet.address == TEST_WALLET_ADDRESS

@pytest.mark.asyncio
async def test_connect_invalid_wallet(client, test_verified_user, mock_web3):
    """Test connecting an invalid wallet address"""
    with patch('api.v1.wallet.services.web3', mock_web3):
        response = await client.post(
            "/wallet/connect",
            params={"wallet_address": TEST_INVALID_WALLET_ADDRESS},
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "Invalid wallet address" in data["detail"]

@pytest.mark.asyncio
async def test_connect_duplicate_wallet(client, test_verified_user, test_wallet, mock_web3):
    """Test connecting a wallet address that already exists"""
    with patch('api.v1.wallet.services.web3', mock_web3):
        response = await client.post(
            "/wallet/connect",
            params={"wallet_address": TEST_WALLET_ADDRESS},
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "detail" in data
        assert "already exists" in data["detail"]

@pytest.mark.asyncio
async def test_get_wallet_details(client, test_verified_user, test_wallet, mock_web3):
    """Test getting wallet details"""
    with patch('api.v1.wallet.services.web3', mock_web3), \
         patch('api.v1.wallet.services.asyncio.to_thread') as mock_to_thread:
        
        # Mock asyncio.to_thread to return the balance values directly
        mock_to_thread.side_effect = [TEST_BALANCE_WEI, TEST_BALANCE_MATIC]
        
        response = await client.post(
            "/wallet/details",
            params={"id": test_wallet.id},
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "id" in data
        assert "user_id" in data
        assert "address" in data
        assert "balance" in data
        assert data["address"] == TEST_WALLET_ADDRESS
        assert data["user_id"] == test_verified_user["id"]
        assert data["balance"] == TEST_BALANCE_MATIC

@pytest.mark.asyncio
async def test_get_nonexistent_wallet(client, test_verified_user, mock_web3):
    """Test getting details for a wallet that doesn't exist"""
    with patch('api.v1.wallet.services.web3', mock_web3):
        response = await client.post(
            "/wallet/details",
            params={"id": 9999},  # Non-existent wallet ID
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "Wallet not found" in data["detail"]

@pytest.mark.asyncio
async def test_get_all_user_wallets(client, test_verified_user, test_wallet, async_db, mock_web3):
    """Test getting all wallets for a user"""
    # Add a second wallet for the user
    second_wallet = WalletAddress(
        user_id=test_verified_user["id"],
        address="******************************************",
        chain_id=137,  # Polygon Mainnet
        is_verified=True,
        created_at=datetime.now(timezone.utc),
        last_verified=datetime.now(timezone.utc)
    )
    async_db.add(second_wallet)
    await async_db.commit()
    await async_db.refresh(second_wallet)
    
    with patch('api.v1.wallet.services.web3', mock_web3):
        response = await client.get(
            "/wallet/get",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) == 2
        
        # Verify both wallets are in the response
        wallet_addresses = [wallet["address"] for wallet in data]
        assert TEST_WALLET_ADDRESS in wallet_addresses
        assert "******************************************" in wallet_addresses

@pytest.mark.asyncio
async def test_user_with_no_wallets(client, test_verified_user, mock_web3):
    """Test getting wallets for a user with no wallets"""
    with patch('api.v1.wallet.services.web3', mock_web3):
        response = await client.get(
            "/wallet/get",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "No wallets found for this user" in data["detail"]

@pytest.mark.asyncio
async def test_wallet_manager_validate_address():
    """Test the WalletManager.validate_wallet_address method"""
    # Valid address
    assert WalletManager.validate_wallet_address(TEST_WALLET_ADDRESS) is True
    
    # Invalid address
    with patch('api.v1.wallet.services.web3.to_checksum_address', side_effect=ValueError("Invalid address")):
        assert WalletManager.validate_wallet_address(TEST_INVALID_WALLET_ADDRESS) is False
    
    # Empty address
    assert WalletManager.validate_wallet_address("") is False
    assert WalletManager.validate_wallet_address(None) is False

@pytest.mark.asyncio
async def test_wallet_manager_create_address(async_db, test_verified_user, mock_web3):
    """Test the WalletManager.create_wallet_address method"""
    from api.v1.user.schemas import User
    
    with patch('api.v1.wallet.services.web3', mock_web3):
        wallet_manager = WalletManager()
        
        # Create a user object from the test_verified_user fixture
        user = User(id=test_verified_user["id"], email=test_verified_user["email"])
        
        # Create a wallet address
        wallet = await wallet_manager.create_wallet_address(
            user=user,
            wallet_address=TEST_WALLET_ADDRESS,
            db=async_db
        )
        
        assert wallet is not None
        assert wallet.user_id == test_verified_user["id"]
        assert wallet.address == TEST_WALLET_ADDRESS
        
        # Verify the wallet was created in the database
        result = await async_db.execute(
            select(WalletAddress).filter(
                WalletAddress.user_id == test_verified_user["id"],
                WalletAddress.address == TEST_WALLET_ADDRESS
            )
        )
        db_wallet = result.scalar_one_or_none()
        assert db_wallet is not None
        assert db_wallet.id == wallet.id
        assert db_wallet.address == TEST_WALLET_ADDRESS

@pytest.mark.asyncio
async def test_wallet_manager_get_details(mock_web3):
    """Test the WalletManager.get_wallet_details method"""
    with patch('api.v1.wallet.services.web3', mock_web3), \
         patch('api.v1.wallet.services.asyncio.to_thread') as mock_to_thread:
        
        # Mock asyncio.to_thread to return the balance values directly
        mock_to_thread.side_effect = [TEST_BALANCE_WEI, TEST_BALANCE_MATIC]
        
        wallet_manager = WalletManager()
        wallet_details = await wallet_manager.get_wallet_details(TEST_WALLET_ADDRESS)
        
        assert wallet_details is not None
        assert "balance" in wallet_details
        assert "matic" in wallet_details["balance"]
        assert wallet_details["balance"]["matic"] == TEST_BALANCE_MATIC

@pytest.mark.asyncio
async def test_wallet_manager_get_details_invalid_address(mock_web3):
    """Test the WalletManager.get_wallet_details method with an invalid address"""
    with patch('api.v1.wallet.services.web3', mock_web3):
        wallet_manager = WalletManager()
        
        # Test with invalid address
        with pytest.raises(ValueError, match="Invalid wallet address"):
            await wallet_manager.get_wallet_details(TEST_INVALID_WALLET_ADDRESS)

@pytest.mark.asyncio
async def test_wallet_manager_get_details_network_error(mock_web3):
    """Test the WalletManager.get_wallet_details method with a network error"""
    with patch('api.v1.wallet.services.web3', mock_web3), \
         patch('api.v1.wallet.services.asyncio.to_thread') as mock_to_thread:
        
        # Mock asyncio.to_thread to raise an exception
        mock_to_thread.side_effect = Exception("Network error")
        
        wallet_manager = WalletManager()
        wallet_details = await wallet_manager.get_wallet_details(TEST_WALLET_ADDRESS)
        
        assert wallet_details is not None
        assert "balance" in wallet_details
        assert "matic" in wallet_details["balance"]
        assert wallet_details["balance"]["matic"] is None  # Balance should be None on error
