from typing import Dict, Any, List, Optional
from datetime import datetime
import json
import aiofiles
from api.core.logging_config import get_logger
from config import config
import os

logger = get_logger(__name__)

class FeedbackManager:
    """Manages user feedback and issue reporting"""
    
    def __init__(self):
        self.feedback_dir = "feedback"
        self.ensure_feedback_directory()
        
    def ensure_feedback_directory(self):
        """Ensure feedback directory exists"""
        if not os.path.exists(self.feedback_dir):
            os.makedirs(self.feedback_dir)
            
    async def submit_feedback(self, user_id: int, feedback_type: str,
                            content: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Submit user feedback"""
        try:
            timestamp = datetime.now().isoformat()
            feedback_data = {
                "user_id": user_id,
                "feedback_type": feedback_type,
                "content": content,
                "metadata": metadata or {},
                "timestamp": timestamp,
                "status": "new"
            }
            
            filename = f"{self.feedback_dir}/feedback_{timestamp}.json"
            async with aiofiles.open(filename, 'w') as f:
                await f.write(json.dumps(feedback_data, indent=2))
                
            logger.info(f"Feedback submitted successfully: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to submit feedback: {str(e)}")
            return False
            
    async def report_issue(self, user_id: int, issue_type: str,
                          description: str, severity: str,
                          steps_to_reproduce: Optional[str] = None,
                          metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Report an issue"""
        try:
            timestamp = datetime.now().isoformat()
            issue_data = {
                "user_id": user_id,
                "issue_type": issue_type,
                "description": description,
                "severity": severity,
                "steps_to_reproduce": steps_to_reproduce,
                "metadata": metadata or {},
                "timestamp": timestamp,
                "status": "new"
            }
            
            filename = f"{self.feedback_dir}/issue_{timestamp}.json"
            async with aiofiles.open(filename, 'w') as f:
                await f.write(json.dumps(issue_data, indent=2))
                
            logger.info(f"Issue reported successfully: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to report issue: {str(e)}")
            return False
            
    async def update_feedback_status(self, feedback_id: str,
                                   new_status: str,
                                   admin_notes: Optional[str] = None) -> bool:
        """Update feedback status"""
        try:
            feedback_file = f"{self.feedback_dir}/feedback_{feedback_id}.json"
            if not os.path.exists(feedback_file):
                return False
                
            async with aiofiles.open(feedback_file, 'r') as f:
                content = await f.read()
                feedback_data = json.loads(content)
                
            feedback_data["status"] = new_status
            if admin_notes:
                feedback_data["admin_notes"] = admin_notes
                
            async with aiofiles.open(feedback_file, 'w') as f:
                await f.write(json.dumps(feedback_data, indent=2))
                
            logger.info(f"Feedback status updated: {feedback_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update feedback status: {str(e)}")
            return False
            
    async def update_issue_status(self, issue_id: str,
                                new_status: str,
                                admin_notes: Optional[str] = None) -> bool:
        """Update issue status"""
        try:
            issue_file = f"{self.feedback_dir}/issue_{issue_id}.json"
            if not os.path.exists(issue_file):
                return False
                
            async with aiofiles.open(issue_file, 'r') as f:
                content = await f.read()
                issue_data = json.loads(content)
                
            issue_data["status"] = new_status
            if admin_notes:
                issue_data["admin_notes"] = admin_notes
                
            async with aiofiles.open(issue_file, 'w') as f:
                await f.write(json.dumps(issue_data, indent=2))
                
            logger.info(f"Issue status updated: {issue_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update issue status: {str(e)}")
            return False
            
    async def get_feedback_list(self, status: Optional[str] = None,
                              feedback_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of feedback entries"""
        try:
            feedback_list = []
            for filename in os.listdir(self.feedback_dir):
                if not filename.startswith('feedback_'):
                    continue
                    
                filepath = os.path.join(self.feedback_dir, filename)
                async with aiofiles.open(filepath, 'r') as f:
                    content = await f.read()
                    feedback_data = json.loads(content)
                    
                if status and feedback_data["status"] != status:
                    continue
                if feedback_type and feedback_data["feedback_type"] != feedback_type:
                    continue
                    
                feedback_list.append(feedback_data)
                
            return sorted(feedback_list, key=lambda x: x["timestamp"], reverse=True)
            
        except Exception as e:
            logger.error(f"Failed to get feedback list: {str(e)}")
            return []
            
    async def get_issue_list(self, status: Optional[str] = None,
                           severity: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get list of reported issues"""
        try:
            issue_list = []
            for filename in os.listdir(self.feedback_dir):
                if not filename.startswith('issue_'):
                    continue
                    
                filepath = os.path.join(self.feedback_dir, filename)
                async with aiofiles.open(filepath, 'r') as f:
                    content = await f.read()
                    issue_data = json.loads(content)
                    
                if status and issue_data["status"] != status:
                    continue
                if severity and issue_data["severity"] != severity:
                    continue
                    
                issue_list.append(issue_data)
                
            return sorted(issue_list, key=lambda x: x["timestamp"], reverse=True)
            
        except Exception as e:
            logger.error(f"Failed to get issue list: {str(e)}")
            return []
            
    async def get_user_feedback(self, user_id: int) -> List[Dict[str, Any]]:
        """Get feedback submitted by a specific user"""
        try:
            feedback_list = []
            for filename in os.listdir(self.feedback_dir):
                if not filename.startswith('feedback_'):
                    continue
                    
                filepath = os.path.join(self.feedback_dir, filename)
                async with aiofiles.open(filepath, 'r') as f:
                    content = await f.read()
                    feedback_data = json.loads(content)
                    
                if feedback_data["user_id"] == user_id:
                    feedback_list.append(feedback_data)
                    
            return sorted(feedback_list, key=lambda x: x["timestamp"], reverse=True)
            
        except Exception as e:
            logger.error(f"Failed to get user feedback: {str(e)}")
            return []
            
    async def get_user_issues(self, user_id: int) -> List[Dict[str, Any]]:
        """Get issues reported by a specific user"""
        try:
            issue_list = []
            for filename in os.listdir(self.feedback_dir):
                if not filename.startswith('issue_'):
                    continue
                    
                filepath = os.path.join(self.feedback_dir, filename)
                async with aiofiles.open(filepath, 'r') as f:
                    content = await f.read()
                    issue_data = json.loads(content)
                    
                if issue_data["user_id"] == user_id:
                    issue_list.append(issue_data)
                    
            return sorted(issue_list, key=lambda x: x["timestamp"], reverse=True)
            
        except Exception as e:
            logger.error(f"Failed to get user issues: {str(e)}")
            return [] 