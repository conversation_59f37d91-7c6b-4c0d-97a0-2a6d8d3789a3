from web3 import AsyncWeb3
from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
from datetime import datetime
import time  
import math
import asyncio
from config import config
from fastapi import HTTPException, status
from api.v1.deploy_contracts.models import ContractDeployment
from sqlalchemy.orm import Session
import json
from api.v1.account.models import Account as AccountWallet
from api.core.blockchain_dep import class_exponential_backoff
from .exceptions import (logger, DataProcessingError, 
                         BlockchainError, CacheError)
from redis.asyncio import Redis

PROVIDER = config.PROVIDER
PRODUCTION = config.PRODUCTION
PLATFORM_WALLET = config.PLATFORM_WALLET
CACHE_TIME = 600
CHUNCK_SIZE = 50



class ChartProcessorBase:
    """Base class for chart processing functionality"""
    
    def __init__(self, db: Session, user_id: int, web3: AsyncWeb3, redis: Optional[Redis] = None):
        self.db = db
        self.user_id = user_id
        self.redis = redis
        self.cache_time = CACHE_TIME
        self.chunk_size = CHUNCK_SIZE
        self.web3 = web3
        
        self._initialize_contracts()
        self._initialize_wallet()

    def _initialize_contracts(self) -> None:
        """Initialize contract details"""
        self.contracts = self.db.query(ContractDeployment).filter(
            ContractDeployment.user_id == self.user_id
        ).all()

        if not self.contracts:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, 
                detail="Contract not found"
            )

    def _initialize_wallet(self) -> None:
        """Initialize wallet details"""
        if PRODUCTION:
            account = self.db.query(AccountWallet).filter(
                AccountWallet.user_id == self.user_id
            ).first()

            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User does not have a vault"
                )
            self.wallet = account.address
        else:
            self.wallet = PLATFORM_WALLET

    async def _get_cached_or_fetch_data(self, cache_key: str, processor) -> List[Dict[str, Any]]:
        """Get data from cache or fetch from blockchain"""
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

            transactions_data = []
            all_results = await asyncio.gather(
                *[self._fetch_logs(contract, processor) for contract in self.contracts],
                return_exceptions=True
            )

            for result in all_results:
                if isinstance(result, Exception):
                    logger.error(f"Error in contract processing: {str(result)}")
                    continue
                if result:
                    transactions_data.extend([event for event in result if event])

            if not transactions_data:
                logger.warning("No transaction data retrieved from any contract")
                return []

            try:
                await self.redis.set(cache_key, json.dumps(transactions_data))
                await self.redis.expire(cache_key, self.cache_time)
            except Exception as e:
                logger.error(f"Failed to cache transaction data: {str(e)}")

            return transactions_data

        except json.JSONDecodeError as e:
            logger.error(f"Cache data corruption detected: {str(e)}")
            await self.redis.delete(cache_key)
            raise CacheError("Corrupted cache data detected and cleared")
        except Exception as e:
            raise BlockchainError(f"Failed to retrieve contract data: {str(e)}")

    async def _fetch_logs(self, contract, processor) -> Optional[List[Dict[str, Any]]]:
        """Fetch and process contract logs in chunks"""
        try:
            logs = await self._get_contract_logs(contract)
            
            # Process logs with semaphore for controlled concurrency
            semaphore = asyncio.Semaphore(self.chunk_size)
            
            async def process_with_semaphore(log):
                async with semaphore:
                    return await processor(log)

            results = await asyncio.gather(
                *[process_with_semaphore(log) for log in logs],
                return_exceptions=True
            )

            valid_results = [
                result for result in results 
                if result and not isinstance(result, Exception)
            ]

            if len(valid_results) < len(logs):
                logger.warning(
                    f"Some logs failed processing: "
                    f"{len(logs) - len(valid_results)} failures"
                )

            return valid_results if valid_results else None

        except Exception as e:
            logger.error(f"Failed to fetch history for contract: {str(e)}")
            raise BlockchainError(f"Contract history fetch failed: {str(e)}")

    async def _get_contract_logs(self, contract) -> List[Dict]:
        """Get contract logs from blockchain"""
        end_block = await self.web3.eth.get_block_number()
        start_block = contract.block_number or (await self.web3.eth.get_block('earliest'))['number']
        
        filter_params = {
            'fromBlock': start_block,
            'toBlock': end_block,
            'address': contract.contract_address
        }
        
        return await self.web3.eth.get_logs(filter_params)

    def _deployment_time(self, freq: str) -> pd.Series:
        """Calculate deployment statistics"""
        try:
            deployment_time = [contract.created_at for contract in self.contracts]
            dep_df = pd.DataFrame({'timestamp': deployment_time})
            dep_df['timestamp'] = pd.to_datetime(dep_df['timestamp'])
            dep_df = dep_df.set_index('timestamp')
            deployed_stats = dep_df.resample(freq).size().fillna(0)
            deployed_stats.name = 'contracts_deployed'
            return deployed_stats
        except Exception as e:
            logger.error(f"Error calculating deployment time: {str(e)}")
            return pd.Series(dtype=float)

class EarningsProcessor(ChartProcessorBase):
    """Handles earnings chart processing"""

    async def earnings_chart(self, freq: str) -> Dict:
        """Generate earnings chart data"""
        start_time = time.time()
        try:
            cache_key = f"earning_chart:{self.contracts[0].contract_address}{self.user_id}"
            transactions_data = await self._get_cached_or_fetch_data(
                cache_key, 
                self._process_earning
            )
            monthly_stats = await self._get_earning_dataframe(transactions_data, freq)
            frontend_data = self._prepare_earning_data(monthly_stats)

            logger.info(
                f"Earnings chart generated successfully. "
                f"Execution time: {time.time() - start_time:.2f} seconds"
            )

            return {
                'status': 'success',
                'data': frontend_data
            }
        except Exception as e:
            self._handle_chart_error(e)

    async def _process_earning(self, log: Dict) -> Optional[Dict]:
        """Process earning log entry"""
        try:
            block, tx, balance, receipt = await asyncio.gather(
                self.web3.eth.get_block(log['blockNumber']),
                self.web3.eth.get_transaction(log['transactionHash']),
                self.web3.eth.get_balance(self.wallet, log['blockNumber']),
                self.web3.eth.get_transaction_receipt(log['transactionHash'])
            )

            balance = self.web3.from_wei(balance, 'ether')
            fee_in_wei = receipt.gasUsed * tx.gasPrice
            fee_in_matic = self.web3.from_wei(fee_in_wei, 'ether')

            return {
                'timestamp': datetime.fromtimestamp(block['timestamp']).isoformat(),
                'balance': float(balance),
                'network_earning': float(fee_in_matic)
            }
        except Exception as e:
            logger.error(f"Error processing earning log: {str(e)}")
            return None

    async def _get_earning_dataframe(self, transactions_data: List[Dict], freq: str) -> pd.DataFrame:
        """Process earnings data into DataFrame"""
        try:
            if not transactions_data:
                return pd.DataFrame()

            df = pd.DataFrame(transactions_data)
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_localize('UTC')
            df = df.set_index('timestamp')

            deployed_stats = self._deployment_time(freq)

            stats = pd.DataFrame({
                'wallet_balance': df['balance'].resample(freq).last().fillna(method='ffill').fillna(0),
                'network_earnings': df['network_earning'].resample(freq).sum().fillna(0),
                'contracts_deployed': deployed_stats.astype('int32')
            })

            stats.ffill(inplace=True)
            return stats
        except Exception as e:
            logger.error(f"Failed to process earnings data: {str(e)}")
            raise DataProcessingError(f"Failed to process earnings data: {str(e)}")

    def _prepare_earning_data(self, time_series_stats: pd.DataFrame) -> List[Dict]:
        """Prepare earnings data for frontend"""
        try:
            return [{
                'timestamp': index.isoformat(),
                'networkEarnings': float(row['network_earnings']) if pd.notna(row['network_earnings']) else 0.0,
                'contractsDeployed': int(row['contracts_deployed']),
                'walletBalance': float(row['wallet_balance']) if pd.notna(row['wallet_balance']) else 0.0
            } for index, row in time_series_stats.iterrows()][::-1]
        except Exception as e:
            logger.error(f"Failed to prepare earnings data: {str(e)}")
            raise DataProcessingError(f"Failed to prepare earnings data: {str(e)}")

class HistoryProcessor(ChartProcessorBase):
    """Handles history chart processing"""

    async def history_chart(self, freq: str) -> Dict:
        """Generate history chart data"""
        start_time = time.time()
        try:
            cache_key = f"history_chart:{self.contracts[0].contract_address}{self.user_id}"
            transactions_data = await self._get_cached_or_fetch_data(
                cache_key, 
                self._process_history
            )
            monthly_stats, total_transactions, weekly_metrics = await self._get_history_dataframe(
                transactions_data, 
                freq
            )
            frontend_data = self._prepare_history_data(monthly_stats)

            logger.info(
                f"History chart generated successfully. "
                f"Execution time: {time.time() - start_time:.2f} seconds"
            )

            return {
                'status': 'success',
                'data': frontend_data,
                'overall_totals': {
                    'totalTransactions': {
                        'total': total_transactions,
                        'weekly_rate_change': weekly_metrics['trx_rate_change'],
                    },
                    'totalContractsDeployed': {
                        'weekly_rate_change': weekly_metrics['contracts_rate_change'],
                    }
                }
            }
        except Exception as e:
            self._handle_chart_error(e)

    async def _process_history(self, log: Dict) -> Optional[Dict]:
        """Process history log entry"""
        try:
            block = await self.web3.eth.get_block(log['blockNumber'])
            return {
                'timestamp': datetime.fromtimestamp(block['timestamp']).isoformat(),
                'transactionHash': log['transactionHash'].hex()
            }
        except Exception as e:
            logger.error(f"Error processing history log: {str(e)}")
            return None

    async def _get_history_dataframe(self, transaction_data, freq: str) -> Tuple[pd.DataFrame, int, Dict]:
        """Process history data into DataFrame"""
        try:
            if not transaction_data:
                return pd.DataFrame(), 0, {'trx_rate_change': 0, 'contracts_rate_change': 0}

            df = pd.DataFrame(transaction_data)
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_localize('UTC')
            df.set_index('timestamp', inplace=True)

            def calculate_period_stats(period_freq: str) -> pd.DataFrame:
                deployed_stats = self._deployment_time(period_freq)
                return pd.DataFrame({
                    'transactions': df['transactionHash'].resample(period_freq).nunique().fillna(0),
                    'contracts_deployed': deployed_stats.astype('int32').fillna(0)
                })

            stats = calculate_period_stats(freq)
            weekly_stats = calculate_period_stats('W')
            stats.fillna(0, inplace=True)

            weekly_changes = weekly_stats.pct_change() * 100
            weekly_changes.fillna(0, inplace=True)

            latest_changes = weekly_changes.iloc[-1] if not weekly_changes.empty else pd.Series({
                'transactions': 0, 'contracts_deployed': 0
            })

            total_transactions = int(df['transactionHash'].nunique())

            weekly_metrics = {
                'trx_rate_change': round(float(latest_changes['transactions']), 2),
                'contracts_rate_change': round(float(latest_changes['contracts_deployed']), 2),
            }

            return stats, total_transactions, weekly_metrics
        except Exception as e:
            logger.error(f"Failed to process history data: {str(e)}")
            raise DataProcessingError(f"Failed to process history data: {str(e)}")

    def _prepare_history_data(self, time_series_stats) -> List[Dict]:
        """Prepare history data for frontend"""
        try:
            return [{
                'timestamp': index.isoformat(),
                'uniqueContracts': int(row['contracts_deployed']) if pd.notna(row['contracts_deployed']) else 0,
                'transactions': int(row['transactions']) if pd.notna(row['transactions']) else 0
            } for index, row in time_series_stats.iterrows()][::-1]
        except Exception as e:
            logger.error(f"Failed to prepare history data: {str(e)}")
            raise DataProcessingError(f"Failed to prepare history data: {str(e)}")

@class_exponential_backoff()
class GeneralChart(EarningsProcessor, HistoryProcessor):
    """Main class combining earnings and history processing"""
    
    def __init__(self, db: Session, user_id: int):
        super().__init__(db, user_id)

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

    def _handle_chart_error(self, error: Exception) -> None:
        """Handle chart processing errors"""
        if isinstance(error, CacheError):
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Cache service temporarily unavailable"
            )
        elif isinstance(error, BlockchainError):
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="Failed to interact with blockchain"
            )
        elif isinstance(error, DataProcessingError):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to process contract data"
            )