# Production-Level Logging System

This document outlines the improved logging system implemented in the Atlas API.

## Features

- **Centralized Configuration**: Single source of truth for logging settings in `api/core/logging_config.py`
- **Environment-Based Settings**: Different logging levels and handlers for development and production
- **Rotating File Logs**: Prevents log files from growing too large
- **Separate Error Logs**: Critical and error logs are separated for easier monitoring
- **JSON Formatting**: Production logs use JSON format for easier parsing by log management tools
- **Context Enrichment**: Add additional context to logs easily
- **Performance Monitoring**: Track function execution time
- **Exception Tracking**: Automatically log exceptions with full tracebacks
- **Thread & Process Info**: Identify which process/thread generated a log
- **Smart Debug Logging**: Debug logs are handled differently in development and production

## How to Use

### Basic Logging

```python
from api.core.logging_config import get_logger

# Get a logger for your module
logger = get_logger(__name__)

# Use standard logging methods
logger.debug("Debugging info")  # Only saved in development
logger.info("Information message")
logger.warning("Warning message")
logger.error("Error message")
logger.critical("Critical error message")
```

### Adding Context to Logs

```python
from api.core.logging_config import get_logger, LogContext

logger = get_logger(__name__)

# Add user_id and action to all logs in this context
with LogContext(logger, user_id="123", action="login"):
    logger.info("User login attempt")
    # Log will include user_id and action fields automatically
```

### Performance Monitoring

```python
from api.core.logging_config import get_logger, log_execution_time

logger = get_logger(__name__)

@log_execution_time(logger)
def slow_function():
    # Function implementation
    pass
```

### Exception Handling

```python
from api.core.logging_config import get_logger, log_exceptions

logger = get_logger(__name__)

@log_exceptions(logger)
def risky_function():
    # Function that might raise an exception
    pass
```

## Log Files

The logging system creates different log files based on the environment:

### Development Environment
- **Main Log**: `logs/app.log` - Contains all logs (including debug)
- **Error Log**: `logs/error.log` - Contains only ERROR and CRITICAL logs
- **Debug Log**: `logs/debug.log` - Contains only DEBUG level logs

### Production Environment
- **Main Log**: `logs/app.log` - Contains INFO and above logs (no debug)
- **Error Log**: `logs/error.log` - Contains only ERROR and CRITICAL logs

## Configuration

The logging system is configured in `main.py` using parameters from `config.py`. Key settings include:

- `LOG_LEVEL`: Minimum log level to record (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `LOG_TO_FILE`: Whether to write logs to files
- `LOG_TO_CONSOLE`: Whether to output logs to console
- `use_json_format`: Whether to use JSON formatting (automatic in production)
- `environment`: Current environment ("dev" or "production")

## Best Practices

1. **Use Appropriate Log Levels**:
   - `DEBUG`: Detailed diagnostic information (only in development)
   - `INFO`: Confirmation that things are working as expected
   - `WARNING`: An indication that something unexpected happened, but the application still works
   - `ERROR`: Due to a more serious problem, the software has not been able to perform some function
   - `CRITICAL`: A serious error indicating that the program itself may be unable to continue running

2. **Include Context**:
   - Always include relevant IDs (user_id, transaction_id, etc.)
   - Add specific context to help troubleshoot issues

3. **Sensitive Information**:
   - Never log passwords, tokens, or sensitive personal information
   - Mask or truncate sensitive fields if they must be included

4. **Structure Messages**:
   - Keep messages clear and concise
   - Use a consistent format for similar events

5. **Debug Logging**:
   - Use debug logs liberally in development for troubleshooting
   - Avoid debug logs in production-critical code paths
   - Remember that debug logs are not saved in production 