from pydantic import BaseModel, model_validator, EmailStr, ValidationError
from typing import Optional
from datetime import datetime
from api.v1.user.models import User
from fastapi import HTTPException, status
from api.core import responses

# In your schemas.py
from typing import List
from pydantic import BaseModel

class WalletAddressList(BaseModel):
    wallets: List[dict]
    # Or more specifically:
    # wallets: List[WalletAddressItem]

class WalletAddressItem(BaseModel):
    id: int
    address: str


class WalletCreate(BaseModel):
    wallet_address: str
    chain_id: int

class WalletResponse(BaseModel):
    wallet_address: str
    chain_id: int
    is_active: bool
    last_connected: datetime

    class Config:
        from_attributes = True


class WalletVerifyRequest(BaseModel):
    uri: str
    wallet_address: str
    chain_id: int

class WalletResponse(BaseModel):
    id: int
    wallet_address: str
    chain_id: int
    session_metadata: str

    class Config:
        from_attributes = True



# schemas.py
from pydantic import BaseModel, constr
from typing import Optional
from datetime import datetime

class WalletAddressBase(BaseModel):
    address: str
    #chain_id: int

class WalletAddressCreate(WalletAddressBase):
    pass

class WalletAddressResponse(WalletAddressBase):
    id: int
    user_id: int
    balance: float
        
    class Config:
        from_attributes = True

class WalletConnectSession(BaseModel):
    uri: str
