import pytest
import pytest_asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from httpx import AsyncClient

from api.v1.subscription.models import SubscriptionPlan, SubscriptionTier, user_subscription
from api.v1.subscription.schemas import (
    SubscriptionPlanCreate,
    SubscriptionPlanResponse,
    UserSubscriptionResponse
)
from api.v1.auth.models import APIKey
from api.core.logging_config import get_logger

logger = get_logger(__name__)

# Test constants
TEST_SUBSCRIPTION_NAME = "Test Subscription Plan"
TEST_SUBSCRIPTION_DESCRIPTION = "Test subscription plan for unit tests"

@pytest_asyncio.fixture
async def professional_subscription_plan(async_db):
    """Create a professional subscription plan for testing"""
    plan = SubscriptionPlan(
        tier=SubscriptionTier.PROFESSIONAL,
        name="Professional Plan",
        description="Professional subscription plan for testing",
        price_monthly=2999,
        price_yearly=29999,
        api_rate_limit=500,
        daily_request_limit=5000,
        monthly_request_limit=150000,
        allowed_endpoints=["*"],
        max_payload_size=5 * 1024 * 1024,  # 5MB
        has_priority_support=True,
        has_advanced_features=True,
        concurrent_requests=10,
        max_response_time=200
    )
    async_db.add(plan)
    await async_db.commit()
    await async_db.refresh(plan)
    return plan

@pytest_asyncio.fixture
async def enterprise_subscription_plan(async_db):
    """Create an enterprise subscription plan for testing"""
    plan = SubscriptionPlan(
        tier=SubscriptionTier.ENTERPRISE,
        name="Enterprise Plan",
        description="Enterprise subscription plan for testing",
        price_monthly=9999,
        price_yearly=99999,
        api_rate_limit=1000,
        daily_request_limit=10000,
        monthly_request_limit=300000,
        allowed_endpoints=["*"],
        max_payload_size=10 * 1024 * 1024,  # 10MB
        has_priority_support=True,
        has_advanced_features=True,
        concurrent_requests=20,
        max_response_time=100
    )
    async_db.add(plan)
    await async_db.commit()
    await async_db.refresh(plan)
    return plan

@pytest_asyncio.fixture
async def user_with_professional_subscription(client, test_user, async_db, professional_subscription_plan):
    """Create a user with a professional subscription"""
    # First verify the user's email to activate the account
    verification_data = {
        "email": test_user["email"],
        "verification_code": test_user["verification_code"]
    }

    response = await client.post("/auth/verify/confirm", json=verification_data)
    if response.status_code == 200:
        response_json = response.json()
        
        # Create user subscription
        subscription = user_subscription(
            user_id=test_user["id"],
            subscription_plan_id=professional_subscription_plan.id,
            start_date=datetime.now(timezone.utc),
            end_date=datetime.now(timezone.utc) + timedelta(days=30),  # 30 day subscription
            status="active"
        )
        
        async_db.add(subscription)
        await async_db.commit()
        await async_db.refresh(subscription)
        
        return {
            "access_token": response_json["access_token"],
            "email": response_json["data"]["email"],
            "password": test_user["password"],
            "id": test_user["id"],
            "subscription_id": subscription.id,
            "subscription_plan_id": professional_subscription_plan.id
        }
    else:
        pytest.fail(f"Failed to verify user: {response.text}")

@pytest_asyncio.fixture
async def user_with_expired_subscription(client, test_user, async_db, professional_subscription_plan):
    """Create a user with an expired subscription"""
    # First verify the user's email to activate the account
    verification_data = {
        "email": test_user["email"],
        "verification_code": test_user["verification_code"]
    }

    response = await client.post("/auth/verify/confirm", json=verification_data)
    if response.status_code == 200:
        response_json = response.json()
        
        # Create expired user subscription
        subscription = user_subscription(
            user_id=test_user["id"],
            subscription_plan_id=professional_subscription_plan.id,
            start_date=datetime.now(timezone.utc) - timedelta(days=60),  # Started 60 days ago
            end_date=datetime.now(timezone.utc) - timedelta(days=30),  # Ended 30 days ago
            status="expired"
        )
        
        async_db.add(subscription)
        await async_db.commit()
        await async_db.refresh(subscription)
        
        return {
            "access_token": response_json["access_token"],
            "email": response_json["data"]["email"],
            "password": test_user["password"],
            "id": test_user["id"],
            "subscription_id": subscription.id,
            "subscription_plan_id": professional_subscription_plan.id
        }
    else:
        pytest.fail(f"Failed to verify user: {response.text}")

@pytest_asyncio.fixture
async def api_key_for_subscription(async_db, user_with_professional_subscription):
    """Create an API key for a subscription"""
    api_key = APIKey(
        user_id=user_with_professional_subscription["id"],
        key="test_api_key_123456",
        name="Test API Key",
        subscription_plan_id=user_with_professional_subscription["subscription_plan_id"],
        expires_at=datetime.now(timezone.utc) + timedelta(days=30),
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )
    async_db.add(api_key)
    await async_db.commit()
    await async_db.refresh(api_key)
    return api_key

@pytest.mark.asyncio
async def test_get_subscription_plans(client, test_verified_user, basic_subscription_plan, professional_subscription_plan, enterprise_subscription_plan):
    """Test getting all subscription plans"""
    response = await client.get(
        "/subscriptions",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert len(data) >= 3  # At least the three plans we created
    
    # Verify the plans contain the expected data
    plan_tiers = [plan["tier"] for plan in data]
    assert SubscriptionTier.BASIC in plan_tiers
    assert SubscriptionTier.PROFESSIONAL in plan_tiers
    assert SubscriptionTier.ENTERPRISE in plan_tiers
    
    # Check specific plan details
    for plan in data:
        if plan["tier"] == SubscriptionTier.PROFESSIONAL:
            assert plan["name"] == "Professional Plan"
            assert plan["price_monthly"] == 2999
            assert plan["has_priority_support"] is True
        elif plan["tier"] == SubscriptionTier.ENTERPRISE:
            assert plan["name"] == "Enterprise Plan"
            assert plan["price_monthly"] == 9999
            assert plan["has_advanced_features"] is True

@pytest.mark.asyncio
async def test_get_subscription_plan_by_tier(client, test_verified_user, basic_subscription_plan):
    """Test getting a specific subscription plan by tier"""
    response = await client.get(
        f"/subscriptions/{basic_subscription_plan.id}?tier={SubscriptionTier.BASIC}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response.status_code == 200
    plan = response.json()
    assert plan["tier"] == SubscriptionTier.BASIC
    assert plan["id"] == basic_subscription_plan.id
    assert plan["name"] == basic_subscription_plan.name
    assert plan["price_monthly"] == basic_subscription_plan.price_monthly
    assert plan["price_yearly"] == basic_subscription_plan.price_yearly
    assert plan["api_rate_limit"] == basic_subscription_plan.api_rate_limit
    assert plan["daily_request_limit"] == basic_subscription_plan.daily_request_limit
    assert plan["monthly_request_limit"] == basic_subscription_plan.monthly_request_limit

@pytest.mark.asyncio
async def test_get_nonexistent_subscription_plan(client, test_verified_user):
    """Test getting a subscription plan that doesn't exist"""
    # Use a non-existent tier value
    response = await client.get(
        f"/subscriptions/999?tier=NONEXISTENT",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response.status_code == 422  # Validation error for invalid enum value

@pytest.mark.asyncio
async def test_create_subscription_plan(client, async_db):
    """Test creating a new subscription plan (admin functionality)"""
    # This test assumes the create_subscription_plan endpoint doesn't require authentication
    # or has special admin permissions which we're bypassing for testing
    
    new_plan_data = {
        "name": "Test Premium Plan",
        "description": "A premium test plan",
        "price_monthly": 1999,
        "price_yearly": 19990,
        "api_rate_limit": 300,
        "daily_request_limit": 3000,
        "monthly_request_limit": 90000,
        "allowed_endpoints": ["*"],
        "max_payload_size": 3145728,  # 3MB
        "has_priority_support": True,
        "has_advanced_features": True,
        "concurrent_requests": 8,
        "max_response_time": 300
    }
    
    response = await client.post(
        f"/subscriptions?tier={SubscriptionTier.PROFESSIONAL}",
        json=new_plan_data
    )
    
    assert response.status_code == 201
    data = response.json()
    assert "message" in data
    assert "detail" in data
    assert data["detail"]["name"] == "Test Premium Plan"
    assert data["detail"]["tier"] == SubscriptionTier.PROFESSIONAL
    assert data["detail"]["price_monthly"] == 1999
    
    # Verify the plan was actually created in the database
    result = await async_db.execute(
        select(SubscriptionPlan).filter(SubscriptionPlan.name == "Test Premium Plan")
    )
    plan = result.scalar_one_or_none()
    assert plan is not None
    assert plan.tier == SubscriptionTier.PROFESSIONAL
    assert plan.price_monthly == 1999

@pytest.mark.asyncio
async def test_cancel_user_subscription(client, user_with_professional_subscription, async_db, api_key_for_subscription, basic_subscription_plan):
    """Test cancelling a user's subscription"""
    # First verify the user has a professional subscription
    result = await async_db.execute(
        select(user_subscription).filter(
            user_subscription.user_id == user_with_professional_subscription["id"],
            user_subscription.subscription_plan_id == user_with_professional_subscription["subscription_plan_id"]
        )
    )
    subscription = result.scalar_one_or_none()
    assert subscription is not None
    
    # Also verify the API key exists
    result = await async_db.execute(
        select(APIKey).filter(
            APIKey.user_id == user_with_professional_subscription["id"],
            APIKey.subscription_plan_id == user_with_professional_subscription["subscription_plan_id"]
        )
    )
    api_key = result.scalar_one_or_none()
    assert api_key is not None
    
    # Now cancel the subscription
    response = await client.delete(
        f"/subscriptions/cancel/{subscription.id}",
        headers={"Authorization": f"Bearer {user_with_professional_subscription['access_token']}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "Unsubscribed successfully" in data["message"]
    
    # Verify the subscription was deleted
    result = await async_db.execute(
        select(user_subscription).filter(
            user_subscription.user_id == user_with_professional_subscription["id"],
            user_subscription.subscription_plan_id == user_with_professional_subscription["subscription_plan_id"]
        )
    )
    subscription = result.scalar_one_or_none()
    assert subscription is None
    
    # Verify the API key was also deleted
    result = await async_db.execute(
        select(APIKey).filter(
            APIKey.user_id == user_with_professional_subscription["id"],
            APIKey.key == "test_api_key_123456"
        )
    )
    api_key = result.scalar_one_or_none()
    assert api_key is None

@pytest.mark.asyncio
async def test_cancel_nonexistent_subscription(client, test_verified_user):
    """Test cancelling a subscription that doesn't exist"""
    response = await client.delete(
        f"/subscriptions/cancel/999",  # Non-existent subscription ID
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "User has no subscription plan" in data["detail"]

@pytest.mark.asyncio
async def test_user_with_expired_subscription(client, user_with_expired_subscription, async_db):
    """Test behavior with an expired subscription"""
    # Verify the user has an expired subscription
    result = await async_db.execute(
        select(user_subscription).filter(
            user_subscription.user_id == user_with_expired_subscription["id"]
        )
    )
    subscription = result.scalar_one_or_none()
    assert subscription is not None
    assert subscription.status == "expired"
    assert subscription.end_date < datetime.now(timezone.utc)
    
    # Try to access a subscription-related endpoint
    response = await client.get(
        "/subscriptions",
        headers={"Authorization": f"Bearer {user_with_expired_subscription['access_token']}"}
    )
    
    # The user should still be able to access basic endpoints even with an expired subscription
    assert response.status_code == 200

@pytest.mark.asyncio
async def test_direct_subscription_service(async_db, test_verified_user, basic_subscription_plan, professional_subscription_plan):
    """Test direct interaction with the Subscription service class"""
    from api.v1.subscription.services import Subscription
    
    # Test get_subscription_plans
    subscription_service = Subscription()
    plans = await subscription_service.get_subscription_plans(db=async_db)
    assert len(plans) >= 2  # At least basic and professional plans
    
    # Test get_subscription_plan
    basic_plan = await subscription_service.get_subscription_plan(tier=SubscriptionTier.BASIC, db=async_db)
    assert basic_plan.tier == SubscriptionTier.BASIC
    assert basic_plan.id == basic_subscription_plan.id
    
    # Test subscribe_user (create a new subscription)
    from api.v1.user.schemas import User
    
    # First, cancel any existing subscriptions
    try:
        await subscription_service.cancel_user_subscription(
            user=User(id=test_verified_user["id"], email=test_verified_user["email"]),
            db=async_db
        )
    except Exception:
        pass  # Ignore errors if no subscription exists
    
    # Now subscribe the user to the professional plan
    user_subscription_response = await subscription_service.subscribe_user(
        tier=SubscriptionTier.PROFESSIONAL,
        user=User(id=test_verified_user["id"], email=test_verified_user["email"]),
        db=async_db
    )
    
    assert user_subscription_response is not None
    assert user_subscription_response.subscription_plan_id == professional_subscription_plan.id
    assert user_subscription_response.user_id == test_verified_user["id"]
    assert user_subscription_response.status == "active"
    
    # Verify the subscription was created in the database
    result = await async_db.execute(
        select(user_subscription).filter(
            user_subscription.user_id == test_verified_user["id"],
            user_subscription.subscription_plan_id == professional_subscription_plan.id
        )
    )
    subscription = result.scalar_one_or_none()
    assert subscription is not None
    assert subscription.status == "active"
    
    # Test cancel_user_subscription
    cancel_result = await subscription_service.cancel_user_subscription(
        user=User(id=test_verified_user["id"], email=test_verified_user["email"]),
        db=async_db
    )
    
    assert cancel_result is not None
    assert "message" in cancel_result
    assert "Unsubscribed successfully" in cancel_result["message"]
    
    # Verify the subscription was deleted
    result = await async_db.execute(
        select(user_subscription).filter(
            user_subscription.user_id == test_verified_user["id"],
            user_subscription.subscription_plan_id == professional_subscription_plan.id
        )
    )
    subscription = result.scalar_one_or_none()
    assert subscription is None
