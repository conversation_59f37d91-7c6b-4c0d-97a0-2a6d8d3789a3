from pydantic import BaseModel, model_validator, EmailStr, ValidationError
from typing import Optional
from datetime import datetime
from api.v1.user.models import User
from fastapi import HTTPException, status, WebSocket
from api.core import responses
from typing import Any
from pydantic import BaseModel
from datetime import datetime
from dataclasses import dataclass


class NotificationSchema(BaseModel):
    id: int
    user_id: int
    message: str
    is_read: bool
    created_at: datetime

    class Config:
        from_attributes=True

@dataclass
class WebSocketSchema:
    socket: WebSocket
    created_at: datetime
    last_heartbeat: datetime





from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime
from .models import NotificationType, NotificationPriority, NotificationStatus

class NotificationCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    message: str = Field(..., min_length=1)
    type: NotificationType = Field(default=NotificationType.SYSTEM)
    priority: NotificationPriority = Field(default=NotificationPriority.NORMAL)
    metadata: Optional[Dict[str, Any]] = None
    action_url: Optional[str] = None

class NotificationUpdate(BaseModel):
    is_read: Optional[bool] = None
    status: Optional[NotificationStatus] = None

class NotificationResponse(BaseModel):
    id: int
    user_id: int
    title: str
    message: str
    type: NotificationType
    priority: NotificationPriority
    status: NotificationStatus
    is_read: bool
    metadata: Optional[Dict[str, Any]]
    action_url: Optional[str]
    created_at: datetime
    updated_at: datetime
    read_at: Optional[datetime]
    delivered_at: Optional[datetime]

    class Config:
        from_attributes = True



class Notifications(BaseModel):
    id: int
    title: str
    message: str
    type: NotificationType
    priority: NotificationPriority
    created_at: datetime
    is_read: bool

    class Config:
        from_attributes = True