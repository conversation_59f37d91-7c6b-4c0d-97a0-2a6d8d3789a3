from fastapi import Depends, UploadFile, File, APIRouter, Depends, status, Response, Request, BackgroundTasks
from sqlalchemy.orm import Session
#from decouple import config
from api.v1.user import schemas as user_schema
from api.db.database import get_db
from api.v1.user.services import UserService
from api.core.dependencies import is_authenticated
from .exceptions import handle_user_notification
from api.v1.auth.exceptions import handle_auth_error
from .exceptions import handle_user_error
from api.core.general_dep import get_redis
from redis.asyncio import Redis
from typing import Optional

app = APIRouter(tags=["Users"])


@app.post("/user/avater", status_code=status.HTTP_200_OK)
async def upload_avater(
    file: UploadFile = File(...),
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    redis: Optional[Redis] = Depends(get_redis)
):
    try:
        userService = UserService(redis)
        avater = await userService.upload_avater(user=user, file=file, db=db)
        #handle_user_notification(user_id=user.id, action="upload_avatar")
        return {"message": "Avater uploaded successfully.", "detail": avater}
    except Exception as e:
        handle_user_error(e)


@app.get("/user/{user_id}/avater", status_code=status.HTTP_200_OK)
async def get_avater(
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    redis: Optional[Redis] = Depends(get_redis)
):
    try:
        userService = UserService(redis)
        user_avater = await userService.get_avater(user=user, db=db)
        return user_avater
    except Exception as e:
        handle_user_error(e)


@app.delete("/users/avater/{user_id}", status_code=status.HTTP_200_OK)
async def delete_avater(
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    redis: Optional[Redis] = Depends(get_redis)
):

    """
        This endpoint deletes a user's avater from the db. (Soft delete)
        Returns message: User avater deleted successfully.
    """
    try:
        userService = UserService(redis)
        deleted_user = await userService.delete_avater(db=db, user=user)
        #handle_user_notification(user_id=user.id, action="delete_avatar")
        return deleted_user
    except Exception as e:
        handle_user_error(e)


@app.get("/user", status_code=status.HTTP_200_OK)
async def get_user(
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    redis: Optional[Redis] = Depends(get_redis)
):
    
    """
        Returns an authenticated user information
    """
    try:
        userService = UserService(redis)
        get_user = await userService.get_user(db=db, user_id=user.id)
        return get_user
    except Exception as e:
        handle_user_error(e)


@app.delete("/users/{user_id}")
async def delete_user(
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    redis: Optional[Redis] = Depends(get_redis)
):

    """
        This endpoint deletes a user from the db. (Soft delete)
    """
    try:    
        userService = UserService(redis)
        deleted_user = await userService.delete(db=db, id=user.id)
        #await handle_user_notification(user_id=user.id, action="delete_user")
        return {"message": "User deleted successfully."}

    except Exception as e:
        handle_user_error(e)


@app.patch("/users/{user_id}", status_code=status.HTTP_200_OK)
async def update_user(
    user_data: user_schema.UpdateUser,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    redis: Optional[Redis] = Depends(get_redis)
    ):
    """
        This endpoint updates a user's information.
        Returns message: User updated successfully.
    """
    try:
        userService = UserService(redis)
        updated_user = await userService.update(db=db, id=user.id, user=user_data)
        handle_user_notification(user_id=user.id, action="update_user")
        return {"message": "User data upadated successfully", "details": updated_user}

    except Exception as e:
        handle_user_error(e)


"""
@app.put("/users/{user_id}/avater", status_code=status.HTTP_200_OK)
async def update_avater(
    file: UploadFile = File(...),
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
    ):
    updated_avater = userService.update_avater(db=db, file=file, user=user)
    await handle_user_notification(user_id=user.id, action="update_avatar")
    return {"message": "Avater uploaded successfully.", "detail": updated_avater}



@app.post("/user/notification/", status_code=status.HTTP_200_OK)
async def notification_user(
    notification_settings: user_schema.NotificationSettings,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
    ):

    notification = userService.notification(user=user, notification_settings=notification_settings, db=db)

    return {"message": "Notification sent successfully.", "detail": notification}
"""

@app.patch("/user/notification/update", status_code=status.HTTP_200_OK)
async def notification_update(
    notification_settings: user_schema.NotificationSettings,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    redis: Optional[Redis] = Depends(get_redis)
):
    try:
        userService = UserService(redis)
        notification = await userService.update_notification(user=user, notification_settings=notification_settings, db=db)
        handle_user_notification(user_id=user.id, action="update_notification_settings")
        return {"message": "Notification updated successfully.", "detail": notification}

    except Exception as e:
        handle_user_error(e)







from api.v1.auth.services import Auth, APIkey
from api.v1.user import schemas as user_schema
from api.v1.auth import schemas as auth_schema

@app.post("/user/api-key", status_code=status.HTTP_201_CREATED)
async def create_api_key(
    name: str,
    current_user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    try:
            
        apiService = APIkey()
        api_key = await apiService.create_api_key(user=current_user, name=name, db=db)
        handle_user_notification(user_id=current_user.id, action="create_api_key")
        return api_key

    except Exception as e:
        handle_user_notification(user_id=current_user.id, action="create_api_key_failed")
        handle_auth_error(e)


@app.get("/user/api-keys", status_code=status.HTTP_200_OK)
async def list_api_keys(
    current_user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    try:
            
        apiService = APIkey()
        keys = await apiService.list_api_keys(db=db, user=current_user)
        return keys

    except Exception as e:
        handle_auth_error(e)


@app.put("/user/api-keys/{api_key_id}/regenerate", status_code=status.HTTP_200_OK, response_model=auth_schema.APIKEY)
async def regenerate_api_key(
    api_key_id: int,
    current_user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):   
    try:
            
        apiService = APIkey()
        key = await apiService.regenerate_api_key(db=db, user=current_user, api_key_id=api_key_id)
        handle_user_notification(user_id=current_user.id, action="regenerate_api_key")
        return key

    except Exception as e:
        handle_auth_error(e)


@app.delete("/user/api-keys/{api_key_id}")
async def delete_api_key(
    api_key_id: int,
    current_user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    try:
            
        apiService = APIkey()
        result = await apiService.delete_api_key(db=db, user=current_user, api_key_id=api_key_id)
        handle_user_notification(user_id=current_user.id, action="delete_api_key")
        return result

    except Exception as e:
        handle_auth_error(e)

