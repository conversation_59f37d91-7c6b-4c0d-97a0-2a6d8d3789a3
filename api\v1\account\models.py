from sqlalchemy import <PERSON><PERSON><PERSON>, Column, ForeignKey, Text, Integer, String, DateTime, BIGINT, Enum, Numeric
from sqlalchemy.orm import relationship
from datetime import datetime, date
from api.db.database import Base
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey
import enum


class Account(Base):
    __tablename__ = "account"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(BIGINT, ForeignKey("user.id"), nullable=False, index=True)
    address = Column(String, unique=True, index=True)

    encrypted_private_key= Column(String, nullable=False)
    encryption_salt= Column(String(40), nullable=False)
    encryption_nonce= Column(String(24), nullable=False)

    created_at = Column(DateTime(timezone=True), default=datetime.now)

    user = relationship("User", back_populates="accounts")
    contract_transactions = relationship("WalletTransactions", back_populates="vault")



class WalletScore(Base):
    __tablename__ = "wallet_scores"

    id = Column(Integer, primary_key=True, index=True)
    wallet_address = Column(BIGINT, ForeignKey("account.id"), nullable=False, index=True)
    user_id = Column(BIGINT, ForeignKey("user.id"), nullable=False, index=True)
    total_score = Column(Integer)
    historical_score = Column(Integer)
    activity_score = Column(Integer)
    financial_score = Column(Integer)
    network_score = Column(Integer)
    created_at = Column(DateTime(timezone=True))
    updated_at = Column(DateTime(timezone=True))



class ContractStatus(enum.Enum):
    DEPLOYED = "deployed"
    FAILED = "failed"

class TransactionType(enum.Enum):
    ER20 = "erc20"
    ERC721 = "erc721"
    NONE = "none"


class WalletTransactions(Base):
    __tablename__ = 'wallet_transactions'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False, index=True)
    address_id = Column(Integer, ForeignKey("account.id"), nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), default=datetime.now, nullable=False)
    contract_status = Column(Enum(ContractStatus), nullable=False)
    value = Column(BIGINT, nullable=True)
    gas_used = Column(BIGINT, nullable=True)
    contract_type = Column(Enum(TransactionType), nullable=True)

    vault = relationship("Account", back_populates="contract_transactions")


class AccountBalanceHistory(Base):
    __tablename__ = 'account_balance_history'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False, index=True)
    account_id = Column(Integer, ForeignKey("account.id"), nullable=False)
    balance = Column(Numeric(precision=36, scale=18), nullable=False)  # Store in Wei
    created_at = Column(DateTime(timezone=True), default=datetime.now, nullable=False)





class WalletScoreHistory(Base):
    __tablename__ = 'wallet_score_history'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False, index=True)
    wallet_address = Column(Integer, ForeignKey("account.id"), nullable=False)
    total_score = Column(Integer, nullable=False)
    historical_score = Column(Integer, nullable=False)
    activity_score = Column(Integer, nullable=False)
    financial_score = Column(Integer, nullable=False)
    network_score = Column(Integer, nullable=False)
    created_at = Column(DateTime(timezone=True), default=datetime.now, nullable=False)
