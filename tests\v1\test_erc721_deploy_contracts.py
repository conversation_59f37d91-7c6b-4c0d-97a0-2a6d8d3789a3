import pytest
import pytest_asyncio
from unittest.mock import patch, MagicMock, AsyncMock
from web3 import AsyncWeb3, Web3
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from fastapi import UploadFile
import io
import json
import os

from api.v1.deploy_contracts.models import ContractDeployment, TransactionType, ToBeSigned
from api.v1.deploy_contracts.services import DeployErc20
from api.v1.deploy_contracts.compile_erc20 import Compile721
from api.core.logging_config import get_logger
from tests.v1.test_account import TEST_PROVIDER, TEST_ADDRESS, MockRedis

logger = get_logger(__name__)

# Test constants
TEST_CONTRACT_ADDRESS = "0x742d35Cc6634C0532925a3b844Bc454e4438f44e"
TEST_BLOCK_NUMBER = 1000000
TEST_TRANSACTION_HASH = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
TEST_PRIVATE_KEY = "0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef"

# Sample ABI and bytecode for mocking
SAMPLE_ABI = [
    {
        "inputs": [
            {"internalType": "string", "name": "name_", "type": "string"},
            {"internalType": "string", "name": "symbol_", "type": "string"},
            {"internalType": "address", "name": "_platformWallet", "type": "address"},
            {"internalType": "uint256", "name": "_platformFeePercentage", "type": "uint256"},
            {"internalType": "uint256", "name": "_flatFeeAmount", "type": "uint256"}
        ],
        "stateMutability": "nonpayable",
        "type": "constructor"
    },
    {
        "inputs": [],
        "name": "name",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "symbol",
        "outputs": [{"internalType": "string", "name": "", "type": "string"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "totalSupply",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}],
        "name": "ownerOf",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "string", "name": "uri", "type": "string"}
        ],
        "name": "mint",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "nonpayable",
        "type": "function"
    }
]

SAMPLE_BYTECODE = "0x608060405234801561001057600080fd5b50610771806100206000396000f3fe608060405234801561001057600080fd5b50600436106100415760003560e01c8063..."

# Mock compiled contract output for ERC721
MOCK_COMPILED_CONTRACT_721 = {
    "contracts": {
        "erc_seventwoone.sol": {
            "ERC721Token": {
                "abi": SAMPLE_ABI,
                "evm": {
                    "bytecode": {
                        "object": SAMPLE_BYTECODE
                    }
                }
            }
        }
    }
}

@pytest_asyncio.fixture
async def test_rpc_web3():
    """Create a real Web3 instance connected to a test RPC"""
    # Use a local Ganache or other test RPC
    # For testing purposes, we'll use a mock but in a real scenario, 
    # you would connect to an actual test RPC like Ganache
    mock = AsyncMock()
    mock.eth = AsyncMock()
    mock.eth.chain_id = 1337  # Local test chain ID
    mock.eth.get_block_number = AsyncMock(return_value=TEST_BLOCK_NUMBER)
    mock.eth.get_transaction_count = AsyncMock(return_value=0)
    mock.eth.get_transaction = AsyncMock(return_value={
        'hash': TEST_TRANSACTION_HASH,
        'from': TEST_ADDRESS,
        'to': None,  # Contract creation
        'value': 0,
        'gas': 2000000,
        'gasPrice': Web3.to_wei(50, 'gwei'),
        'nonce': 0,
        'blockHash': '0x...',
        'blockNumber': TEST_BLOCK_NUMBER,
        'transactionIndex': 0
    })
    mock.eth.get_transaction_receipt = AsyncMock(return_value={
        'transactionHash': TEST_TRANSACTION_HASH,
        'blockHash': '0x...',
        'blockNumber': TEST_BLOCK_NUMBER,
        'contractAddress': TEST_CONTRACT_ADDRESS,
        'cumulativeGasUsed': 1000000,
        'effectiveGasPrice': Web3.to_wei(50, 'gwei'),
        'gasUsed': 1000000,
        'logs': [],
        'logsBloom': '0x...',
        'status': 1,  # Success
        'transactionIndex': 0
    })
    
    # Mock contract instance
    mock_contract = AsyncMock()
    mock_contract.address = TEST_CONTRACT_ADDRESS
    mock_contract.functions = AsyncMock()
    mock_contract.functions.name = AsyncMock()
    mock_contract.functions.name.call = AsyncMock(return_value="Test NFT")
    mock_contract.functions.symbol = AsyncMock()
    mock_contract.functions.symbol.call = AsyncMock(return_value="TNFT")
    mock_contract.functions.totalSupply = AsyncMock()
    mock_contract.functions.totalSupply.call = AsyncMock(return_value=0)
    mock_contract.functions.balanceOf = AsyncMock()
    mock_contract.functions.balanceOf.call = AsyncMock(return_value=0)
    
    # Mock contract creation
    mock.eth.contract = MagicMock(return_value=mock_contract)
    
    # Mock transaction sending
    mock.eth.send_raw_transaction = AsyncMock(return_value=TEST_TRANSACTION_HASH)
    mock.eth.wait_for_transaction_receipt = AsyncMock(return_value={
        'transactionHash': TEST_TRANSACTION_HASH,
        'blockHash': '0x...',
        'blockNumber': TEST_BLOCK_NUMBER,
        'contractAddress': TEST_CONTRACT_ADDRESS,
        'cumulativeGasUsed': 1000000,
        'effectiveGasPrice': Web3.to_wei(50, 'gwei'),
        'gasUsed': 1000000,
        'logs': [],
        'logsBloom': '0x...',
        'status': 1,  # Success
        'transactionIndex': 0
    })
    
    # Mock address conversion
    mock.to_checksum_address = MagicMock(lambda addr: addr)
    
    return mock

@pytest_asyncio.fixture
async def mock_redis():
    return MockRedis()

@pytest_asyncio.fixture
async def test_erc721_signature(async_db, test_verified_user):
    """Create a test signature record for ERC721 deployment"""
    signature = ToBeSigned(
        user_id=test_verified_user["id"],
        transaction_type=TransactionType.ERC721,
        transaction_data={
            "name": "Test NFT",
            "symbol": "TNFT",
            "description": "Test NFT collection for unit tests"
        },
        status="pending",
        created_at=datetime.now(timezone.utc)
    )
    async_db.add(signature)
    await async_db.commit()
    await async_db.refresh(signature)
    return signature

@pytest.mark.asyncio
async def test_compile_erc721(client, test_verified_user):
    """Test compiling ERC721 contract"""
    # Mock the Compile721.create and compile methods
    with patch('api.v1.deploy_contracts.compile_erc20.Compile721.create') as mock_create, \
         patch('api.v1.deploy_contracts.compile_erc20.setup_solc') as mock_setup_solc:
        
        mock_compile_instance = AsyncMock()
        mock_compile_instance.compile = AsyncMock(return_value=MOCK_COMPILED_CONTRACT_721)
        mock_create.return_value = mock_compile_instance
        mock_setup_solc.return_value = None
        
        response = await client.post(
            "/erc721/compile",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "contracts" in data
        assert "erc_seventwoone.sol" in data["contracts"]
        assert "ERC721Token" in data["contracts"]["erc_seventwoone.sol"]
        assert "abi" in data["contracts"]["erc_seventwoone.sol"]["ERC721Token"]

@pytest.mark.asyncio
async def test_deploy_erc721_with_test_rpc(client, test_verified_user, test_rpc_web3, mock_redis, async_db):
    """Test deploying ERC721 contract using test RPC"""
    # Create a test file for upload
    test_file_content = b"test logo content"
    test_file = io.BytesIO(test_file_content)
    
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services.load_contract_artifacts') as mock_load_artifacts, \
         patch('api.v1.deploy_contracts.services.DeployErc20._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services.upload_image') as mock_upload_image, \
         patch('api.v1.deploy_contracts.services.ContractDeployer.constructor') as mock_constructor:
        
        mock_load_artifacts.return_value = (SAMPLE_ABI, SAMPLE_BYTECODE)
        mock_init_wallet.return_value = None
        mock_upload_image.return_value = ("test_logo.png", "/path/to/test_logo.png")
        
        # Mock the constructor method to return a signature ID
        mock_constructor.return_value = {
            "signature_id": 1,
            "message": "Contract deployment prepared. Please sign the transaction."
        }
        
        response = await client.post(
            "/erc721/deploy",
            data={
                "name": "Test NFT",
                "symbol": "TNFT",
                "description": "Test NFT collection for unit tests",
                "default_wallet": "true"
            },
            files={"file": ("test_logo.png", test_file, "image/png")},
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "signature_id" in data
        assert "message" in data
        
        # Verify a ToBeSigned record was created
        result = await async_db.execute(
            select(ToBeSigned).where(
                ToBeSigned.user_id == test_verified_user["id"],
                ToBeSigned.transaction_type == TransactionType.ERC721
            )
        )
        signature = result.scalar_one_or_none()
        assert signature is not None
        assert signature.transaction_type == TransactionType.ERC721
        assert signature.transaction_data["name"] == "Test NFT"
        assert signature.transaction_data["symbol"] == "TNFT"

@pytest.mark.asyncio
async def test_sign_and_deploy_erc721_transaction(client, test_verified_user, test_erc721_signature, test_rpc_web3, mock_redis, async_db):
    """Test signing and deploying an ERC721 transaction using test RPC"""
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services.load_contract_artifacts') as mock_load_artifacts, \
         patch('api.v1.deploy_contracts.services.DeployErc20._initialize_wallet') as mock_init_wallet, \
         patch('api.v1.deploy_contracts.services.ContractDeployer.sign_and_send_transaction') as mock_sign_and_send, \
         patch('api.v1.deploy_contracts.router.handle_deploy_notification') as mock_notification:
        
        mock_load_artifacts.return_value = (SAMPLE_ABI, SAMPLE_BYTECODE)
        mock_init_wallet.return_value = None
        
        # Mock successful transaction deployment
        mock_sign_and_send.return_value = {
            "contract_address": TEST_CONTRACT_ADDRESS,
            "transaction_hash": TEST_TRANSACTION_HASH,
            "block_number": TEST_BLOCK_NUMBER,
            "status": "success",
            "message": "Contract deployed successfully"
        }
        
        mock_notification.return_value = None
        
        response = await client.post(
            f"/sign/deploy/{test_erc721_signature.id}",
            params={"contract": "ERC721"},
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "contract_address" in data
        assert data["contract_address"] == TEST_CONTRACT_ADDRESS
        assert "transaction_hash" in data
        assert data["transaction_hash"] == TEST_TRANSACTION_HASH
        assert "status" in data
        assert data["status"] == "success"
        
        # Verify a ContractDeployment record was created
        result = await async_db.execute(
            select(ContractDeployment).where(
                ContractDeployment.user_id == test_verified_user["id"],
                ContractDeployment.contract_address == TEST_CONTRACT_ADDRESS
            )
        )
        contract = result.scalar_one_or_none()
        assert contract is not None
        assert contract.contract_type == TransactionType.ERC721
        assert contract.block_number == TEST_BLOCK_NUMBER

@pytest.mark.asyncio
async def test_get_all_erc721_contracts(client, test_verified_user, test_rpc_web3, mock_redis, async_db):
    """Test getting all ERC721 contracts using test RPC"""
    # Create a test contract deployment
    contract = ContractDeployment(
        user_id=test_verified_user["id"],
        contract_address=TEST_CONTRACT_ADDRESS,
        contract_type=TransactionType.ERC721,
        block_number=TEST_BLOCK_NUMBER,
        created_at=datetime.now(timezone.utc)
    )
    async_db.add(contract)
    await async_db.commit()
    
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services.load_contract_artifacts') as mock_load_artifacts, \
         patch('api.v1.deploy_contracts.services.DeployErc20._initialize_wallet') as mock_init_wallet:
        
        mock_load_artifacts.return_value = (SAMPLE_ABI, SAMPLE_BYTECODE)
        mock_init_wallet.return_value = None
        
        response = await client.get(
            "/users/get_all_erc721_contracts",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "contracts" in data
        assert len(data["contracts"]) >= 1
        
        # Find our test contract in the results
        found = False
        for contract_data in data["contracts"]:
            if contract_data["contract_address"] == TEST_CONTRACT_ADDRESS:
                found = True
                assert contract_data["contract_type"] == "erc721"
                break
        
        assert found, "Test contract not found in the results"

@pytest.mark.asyncio
async def test_get_contract_by_id_erc721(client, test_verified_user, test_rpc_web3, mock_redis, async_db):
    """Test getting a specific ERC721 contract by ID using test RPC"""
    # Create a test contract deployment
    contract = ContractDeployment(
        user_id=test_verified_user["id"],
        contract_address=TEST_CONTRACT_ADDRESS,
        contract_type=TransactionType.ERC721,
        block_number=TEST_BLOCK_NUMBER,
        created_at=datetime.now(timezone.utc)
    )
    async_db.add(contract)
    await async_db.commit()
    await async_db.refresh(contract)
    
    # Mock the necessary methods
    with patch('api.v1.deploy_contracts.services.load_contract_artifacts') as mock_load_artifacts, \
         patch('api.v1.deploy_contracts.services.DeployErc20._initialize_wallet') as mock_init_wallet:
        
        mock_load_artifacts.return_value = (SAMPLE_ABI, SAMPLE_BYTECODE)
        mock_init_wallet.return_value = None
        
        response = await client.get(
            f"/users/get_contract?contract_id={contract.id}",
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "contract_address" in data
        assert data["contract_address"] == TEST_CONTRACT_ADDRESS
        assert "contract_type" in data
        assert data["contract_type"] == "erc721"
