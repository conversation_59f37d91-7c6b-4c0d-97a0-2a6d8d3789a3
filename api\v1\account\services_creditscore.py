from api.v1.account.models import Account as AccountWallet
from api.v1.deploy_contracts.models import ContractDeployment
from api.v1.account import schemas as AccountSchemas
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status
from .models import WalletScore, WalletTransactions, ContractStatus, TransactionType, WalletScoreHistory
from datetime import datetime, timedelta, timezone
from dateutil.relativedelta import relativedelta
from .exceptions import (logger, handle_chart_error, DataProcessingError)
import json
from typing import Any, Optional
import time
from sqlalchemy.exc import SQLAlchemyError
from redis.asyncio import Redis
from sqlalchemy.future import select
import asyncio

LONG_CACHE_TIME = 3600

class CreditScore:
    def __init__(self, db: AsyncSession, user_id: int, address_id: int, redis: Optional[Redis] = None):
        self.db = db
        self.user_id = user_id
        self.cache_time = LONG_CACHE_TIME
        self.redis = redis
        self.address_id = address_id

    @classmethod
    async def create(cls, db: AsyncSession, user_id: int, redis: Optional[Redis] = None):

        stmt = select(AccountWallet.id).filter(AccountWallet.user_id == user_id)
        result = await db.execute(stmt)
        address_id = result.scalar_one_or_none()

        if not address_id:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Wallet address not found")

        return cls(db=db, user_id=user_id, address_id=address_id, redis=redis)

    def _handle_chart_error(self, error: Exception) -> None:
        """Handle errors in event processing"""
        handle_chart_error(error)


    async def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache with error handling"""
        if not self.redis:
            return None

        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Cache data corruption detected: {str(e)}")
            await self.redis.delete(cache_key)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval failed: {str(e)}")
            return None

    async def _cache_data(self, cache_key: str, data: Any, cache_time: int) -> None:
        """Cache data with error handling"""
        if not self.redis:
            return None

        try:
            await self.redis.set(cache_key, json.dumps(data))
            await self.redis.expire(cache_key, cache_time)
        except Exception as e:
            logger.warning(f"Failed to cache data: {str(e)}")



    async def get_transaction_data(self):

        wallet_query = self.db.execute(select(WalletTransactions).filter(
            WalletTransactions.user_id == self.user_id,
            WalletTransactions.address_id == self.address_id
        ))

        contract_query = self.db.execute(select(ContractDeployment).filter(
            ContractDeployment.user_id == self.user_id
        ))

        account_query = self.db.execute(select(AccountWallet).filter(
            AccountWallet.user_id == self.user_id
        ))

        wallet_result, contract_result, account_result = await asyncio.gather(
            wallet_query, contract_query, account_query
        )

        transactions = wallet_result.scalars().all()
        depolyed_transactions = contract_result.scalars().all()
        vault = account_result.scalar_one_or_none()

        if not vault:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="No wallet found for this user")

        return transactions, depolyed_transactions, vault



    def _calculate_avg_transaction_value_ratio(self, transactions: list[WalletTransactions]) -> float:
        """Helper to calculate the average transaction value ratio."""
        transaction_count = len(transactions)
        user_avg_value = (sum(tx.value for tx in transactions if tx.value) / transaction_count) if transaction_count > 0 else 0
        network_avg_value = 1000  # Placeholder for network average value
        return (user_avg_value / network_avg_value) if user_avg_value > 0 and network_avg_value else 0.0


    def _calculate_transaction_success_rate(self, transactions: list[WalletTransactions]) -> float:
        """Helper to calculate the transaction success rate."""
        transaction_count = len(transactions)
        if not transaction_count:
            return 0.0
        success_count = sum(1 for tx in transactions if tx.contract_status == ContractStatus.DEPLOYED)
        return (success_count / transaction_count) * 100


    async def calculate_and_store_wallet_score(self):
        start_time = time.time()
        cache_key = f"user_credit_score:{self.user_id}"
        try:
            user_credit_score = await self._get_cached_data(cache_key)
            if user_credit_score:
                return user_credit_score

            transactions, depolyed_transactions, vault = await self.get_transaction_data()
            balance_stability_ratio = 1.0

            try:
                current_time = datetime.now(timezone.utc)
                vault_created_at = vault.created_at.replace(tzinfo=timezone.utc) if vault.created_at.tzinfo is None else vault.created_at                
                rd = relativedelta(current_time, vault_created_at)

                #rd = relativedelta(datetime.now(), vault.created_at)
                wallet_age_months = rd.months
                wallet_age_years = rd.years

                nft_count = sum(1 for tx in depolyed_transactions if tx.contract_type == TransactionType.ERC721)
                erc20_count = sum(1 for tx in depolyed_transactions if tx.contract_type == TransactionType.ER20)

                transaction_count = len(transactions)
                transaction_success_rate = self._calculate_transaction_success_rate(transactions)
                avg_tx_value_ratio = self._calculate_avg_transaction_value_ratio(transactions)
                transaction_frequency = self.infer_transaction_frequency(transactions)

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Failed to process statistics: {str(e)}")
                raise DataProcessingError(f"Failed to process statistics: {str(e)}")
            
            # 3) Compute final score
            total_score, hs, ascore, fs, ns = self.compute_final_score(
                wallet_age_months=wallet_age_months,
                transaction_count=transaction_count,
                transaction_frequency=transaction_frequency,
                transaction_success_rate=transaction_success_rate,
                balance_stability_ratio=balance_stability_ratio,
                nft_count=nft_count,
                erc20_count=erc20_count,
                avg_tx_value_ratio=avg_tx_value_ratio
            )
            weekly_rate_change = await self._get_previous_week_score(total_score)

            # Store the current score in history
            new_history = WalletScoreHistory(
                user_id=self.user_id,
                wallet_address=self.address_id,
                total_score=total_score,
                historical_score=hs,
                activity_score=ascore,
                financial_score=fs,
                network_score=ns,
                created_at=datetime.now(timezone.utc)
            )
            self.db.add(new_history)

            # 4) Insert into wallet_scores
            new_score_record = WalletScore(
                wallet_address=self.address_id,
                user_id = self.user_id,
                total_score=total_score,
                historical_score=hs,
                activity_score=ascore,
                financial_score=fs,
                network_score=ns,
            )
            self.db.add(new_score_record)


            try:
                await self.db.commit()
                await self.db.refresh(new_score_record)
            except SQLAlchemyError as e:
                await self.db.rollback()
                logger.error(f"Database error occurred: {str(e)}")
                raise DataProcessingError(f"Database error: {str(e)}") from e


            saved_records = AccountSchemas.WalletScoreSchema(
                total_score = new_score_record.total_score,
                wallet_age_years = wallet_age_years,
                wallet_age_months = wallet_age_months,
                transaction_count = transaction_count,
                historical_score = new_score_record.historical_score,
                activity_score = new_score_record.activity_score,
                financial_score = new_score_record.financial_score,
                network_score = new_score_record.network_score,
                weekly_rate_change=round(weekly_rate_change, 2),
                )

            formatted_data = saved_records.model_dump()
            formatted_data['created_at'] = saved_records.created_at.isoformat()

            await self._cache_data(
                cache_key,
                formatted_data,
                self.cache_time
            )
            logger.info(f"get vault retrival completed in {time.time() - start_time:.2f} seconds")

            return saved_records

        except Exception as e:
            self._handle_chart_error(e)




    async def _get_previous_week_score(self, total_score: int) -> float:
        """Helper to get the previous week's score and calculate weekly rate change."""
        try:
            one_week_ago = datetime.now() - timedelta(days=7)
            two_weeks_ago = datetime.now() - timedelta(days=14)

            result = await self.db.execute(
                select(WalletScoreHistory)
                .filter(
                    WalletScoreHistory.user_id == self.user_id,
                    WalletScoreHistory.created_at >= two_weeks_ago,
                    WalletScoreHistory.created_at < one_week_ago
                )
                .order_by(WalletScoreHistory.created_at.desc())
            )
            previous_week_score = result.scalars().first()

            previous_score = previous_week_score.total_score if previous_week_score else total_score
            weekly_rate_change = (
                ((total_score - previous_score) / previous_score * 100)
                if previous_score > 0
                else 0.0
            )
            return weekly_rate_change

        except Exception as e:
            logger.error(f"Failed to process analytics statistics: {str(e)}")
            raise DataProcessingError(f"Failed to process analytics statistics: {str(e)}")


    #HELPERS
    @staticmethod
    def calculate_historical_factors(wallet_age_months: int, transaction_count: int) -> int:
        """
        1. Historical Factors (Total: 250 points)
        - Wallet Age (150 points)
        - Transaction History Length (100 points)
        """
        # Wallet Age (150 points)
        if wallet_age_months > 24:
            wallet_age_score = 150
        elif wallet_age_months > 12:
            wallet_age_score = 100
        elif wallet_age_months >= 6:
            wallet_age_score = 75
        elif wallet_age_months >= 3:
            wallet_age_score = 50
        else:
            wallet_age_score = 25

        # Transaction History Length (100 points)
        if transaction_count > 1000:
            tx_count_score = 100
        elif transaction_count >= 500:
            tx_count_score = 75
        elif transaction_count >= 100:
            tx_count_score = 50
        elif transaction_count >= 50:
            tx_count_score = 25
        else:
            tx_count_score = 10

        return wallet_age_score + tx_count_score


    @staticmethod
    def calculate_activity_factors(transaction_frequency: str, transaction_success_rate: float) -> int:
        """
        2. Activity Factors (Total: 250 points)
        - Transaction Frequency (100 points)
        - Transaction Success Rate (100 points)
        - Smart Contract Interactions (50 points) => You removed it in your snippet,
            so let's assume it's rolled into something else or not used for now.
        """
        # Transaction Frequency (100 points)
        freq_map = {
            "daily": 100,
            "weekly": 75,
            "monthly": 50,
            "quarterly": 25,
            "less_frequent": 10
        }
        frequency_score = freq_map.get(transaction_frequency.lower(), 10)

        # Transaction Success Rate (100 points)
        if transaction_success_rate > 98:
            success_score = 100
        elif transaction_success_rate >= 95:
            success_score = 75
        elif transaction_success_rate >= 90:
            success_score = 50
        elif transaction_success_rate >= 80:
            success_score = 25
        else:
            success_score = 0

        return frequency_score + success_score


    @staticmethod
    def calculate_financial_factors(balance_stability_ratio: float,
                                    nft_count: int, erc20_count: int,
                                    avg_tx_value_ratio: float) -> int:
        """
        3. Financial Factors (Total: 300 points)
        - Balance Stability (100 points) --> Omitted in your snippet, add if needed
        - Asset Diversity (50 points)
        - Value of Transactions (100 points)
        - Gas Payment History (50 points) --> Omitted in your snippet, add if needed
        """

        # EXAMPLE: Simplified Balance Stability Score (if you want to reintroduce it):
        if balance_stability_ratio >= 0.8:
            balance_stability_score = 100
        elif balance_stability_ratio >= 0.6:
            balance_stability_score = 75
        elif balance_stability_ratio >= 0.4:
            balance_stability_score = 50
        elif balance_stability_ratio >= 0.2:
            balance_stability_score = 25
        else:
            balance_stability_score = 0

        # Asset Diversity (50 points)
        if nft_count > 1 and erc20_count > 1:
            asset_diversity_score = 50
        elif erc20_count > 1 and nft_count <= 1:
            asset_diversity_score = 35
        elif nft_count == 1 and erc20_count == 1:
            asset_diversity_score = 25
        elif erc20_count == 1 and nft_count < 1:
            asset_diversity_score = 10
        else:
            asset_diversity_score = 0

        # Value of Transactions (100 points)
        if avg_tx_value_ratio > 2.0:
            tx_value_score = 100
        elif avg_tx_value_ratio >= 1.5:
            tx_value_score = 75
        elif avg_tx_value_ratio >= 1.0:
            tx_value_score = 50
        elif avg_tx_value_ratio >= 0.5:
            tx_value_score = 25
        else:
            tx_value_score = 10

        #Set it to 0 for now. Gas Payment History to be implemented once understood
        gas_payment_history_score = 0

        return (balance_stability_score + asset_diversity_score + tx_value_score + gas_payment_history_score)


    def compute_final_score(self, wallet_age_months: int, transaction_count: int,
                            transaction_frequency: str, transaction_success_rate: float, 
                            balance_stability_ratio: float, nft_count: int, erc20_count: int, 
                            avg_tx_value_ratio: float):

        """Calculate all partial scores, sum up, and return them."""
        try:
                
            historical_score = self.calculate_historical_factors(wallet_age_months, transaction_count)
            activity_score = self.calculate_activity_factors(transaction_frequency, transaction_success_rate)

            financial_score = self.calculate_financial_factors(
                balance_stability_ratio,
                nft_count,
                erc20_count,
                avg_tx_value_ratio
            )
            network_score = 0  # Not implemented untill understood

            total_score = historical_score + activity_score + financial_score + network_score
            return total_score, historical_score, activity_score, financial_score, network_score

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to process statistics: {str(e)}")
            raise DataProcessingError(f"Failed to process statistics: {str(e)}")


    @staticmethod
    def interpret_score(score: int):

        if score >= 800:
            risk_level = "Excellent Credit Score (Low Risk)"
            implications = "Eligible for highest lending amounts, lowest collateral requirements"
        elif score >= 650:
            risk_level = "Good Credit Score (Moderate Risk)"
            implications = "Standard lending terms, moderate collateral requirements"
        elif score >= 500:
            risk_level = "Fair Credit Score (Medium Risk)"
            implications = "Limited lending amounts, higher collateral requirements"
        elif score >= 350:
            risk_level = "Poor Credit Score (High Risk)"
            implications = "Very limited lending, high collateral requirements"
        else:
            risk_level = "Very Poor Credit Score (Very High Risk)"
            implications = "Likely requires 100%+ collateralization or ineligible for lending"

        return risk_level, implications


    @staticmethod
    def infer_transaction_frequency(transactions):
        """
        transactions: List of transaction objects that have a .created_at datetime.
        Returns: 'daily', 'weekly', 'monthly', or 'less_frequent'
        """
        now = datetime.now(timezone.utc)
        thirty_days_ago = now - timedelta(days=30)

        recent_txs = [tx for tx in transactions if tx.created_at >= thirty_days_ago]

        if not recent_txs:
            return "less_frequent"

        distinct_active_days = set(tx.created_at.date() for tx in recent_txs)
        active_days_count = len(distinct_active_days)

        if active_days_count >= 20:
            return "daily"
        elif active_days_count >= 8:
            return "weekly"
        elif active_days_count >= 2:
            return "monthly"
        else:
            return "less_frequent"
