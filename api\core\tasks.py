import asyncio
from celery import shared_task
from celery.schedules import crontab
from api.core.logging_config import get_logger
from datetime import datetime, timedelta
from typing import Dict, Any
from celery import Celery
from config import config
from functools import wraps

logger = get_logger(__name__)

celery_app = Celery(
    "atlas_worker",
    broker=config.REDIS_URL,
    backend=config.REDIS_URL,
    include=[
        "api.v1.websockets.connection_manager",
        "api.v1.deploy_contracts.sign_notifications",
        "api.db.db_task",
        "api.v1.auth.services",
        "api.core.tasks",
        "api.v1.auth.services"
    ]
)

# Task queues configuration
task_queues = {
    'celery': {
        'exchange': 'celery',
        'exchange_type': 'direct',
        'routing_key': 'celery'
    },
    'monitoring': {
        'exchange': 'monitoring',
        'exchange_type': 'direct',
        'routing_key': 'monitoring'
    },
    'maintenance': {
        'exchange': 'maintenance',
        'exchange_type': 'direct',
        'routing_key': 'maintenance'
    }
}

# Task routing configuration
task_routes = {
    'tasks.monitor_database_health': {'queue': 'monitoring'},
    'tasks.vacuum_analyze': {'queue': 'maintenance'},
    'tasks.create_database_backup': {'queue': 'maintenance'},
    'tasks.cleanup_old_backups_task': {'queue': 'maintenance'},
    'tasks.send_notification': {'queue': 'celery'},
    'tasks.process_notification': {'queue': 'celery'},
    'tasks.process_signed_trx_notification': {'queue': 'celery'},
    'tasks.create_free_subscription': {'queue': 'celery'},
}

celery_app.conf.update(
    task_serializer="json",
    result_serializer="json",
    accept_content=["json"],
    timezone="UTC",
    enable_utc=True,
    
    task_queues=task_queues,
    task_routes=task_routes,
    task_default_queue="default",
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=50,
    #worker_pool="gevent",
    task_track_started=True,
    
    broker_connection_timeout=10,
    broker_connection_retry=True,
    broker_connection_max_retries=3,
    task_soft_time_limit=300,
    task_time_limit=600,
)

"""
celery_app.conf.beat_schedule = {
    'daily-database-backup': {
        'task': 'create_database_backup',
        'schedule': crontab(hour=2, minute=0),
        'options': {'queue': 'maintenance', 'expires': 7200}
    },
    'cleanup-old-backups': {
        'task': 'cleanup_old_backups_task',
        'schedule': crontab(hour=3, minute=0, day_of_week="sun"),
        'options': {'queue': 'maintenance', 'expires': 3600}
    },
    'vacuum-analyze-task': {
        'task': 'tasks.vacuum_analyze',
        'schedule': crontab(hour=3, minute=30),
        'options': { 'queue': 'maintenance', 'expires': 3600 }
    },
    'database-health-monitor': {
        'task': 'tasks.monitor_database_health',
        'schedule': timedelta(minutes=15),
        'options': { 'queue': 'monitoring', 'expires': 300 }

    },
}
"""


celery_app.conf.beat_schedule = {
    "logical-backup": {
        "task": "create_database_backup",
        "schedule": crontab(hour=2, minute=0),
        "options": {"queue": "maintenance", "expires": 7200}
    },
    "cleanup-backup-archives": {
        "task": "cleanup_old_backups_task",
        "schedule": crontab(hour=3, minute=0, day_of_week="sun"),
        "options": {"queue": "maintenance", "expires": 3600}
    }
}




