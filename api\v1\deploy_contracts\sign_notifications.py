from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from .models import ToBeSigned
from api.v1.account.models import Account as AccountWallet
from .exceptions import logger, NotificationError
from api.v1.websockets.services import NotificationService
from celery.utils.log import get_task_logger
import json
from api.db.database import SessionLocal
from api.v1.websockets.models import NotificationType, NotificationPriority
from api.v1.websockets.schemas import NotificationCreate
from api.core.tasks import celery_app
from sqlalchemy.future import select
from sqlalchemy.exc import NoResultFound, MultipleResultsFound
from celery.exceptions import MaxRetriesExceededError, Ignore
from asgiref.sync import async_to_sync
#from celery import shared_task

task_logger = get_task_logger(__name__)


def validate_tx_data(tx_data: Dict[str, Any]) -> bool:
    """Validate transaction data structure"""
    required_fields = ['type', 'transaction']
    return all(field in tx_data for field in required_fields)

def get_recipient_address(tx_data: Dict[str, Any]) -> Optional[str]:
    """Extract recipient address from transaction data"""
    address_fields = ['to_address', 'recipient_address', 'spender_address']
    for field in address_fields:
        if address := tx_data.get(field):
            return address.lower()
    return None

@celery_app.task(
    name="tasks.process_signed_trx_notification",
    bind=True,
    max_retries=3,
    default_retry_delay=15,
    retry_backoff=True
)
def signed_notification(self, signature_id: str) -> bool:
    """Process notification after transaction is signed"""
    task_logger.info(f"Processing notification for signature ID: {signature_id}")

    try:
        # Run the async function in a new event loop
        import asyncio
        return asyncio.run(_process_signed_notification(self, signature_id))
        #return async_to_sync(_process_signed_notification)(self, signature_id)
    except Ignore:
        # Task is marked as failed but not retried (e.g., invalid input)
        task_logger.warning(f"Task for signature ID {signature_id} ignored (non-retryable failure).")
        return False
    except Exception as e:
        task_logger.error(f"Error processing notification: {e}")
        # Retry the task with exponential backoff
        self.retry(exc=e)

async def _process_signed_notification(task_self, signature_id: str) -> bool:
    """Async implementation of the signed notification processing"""
    try:
        async with SessionLocal() as db:
            # Get pending transaction
            stmt_pending = select(ToBeSigned).filter(
                ToBeSigned.id == signature_id,
                ToBeSigned.status == "signed"
            )
            result_pending = await db.execute(stmt_pending)
            pending_tx = result_pending.scalars().one_or_none()

            if not pending_tx:
                task_logger.error(f"Signed transaction {signature_id} not found or not in signed status")
                return False

            try:
                tx_data = json.loads(pending_tx.operation) if pending_tx.operation else {}
                if not tx_data or not validate_tx_data(tx_data):
                    task_logger.error(f"Invalid or missing transaction data structure in operation field for {signature_id}")
                    raise Ignore()
            except json.JSONDecodeError as e:
                task_logger.error(f"Invalid JSON in operation field for {signature_id}: {e}")
                raise Ignore()

            notification_service = NotificationService()

            # Send notification to sender
            try:
                sender_message = build_transaction_message(tx_data, True)
                if sender_message:
                    notification_service.publish_notification(
                        user_id=pending_tx.user_id,
                        notification=sender_message,
                    )
                task_logger.info(f"Sender notification sent to user {pending_tx.user_id}")
            except Exception as e:
                task_logger.error(f"Failed to send sender notification: {e}")
                raise NotificationError(f"Sender notification failed: {str(e)}")

            # Process recipient notification if applicable
            if recipient_address := get_recipient_address(tx_data):
                try:
                    stmt_recipient = select(AccountWallet).filter(
                        AccountWallet.address.ilike(recipient_address)
                    )
                    result_recipient = await db.execute(stmt_recipient)
                    recipient_wallet = result_recipient.scalars().one_or_none()

                    if recipient_wallet and recipient_wallet.user_id != pending_tx.user_id:
                        recipient_message = build_transaction_message(tx_data, False)
                        if recipient_message:
                            notification_service.publish_notification(
                                user_id=recipient_wallet.user_id,
                                notification=recipient_message,
                            )
                            task_logger.info(f"Recipient notification sent to user {recipient_wallet.user_id}")
                except Exception as e:
                    task_logger.error(f"Failed to send recipient notification: {e}")

            return True
    except Ignore as e:
        raise e
    except Exception as e:
        task_logger.error(f"Error in async processing: {e}")
        raise e





def build_transaction_message(tx_data: Dict[str, Any], sender: bool = True) -> str:
    """Build meaningful message based on transaction type"""
    try:
        metadata = tx_data.get("metadata", {})
        notification = tx_data.get("notification", {})
        if sender:
            message = notification.get("from_description", "Transaction processed successfully")
        else:
            message = notification.get("to_description", "Transaction received successfully")

        if notification:
            trx_notification = NotificationCreate(
                title=notification.get("title", "Transaction Update"),
                message=message,
                type=NotificationType.TRANSACTION.value,
                priority=NotificationPriority.NORMAL.value,
                metadata=metadata,
                action_url=notification.get("action_url"),
                )
        else:
            trx_notification = NotificationCreate(
                title="Transaction Update",
                message= "Transaction processed",
                type= NotificationType.TRANSACTION.value,
                priority=NotificationPriority.NORMAL.value,
                metadata=None,
                action_url=None,
                )
        return trx_notification

        # Fallback to building message from metadata
    except Exception as e:
        logger.error(f"Error building transaction message: {e}")

        notification = NotificationCreate(
            title="Transaction Update",
            message= "Transaction processed",
            type= NotificationType.TRANSACTION.value,
            priority=NotificationPriority.NORMAL.value,
            metadata=None,
            action_url=None,
            )

        return notification
