aiofiles==24.1.0
aiohappyeyeballs==2.4.3
aiohttp==3.11.9
aiosignal==1.3.1
alchemy-sdk==0.1.1
alembic==1.14.0
amqp==5.3.1
annotated-types==0.7.0
anyio==4.6.2.post1
argcomplete==3.5.3
asgiref==3.8.1
asyncpg==0.30.0
asyncstdlib==3.13.1
attrs==24.2.0
Authlib==1.3.2
autobahn==24.4.2
Automat==24.8.1
azure-core==1.32.0
azure-storage-blob==12.25.1
backoff==2.2.1
bcrypt==4.3.0
billiard==4.2.1
bitarray==3.0.0
cachetools==5.5.0
celery==5.4.0
certifi==2024.8.30
cffi==1.17.1
channels==4.1.0
chardet==5.2.0
charset-normalizer==3.3.2
ckzg==2.0.1
click==8.1.7
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
colorama==0.4.6
constantly==23.10.4
contourpy==1.3.1
coverage==7.6.7
cryptography==43.0.1
cssselect==1.3.0
cssutils==2.11.1
cycler==0.12.1
cytoolz==1.0.0
daphne==4.1.2
databases==0.9.0
dataclass-wizard==0.35.0
dj-database-url==2.2.0
Django==5.1
django-autoslug==1.9.9
django-cities-light==3.10.1
django-ckeditor==6.7.1
django-crontab==0.7.1
django-js-asset==2.2.0
django-widget-tweaks==1.5.0
djangorestframework==3.15.2
dnspython==2.7.0
dparse==0.6.4
ecdsa==0.19.0
email_validator==2.2.0
emails==0.6
eth-account==0.13.4
eth-hash==0.7.0
eth-keyfile==0.8.1
eth-keys==0.6.0
eth-rlp==2.1.0
eth-typing==5.0.1
eth-utils==5.1.0
eth_abi==5.2.0
eventlet==0.39.1
fastapi==0.115.5
fastapi-cli==0.0.5
filelock==3.12.4
fonttools==4.55.3
frozendict==2.3.10
frozenlist==1.5.0
gevent==24.11.1
greenlet==3.1.1
gunicorn==23.0.0
h11==0.14.0
haversine==2.8.1
hexbytes==1.2.1
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
hyperlink==21.0.0
idna==3.8
incremental==24.7.2
iniconfig==2.0.0
isodate==0.7.2
itsdangerous==2.2.0
Jinja2==3.1.4
jose==1.0.0
jwt==1.3.1
kiwisolver==1.4.7
kombu==5.4.2
lru-dict==1.3.0
lxml==5.3.1
Mako==1.3.6
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.23.1
matplotlib==3.10.0
mdurl==0.1.2
more-itertools==10.6.0
multidict==6.1.0
ngrok==1.4.0
numpy==2.2.0
orjson==3.10.11
packaging==24.1
pandas==2.2.3
parsimonious==0.10.0
passlib==1.7.4
pillow==10.4.0
pipx==1.7.1
platformdirs==4.3.6
pluggy==1.5.0
premailer==3.10.0
progressbar2==4.5.0
prometheus_client==0.21.1
prompt_toolkit==3.0.50
propcache==0.2.0
psutil==6.0.0
psycopg2-binary==2.9.9
py-solc-x==1.1.1
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.9.2
pydantic-extra-types==2.10.0
pydantic-settings==2.6.1
pydantic_core==2.23.4
Pygments==2.18.0
pymongo==4.10.1
PyMySQL==1.1.1
pyOpenSSL==24.2.1
pyparsing==3.2.0
pytest==8.3.3
pytest-asyncio==0.26.0
python-dateutil==2.9.0.post0
python-decouple==3.8
python-dotenv==1.0.1
python-jose==3.3.0
python-multipart==0.0.17
python-utils==3.9.0
pytz==2024.2
pyunormalize==16.0.0
pyWalletConnect==1.6.2
#pywin32==308
PyYAML==6.0.2
redis==5.2.1
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rlp==4.0.1
rsa==4.9
ruamel.yaml==0.18.6
ruamel.yaml.clib==0.2.12
safety==3.2.11
safety-schemas==0.0.9
semantic-version==2.10.0
service-identity==24.1.0
setuptools==75.7.0
shellingham==1.5.4
six==1.16.0
sniffio==1.3.1
SQLAlchemy==2.0.36
sqlparse==0.5.1
starlette==0.41.3
starlette-prometheus==0.10.0
stripe==11.3.0
tenacity==9.0.0
toolz==1.0.0
tqdm==4.67.1
Twisted==24.7.0
txaio==23.1.1
typer==0.13.1
types-requests==2.32.0.20241016
typing_extensions==4.12.2
tzdata==2024.1
ua-parser==1.0.1
ua-parser-builtins==0.18.0.post1
ujson==5.10.0
Unidecode==1.3.8
upstash-redis==1.2.0
urllib3==2.2.2
user-agents==2.2.0
userpath==1.9.2
uvicorn==0.18.2
vine==5.1.0
watchfiles==0.24.0
wcwidth==0.2.13
web3==7.10.0
websockets==13.1
wheel==0.45.1
whitenoise==6.7.0
wsproto==1.2.0
yarl==1.18.0
zope.event==5.0
zope.interface==7.0.3
