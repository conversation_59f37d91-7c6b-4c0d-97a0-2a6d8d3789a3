from sqlalchemy.ext.asyncio import AsyncSession
from api.v1.user import schemas as user_schema
from fastapi import Depends, Form, APIRouter, Depends, UploadFile, File, HTTPException, Query
from api.core.dependencies import is_authenticated
from .services import DeployErc20
from .services_sign import SignTransaction
from api.db.database import get_db
from .exceptions import handle_deploy_notification, handle_deployment_error, logger
from .models import TransactionType
from .compile_erc20 import Compile, Compile721
from typing import Optional
from api.v1.contracts.schemas import Order, TimeData
from api.core.general_dep import get_redis
from redis.asyncio import Redis
from web3 import AsyncWeb3
from api.core.blockchain_dep import get_web3
from .sign_notifications import signed_notification

app = APIRouter(tags=["Deploy Contracts"])


@app.post("/erc20/compile")
async def compile_erc20(user: user_schema.User = Depends(is_authenticated),
                        db: AsyncSession = Depends(get_db)):
    try:

        compiled_contract = await Compile.create()
        erc20_contract = await compiled_contract.compile()

        action = {
            "type": "compile",
            "contract": "ERC20",
        }
        handle_deploy_notification(
            user_id=user.id,
            action=action
        )
        return erc20_contract
    except Exception as e:
        handle_deployment_error(e)

@app.post("/erc721/compile")
async def compile_erc721(user: user_schema.User = Depends(is_authenticated),
                        db: AsyncSession = Depends(get_db)):

    try:

        compiled_contract = await Compile721.create()
        erc721_contract = await compiled_contract.compile()

        action = {
            "type": "compile",
            "contract": "ERC721",
        }
        handle_deploy_notification(
            user_id=user.id,
            action=action
        )

        return erc721_contract

    except Exception as e:
        handle_deployment_error(e)


@app.get("/deploy/available")
async def get_available_contract(user: user_schema.User = Depends(is_authenticated)):
    try:
        return {transaction.name: transaction.value for transaction in TransactionType if transaction.value != "none" and transaction.value != "none"}
    except Exception as e:
        handle_deployment_error(e)


@app.get("/deploy/code")
async def get_contract_code(contract_type: TransactionType,
                            db: AsyncSession = Depends(get_db),
                            user: user_schema.User = Depends(is_authenticated),
                            web3: AsyncWeb3 = Depends(get_web3),
                            redis: Optional[Redis] = Depends(get_redis)):

    try:
        contract = await DeployErc20.create(db=db, user_id=user.id, web3=web3,
                               transaction_type=None, redis=redis)
        contract_code = await contract.get_contract_code(contract_type)
        return contract_code
    except Exception as e:
        handle_deployment_error(e)


@app.post("/erc20/deploy")
async def deploy_erc20(name: str, symbol: str, description: Optional[str] = File(None),
                        file: Optional[UploadFile] = File(None),
                        default_wallet:Optional[bool]=True,
                        user: user_schema.User = Depends(is_authenticated),
                        db: AsyncSession = Depends(get_db),
                        web3: AsyncWeb3 = Depends(get_web3),
                        redis: Optional[Redis] = Depends(get_redis)):
    try:
        compiled_contract = await DeployErc20.create(db=db, user_id=user.id, web3=web3,
                               transaction_type=TransactionType.ER20, redis=redis)
        deployed_contract = await compiled_contract.deploy(name, symbol, description, file, default_wallet)
        return deployed_contract

    except Exception as e:
        handle_deployment_error(e)



@app.post("/erc721/deploy")
async def deploy_erc721(name: str, symbol: str, description: Optional[str] = File(None),
                        file: Optional[UploadFile] = File(None),
                        default_wallet:Optional[bool]=True,
                        user: user_schema.User = Depends(is_authenticated),
                        db: AsyncSession = Depends(get_db),
                        web3: AsyncWeb3 = Depends(get_web3),
                        redis: Optional[Redis] = Depends(get_redis)):
    try:
        compiled_contract = await DeployErc20.create(db=db, user_id=user.id, web3=web3,
                               transaction_type=TransactionType.ERC721, redis=redis)
        deployed_contract = await compiled_contract.deploy(name, symbol, description, file, default_wallet)
        return deployed_contract

    except Exception as e:
        handle_deployment_error(e)

@app.get("/users/get_user_contracts")
async def get_user_contracts(page:Optional[int]=Query(1, ge=1), page_size:Optional[int]=Query(10, ge=1),
                             order:Optional[Order]="desc", user: user_schema.User = Depends(is_authenticated),
                             db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                             redis: Optional[Redis] = Depends(get_redis)):

    try:
        compiled_contract = await DeployErc20.create(db=db, user_id=user.id, web3=web3,
                               transaction_type=None, redis=redis)
        deployed_contract = await compiled_contract.get_all_contracts(page, page_size, order)

        return deployed_contract
    except Exception as e:
        handle_deployment_error(e)



@app.get("/users/get_user_contracts/chart")
async def get_user_contract_chart(
    freq: TimeData,
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db),
    web3: AsyncWeb3 = Depends(get_web3),
    redis: Optional[Redis] = Depends(get_redis)
):
    """
    Get contract earnings chart with specified frequency
    freq options: 'h' (hourly), 'd' (daily), 'W' (weekly), 'ME' (monthly)
    """
    try:
        compiled_contract = await DeployErc20.create(db=db, user_id=user.id, web3=web3,
                               transaction_type=None, redis=redis)
        deployed_contract = await compiled_contract.get_contract_charts(freq)
        return deployed_contract

    except Exception as e:
        handle_deployment_error(e)



@app.get("/users/get_all_erc20_contracts")
async def get_all_erc20_contracts(page:Optional[int]=Query(1, ge=1), page_size:Optional[int]=Query(10, ge=1),
                                order:Optional[Order]="desc", user: user_schema.User = Depends(is_authenticated),
                                db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3), redis: Optional[Redis] = Depends(get_redis)):
    try:
        compiled_contract = await DeployErc20.create(db=db, user_id=user.id, web3=web3,
                               transaction_type=TransactionType.ER20, redis=redis)
        deployed_contract = await compiled_contract.get_all_erc20_contracts(page, page_size, order)

        return deployed_contract
    except Exception as e:
        handle_deployment_error(e)

@app.get("/users/get_all_erc721_contracts")
async def get_all_erc721_contracts(page:Optional[int]=Query(1, ge=1), page_size:Optional[int]=Query(10, ge=1),
                                order:Optional[Order]="desc", user: user_schema.User = Depends(is_authenticated),
                                db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                                redis: Optional[Redis] = Depends(get_redis)):
    try:
        compiled_contract = await DeployErc20.create(db=db, user_id=user.id, web3=web3,
                               transaction_type=TransactionType.ERC721, redis=redis)
        deployed_contract = await compiled_contract.get_all_erc721_contracts(page, page_size, order)

        return deployed_contract

    except Exception as e:
        handle_deployment_error(e)

@app.get("/users/get_contract")
async def get_contract(contract_id: int, user: user_schema.User = Depends(is_authenticated),
                        db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                        redis: Optional[Redis] = Depends(get_redis)):
    try:

        compiled_contract = await DeployErc20.create(db=db, user_id=user.id, web3=web3,
                               transaction_type=None, redis=redis)
        deployed_contract = await compiled_contract.get_contract(contract_id)

        return deployed_contract

    except Exception as e:
        handle_deployment_error(e)



@app.post("/sign/deploy/{signature_id}")
async def sign_transaction(signature_id: int, contract: TransactionType,
                           trx_hash:Optional[str]=None, user: user_schema.User = Depends(is_authenticated),
                           db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                           redis: Optional[Redis] = Depends(get_redis)):
    try:
        #compiled_contract = DeployErc20(db, user.id, contract, redis=redis)
        compiled_contract = await DeployErc20.create(db=db, user_id=user.id, web3=web3,
                               transaction_type=contract, redis=redis)

        deployed_contract = await compiled_contract.sign_and_send_transaction(transaction_id=signature_id, trx_hash=trx_hash)
        action = {
            "contract": contract.value if hasattr(contract, 'value') else str(contract),
            "type": "deploy"
        }
        handle_deploy_notification(
            user_id=user.id,
            action=action
        )
        return deployed_contract

    except Exception as e:
        handle_deployment_error(e)


@app.post("/sign/transaction/{signature_id}")
async def sign_transaction(contract_id: int, signature_id: int, trx_hash:Optional[str]=None,
                            user: user_schema.User = Depends(is_authenticated), db: AsyncSession = Depends(get_db),
                            web3: AsyncWeb3 = Depends(get_web3), redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await SignTransaction.create(db=db, user_id=user.id, web3=web3, redis=redis)

        tx_result = await contract.transaction_sign(contract_id=contract_id, signature_id=signature_id, trx_hash=trx_hash)
        if tx_result.receipt.status == "success":
            try:
                signed_notification.delay(signature_id)
            except Exception as e:
                logger.error(f"Sign notification queuing failed: {str(e)}")
                return False
        return tx_result

    except Exception as e:
        handle_deployment_error(e)