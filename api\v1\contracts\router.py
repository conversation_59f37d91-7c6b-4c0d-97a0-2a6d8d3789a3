from fastapi import Depends, <PERSON><PERSON>, H<PERSON>P<PERSON>x<PERSON>, APIRouter, Response
from api.core.dependencies import is_authenticated
from .services import Chain<PERSON>
from .services_general_chart import GeneralChart
from api.v1.user import schemas as user_schema
from .schemas import Order, TimeData
from sqlalchemy.orm import Session
from api.db.database import get_db
from .services_events import BlockEvents
from .services_contract_chart import ContractChat
from api.core.general_dep import get_redis
from redis.asyncio import Redis
from typing import Optional
from web3 import AsyncWeb3
from api.core.blockchain_dep import get_web3

app = APIRouter(tags=["Contracts"])
CACHE_TIME = 600



@app.get("/contract-events")
async def get_events(contract_id: int, order: Order, 
                    response: Response, page: int = 1, 
                    page_size: int = 10, db: Session = Depends(get_db), 
                    user: user_schema.User = Depends(is_authenticated),
                
    web3: AsyncWeb3 = Depends(get_web3),
    redis: Optional[Redis] = Depends(get_redis)
                    ):
    
    if order not in ["asc", "desc"]:
        raise HTTPException(status_code=400, detail={"status": "error", "message": "Invalid order parameter. Use 'asc' or 'desc'."})
    
    blockchain_service = await BlockEvents.create(db, user.id, contract_id=contract_id, redis=redis)
    transactions = await blockchain_service.get_contract_events(
                                    order=order, page=page, page_size=page_size)


    if transactions.get('source') == 'cache':
        response.headers["Cache-Control"] = f"public, max-age={CACHE_TIME}"
    else:
        response.headers["Cache-Control"] = "no-cache"

    return transactions


@app.get("/network/id")
async def get_network_id(user: user_schema.User = Depends(is_authenticated), 
                        web3: AsyncWeb3 = Depends(get_web3),
                        redis: Optional[Redis] = Depends(get_redis)):

    blockchain_service = ChainID(user_id=user.id, web3=web3, redis=redis)
    contract_id = await blockchain_service.get_network_id()

    return {"network": contract_id}
    

@app.get("/contract/chart")
async def get_contract_chart(
    freq: TimeData, contract_id: int,
    response: Response, user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
    redis: Optional[Redis] = Depends(get_redis)
):
    """
    Get contract earnings chart with specified frequency
    freq options: 'h' (hourly), 'd' (daily), 'W' (weekly), 'ME' (monthly)
    """

    get_data = await ContractChat.create(db=db, user_id=user.id, 
                            contract_id=contract_id, 
                            web3=web3, redis=redis)
    chart_data = await get_data.get_data_chart(freq=freq)
    
    # Set cache control headers
    if chart_data.get('source') == 'cache':
        response.headers["Cache-Control"] = "public, max-age={CACHE_TIME}"
    else:
        response.headers["Cache-Control"] = "no-cache"
        
    return chart_data



@app.get("/contract/analytics/")
async def get_analytics_chart(freq: TimeData, contract_id: int, response: Response,
                            user: user_schema.User = Depends(is_authenticated),
                            db: Session = Depends(get_db), 
                            web3: AsyncWeb3 = Depends(get_web3),
                            redis: Optional[Redis] = Depends(get_redis)):

    """
    NOTE: this statistics with specified frequency
    freq options: 'h' (hourly), 'd' (daily), 'W' (weekly), 'ME' (monthly)
    """
 
    get_data = await ContractChat.create(db=db, user_id=user.id, 
                            contract_id=contract_id, 
                            web3=web3, redis=redis)
    chart_data = await get_data.get_analytics_chart(freq=freq)

    return chart_data




@app.get("/contract/users/")
async def get_contract_users_chart(freq: TimeData, contract_id: int, response: Response,
                                user: user_schema.User = Depends(is_authenticated),
                                db: Session = Depends(get_db),
                                web3: AsyncWeb3 = Depends(get_web3),
                                redis: Optional[Redis] = Depends(get_redis)):

    """
    NOTE: this statistics with specified frequency
    freq options: 'h' (hourly), 'd' (daily), 'W' (weekly), 'ME' (monthly)
    """

    get_data = await ContractChat.create(db=db, user_id=user.id, 
                            contract_id=contract_id, 
                            web3=web3, redis=redis)
    chart_data = await get_data.get_contract_users(freq=freq)

    return chart_data








@app.get("/contract/users/history")
async def get_history_users_chart(freq: TimeData, contract_id: int, response: Response,
                                user: user_schema.User = Depends(is_authenticated),
                                db: Session = Depends(get_db),
                                web3: AsyncWeb3 = Depends(get_web3),
                                redis: Optional[Redis] = Depends(get_redis)):

    """
    NOTE: this statistics with specified frequency
    freq options: 'h' (hourly), 'd' (daily), 'W' (weekly), 'ME' (monthly)
    """

    get_data = await ContractChat.create(db=db, user_id=user.id, 
                            contract_id=contract_id, 
                            web3=web3, redis=redis)
    chart_data = await get_data.get_history_chart(freq=freq)

    return chart_data




@app.get("/contract/unique-users")
async def get_contract_unique_users(
    contract_id: int,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    web3: AsyncWeb3 = Depends(get_web3),
    redis: Optional[Redis] = Depends(get_redis)
):
    """Get unique users who have interacted with the contract"""
    contract_chat = await ContractChat.create(
        db=db, 
        user_id=user.id,
        contract_id=contract_id,
        web3=web3,
        redis=redis
    )
    return await contract_chat.get_unique_users()





# Initialize your data handler
@app.post("/earning-chart")
async def earnings_chart(freq: TimeData, response: Response,
                        user: user_schema.User = Depends(is_authenticated),
                        db: Session = Depends(get_db),   
                        web3: AsyncWeb3 = Depends(get_web3),
                        redis: Optional[Redis] = Depends(get_redis)):
    """
    NOTE: this statistics with specified frequency
    freq options: 'h' (hourly), 'd' (daily), 'W' (weekly), 'ME' (monthly)
    """
    get_data = GeneralChart(db=db, user_id=user.id, web3=web3, redis=redis)
    chart_data = await get_data.earnings_chart(freq=freq)

    return chart_data
    


@app.post("/history-chart")
async def history_chart(freq: TimeData, response: Response,
                        user: user_schema.User = Depends(is_authenticated),
                        db: Session = Depends(get_db),            
                        web3: AsyncWeb3 = Depends(get_web3),
                        redis: Optional[Redis] = Depends(get_redis)):

    """
    NOTE: this statistics with specified frequency
    freq options: 'h' (hourly), 'd' (daily), 'W' (weekly), 'ME' (monthly)
    """
    get_data = GeneralChart(db=db, user_id=user.id, web3=web3, redis=redis)
    chart_data = await get_data.history_chart(freq=freq)

    return chart_data



@app.get("/contract/metrics/")
async def get_contract_metrics(
    contract_id: int,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    redis: Optional[Redis] = Depends(get_redis)

):
    """
    Get key contract metrics:
    - Total Net Worth (current contract balance)
    - Total Transactions
    - Total Gas Spent
    - Number of Unique Users
    - Per User Value
    - Average Holding
    """
    get_data = await BlockEvents.create(db=db, user_id=user.id, contract_id=contract_id, redis=redis)
    metrics = await get_data.get_contract_metrics()
    return metrics
