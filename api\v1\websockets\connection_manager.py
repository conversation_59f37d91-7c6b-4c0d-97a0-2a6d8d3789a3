from fastapi import WebSocket, WebSocketDisconnect
from typing import Dict, List, Optional
import json
from datetime import datetime, timedelta, timezone
import asyncio
from dataclasses import dataclass
from .schemas import WebSocketSchema
from celery import Celery
from .models import Notification
from api.db.database import SessionLocal
import redis
from config import config
from .exceptions import logger, DatabaseError, InfrastructureError, NotificationError
from api.core.tasks import celery_app
from celery.exceptions import MaxRetriesExceededError, Ignore
from sqlalchemy.exc import SQLAlchemyError, IntegrityError
from celery.utils.log import get_task_logger
from asgiref.sync import async_to_sync
#from celery import shared_task


NOTIFICATION_CHANNEL = "notifications"
REDIS_URL = config.REDIS_URL


class ConnectionManager:

    def __init__(self, max_connection:int=5, heartbeat_intervel=30, connection_timeout=60):
        self._active_connections: Dict[int, List[WebSocketSchema]] = {}
        self._user_connecitons: Dict[WebSocket, int] = {}
        self.max_connections = max_connection
        self.heartbeat_interval = heartbeat_intervel
        self.connection_timeout = connection_timeout
        self._cleanup_task: Optional[asyncio.Task] = None


    async def start(self):
        if not self._cleanup_task or self._cleanup_task.done(): # Prevent multiple starts
            self._cleanup_task = asyncio.create_task(self._clean_loop())
            logger.info("ConnectionManager cleanup loop started.")
        else:
            logger.warning("ConnectionManager cleanup loop already running.")

    async def stop(self):
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        await self._close_all_connections()
        logger.info("Connection manager stopped")


    async def connect(self, websocket: WebSocket, user_id:int) -> bool:
        """Handle new connection with rate limiting and max connection checks"""
        try:
            if self._get_user_connection_count(user_id) >= self.max_connections:
                await websocket.close(code=1008, reason="Too many connections")
                return False
            
            await websocket.accept()
            connection = WebSocketSchema(
                socket=websocket,
                created_at=datetime.now(),
                last_heartbeat=datetime.now()
            )
            self._active_connections.setdefault(user_id, []).append(connection)
            self._user_connecitons[websocket] = user_id
            logger.info(f"New connection for user {user_id}, total connections: {self._get_user_connection_count(user_id)}")
            return True
        except Exception as e:
            logger.error(f"Error connecting to websocket: {str(e)}")
            return False

    async def disconnect(self, websocket:WebSocket):
        try:
            user_id = self._user_connecitons.pop(websocket, None)
            if user_id:
                connections = self._active_connections[user_id]
                connections = [conn for conn in connections if conn.socket != websocket]
                if connections:
                    self._active_connections[user_id] = connections
                else:
                    self._active_connections.pop(user_id, None)
                await websocket.close()
                logger.info(f"Disconnected user {user_id}, remaining connections: {self._get_user_connection_count(user_id)}")
        except Exception as e:
            logger.error(f"Error disconnecting from websocket: {str(e)}")


    async def send_notification(self, user_id:int, notifications: Dict, notification_id:int):
        """Send notifications to all connected users for a given user_id"""
        try:    
            if user_id not in self._active_connections:
                return 0
            successful_sends = 0
            #disconnected_sockets: List[WebSocketSchema] = []
            disconnected_sockets = []
            parsed_notification = notifications if isinstance(notifications, dict) else json.loads(notifications)

            notification_payload = {
                "type": "notification",
                "id": notification_id,
                "data": parsed_notification
            }
            
            for conn in self._active_connections[user_id]:
                try:
                    await conn.socket.send_json(notification_payload)
                    successful_sends += 1
                    conn.last_heartbeat = datetime.now()
                except WebSocketDisconnect:
                    disconnected_sockets.append(conn)
                except Exception as e:
                    logger.error(f"Failed to send notification to user {user_id}: {str(e)}")
                    disconnected_sockets.append(conn)
            
            for conn in disconnected_sockets:
                try:
                    await self.disconnect(conn)
                except Exception as e:
                    logger.error(f"Error disconnecting socket: {str(e)}")

            return successful_sends
        except Exception as e:
            logger.error(f"Error in send_notification: {str(e)}")
            raise

    async def _clean_loop(self):
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                await self._cleanup_stale_connections()
                await asyncio.sleep(self.heartbeat_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {str(e)}", exc_info=True)
                asyncio.sleep(10)


    def _get_user_connection_count(self, user_id):
        return len(self._active_connections.get(user_id, []))


    async def _close_all_connections(self):
        for user in self._active_connections.keys():
            for conn in self._active_connections[user]:
                await self.disconnect(conn.socket)


    async def _cleanup_stale_connections(self):
        """Remove stale connections"""
        try:    
            now = datetime.now()
            stale_threshold = now - timedelta(seconds=self.connection_timeout)
            disconnecting_sockets = []

            for user_id, connections in list(self._active_connections.items()):

                stale_sockets = [
                    conn for conn in connections 
                    if conn.last_heartbeat < stale_threshold
                ]

                if stale_sockets:
                    disconnecting_sockets.extend(stale_sockets)
                        
            for conncet in disconnecting_sockets:
                logger.info(f"Cleaning up stale connection from {conncet.created_at}")
                await self.disconnect(conncet.socket)
                
            if disconnecting_sockets:
                logger.info(f"Cleaned up {len(disconnecting_sockets)} stale connections")
                
        except Exception as e:
            logger.error(f"Error in cleanup: {str(e)}", exc_info=True)



@celery_app.task(
    name="tasks.send_notification",
    bind=True,
    max_retries=3,
    default_retry_delay=15
)
def send_notification_task(self, user_id:int, notification:Dict, notification_id:int):
    """
    Trigger the delivery via websocket
    """
    task_logger = get_task_logger(__name__)
    redis_client = redis.Redis.from_url(REDIS_URL, decode_responses=True)
    try:
        payload = {
            "user_id": user_id,
            "message": notification,
            "notification_id": notification_id
        }
        redis_client.publish(NOTIFICATION_CHANNEL, json.dumps(payload))

        task_logger.info(f"Notification {notification['message']} queued for user {user_id}")
        return True
    except Exception as e:
        task_logger.error(f"Failed to send notification: {str(e)}", exc_info=True)
        raise self.retry(exc=e)
    finally:
        redis_client.close()
        task_logger.info(f"Successfully sent notification {notification_id} to user {user_id}")



@celery_app.task(
    name="tasks.process_notification",
    bind=True,
    max_retries=3,
    default_retry_delay=15,
    autoretry_for=(DatabaseError, InfrastructureError, NotificationError), # Auto-retry specific errors
    retry_backoff=True,
)
def process_notification_task(self, user_id:int, noti:dict):
    """
    Saves notification to DB and queues delivery task.
    """
    task_logger = get_task_logger(__name__)
    notification_id = None

    async def _process_and_commit():
        nonlocal notification_id
        
        async with SessionLocal() as session:

            notification_obj = Notification(
                user_id=user_id,
                title=noti.get("title", "Notification"),
                message=noti.get("message", ""),
                type=noti.get("type"),
                priority=noti.get("priority"),
                noti_metadata=noti.get("metadata"),
                action_url=noti.get("action_url")
            )
            session.add(notification_obj)
            await session.flush([notification_obj])
            notification_id = notification_obj.id

            if notification_id is None:
                raise DatabaseError("Failed to obtain notification ID after flush.")

            task_logger.info(f"Notification {notification_id} for user {user_id} added to session.")

            delivery_payload = {
                "title": notification_obj.title,
                "message": notification_obj.message,
                "type": notification_obj.type,
                "priority": notification_obj.priority,
                "is_read": notification_obj.is_read,
                "created_at": datetime.now(timezone.utc).isoformat()
            }

            try:
                send_notification_task.delay(
                    user_id=user_id,
                    notification=delivery_payload,
                    notification_id=notification_id
                )
                task_logger.info(f"Delivery task queued for notification ID: {notification_id}")
            except Exception as queue_err:
                task_logger.error(f"Failed to queue delivery task for notification ID {notification_id}: {queue_err}", exc_info=True)
                raise DatabaseError("Failed to queue delivery task") from queue_err

            await session.commit()
            task_logger.info(f"Notification {notification_id} committed successfully.")

            return notification_id

    try:
        result_id = asyncio.run(_process_and_commit())
        #result_id = async_to_sync(_process_and_commit)()
        return result_id

    except (IntegrityError, DatabaseError,)  as e:
        task_logger.warning(f"Retryable error processing notification {notification_id or '(no ID yet)'} for user {user_id}. Attempt {self.request.retries + 1}. Error: {e}", exc_info=True)
        try:
            raise self.retry(exc=e)
        except MaxRetriesExceededError:
            task_logger.critical(f"Max retries exceeded processing notification {notification_id or '(no ID yet)'} for user {user_id}. Error: {e}", exc_info=True)

    except IntegrityError as e:
        task_logger.warning(f"Database integrity error processing notification for user {user_id} (likely duplicate?): {e}")
        raise Ignore() 

    except Exception as e:
        task_logger.exception(f"Unexpected error processing notification {notification_id or '(no ID yet)'} for user {user_id}. Attempt {self.request.retries + 1}.")
        try:
            raise self.retry(exc=e)
        except MaxRetriesExceededError:
            task_logger.critical(f"Max retries exceeded processing notification {notification_id or '(no ID yet)'} for user {user_id} after unexpected error.", exc_info=True)
            raise