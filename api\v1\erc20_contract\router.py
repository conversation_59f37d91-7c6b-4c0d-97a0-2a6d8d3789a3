from sqlalchemy.ext.asyncio import AsyncSession
from api.v1.user import schemas as user_schema
from fastapi import Depends, HTTPException, APIRouter, Depends
from api.core.dependencies import is_authenticated
from .schemas import TransferRequest, ApprovalRequest, TransferFromRequest, MintRequest, IncreaseAllowanceRequest, DecreaseAllowanceRequest
from .services import ERC20Interaction
from api.db.database import get_db
from .exceptions import handle_contract_notification, handle_erc20_error
from typing import Optional
import asyncio
from web3 import AsyncWeb3
from api.core.blockchain_dep import get_web3
from api.core.general_dep import get_redis
from redis.asyncio import Redis
from api.v1.deploy_contracts.services import ToBeSigned, ContractDeployment, TransactionType

app = APIRouter(tags=["Erc20 Contract"])


@app.post("/token/mint")
async def mint_token(request: MintRequest, contract_id: int,
                    db: AsyncSession = Depends(get_db), default_wallet:Optional[bool]=True,
                    user: user_schema.User = Depends(is_authenticated), web3: AsyncWeb3 = Depends(get_web3)):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3)
        tx_hash = await contract.mint(
            request.recipient_address,
            request.amount,
            default_wallet,
        )
        return tx_hash
    except Exception as e:
        handle_erc20_error(e)


@app.get("/token/name")
async def get_name(contract_id: int, user: user_schema.User = Depends(is_authenticated),
                    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                    redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id,
                    contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3, redis=redis)
        return await contract.get_name()
    except Exception as e:
        handle_erc20_error(e)



@app.get("/token/symbol")
async def get_symbol(contract_id: int,
                    user: user_schema.User = Depends(is_authenticated),
                    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                     redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id,
                    contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3, redis=redis)
        return await contract.get_symbol()
    except Exception as e:
        handle_erc20_error(e)


@app.get("/token/decimals")
async def get_decimals(contract_id: int,
                    user: user_schema.User = Depends(is_authenticated),
                    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                     redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id,
                    contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3, redis=redis)
        return await contract.get_decimals()
    except Exception as e:
        handle_erc20_error(e)


@app.get("/token/total-supply")
async def get_total_supply(contract_id: int,
                    user: user_schema.User = Depends(is_authenticated),
                    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                     redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id,
                    contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3, redis=redis)
        return await contract.get_total_supply()
    except Exception as e:
        handle_erc20_error(e)


@app.get("/token/owned-by-user")
async def owned_by_you(contract_id: int,
                    user: user_schema.User = Depends(is_authenticated),
                    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                     redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id,
                    contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3, redis=redis)
        return await contract.owned_by_you()
    except Exception as e:
        handle_erc20_error(e)


@app.get("/token/balance")
async def get_balance(contract_id: int, account_address: str,
                    user: user_schema.User = Depends(is_authenticated),
                    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                    redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id,
                    contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3, redis=redis)
        return await contract.get_balance_of(account_address)
    except Exception as e:
        handle_erc20_error(e)


@app.get("/token/allowance")
async def get_allowance(contract_id: int, spender_address: str,
                        user: user_schema.User = Depends(is_authenticated),
                        db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3),
                        redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id,
                contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3, redis=redis)
        return await contract.get_allowance(spender_address)
    except Exception as e:
        handle_erc20_error(e)



@app.post("/token/transfer")
async def transfer(
    contract_id: int, request: TransferRequest,
    default_wallet:Optional[bool]=True,
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3)
):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3)
        tx_hash = await contract.transfer(
            request.recipient_address,
            request.amount,
            default_wallet
            )
        return tx_hash
    except Exception as e:
        handle_erc20_error(e)



@app.post("/token/approve")
async def approve(
    contract_id: int, request: ApprovalRequest,
    default_wallet:Optional[bool]=True,
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3)
):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3)
        tx_hash = await contract.approve(
            request.spender_address,
            request.amount,
            default_wallet,
        )
        return tx_hash
    except Exception as e:
        handle_erc20_error(e)


@app.post("/token/transfer-from")
async def transfer_from(
    contract_id: int, request: TransferFromRequest,
    default_wallet:Optional[bool]=True,
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3)
):
    try:

        contract = await ERC20Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3)
        tx_hash = await contract.transfer_from(
            request.recipient_address,
            request.amount,
            request.sender_address,
            default_wallet,
        )
        return tx_hash
    except Exception as e:
        handle_erc20_error(e)


@app.post("/token/increase-allowance")
async def increase_allowance(
    contract_id: int, request: IncreaseAllowanceRequest,
    default_wallet:Optional[bool]=True,  # not applicable for ERC20
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3)
):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3)
        tx_hash = await contract.increase_allowance(
            request.spender_address,
            request.added_value,
            default_wallet,
        )
        return tx_hash
    except Exception as e:
        handle_erc20_error(e)


@app.post("/token/decrease-allowance")
async def decrease_allowance(
    contract_id: int, request: DecreaseAllowanceRequest,
    default_wallet:Optional[bool]=True,
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db), web3: AsyncWeb3 = Depends(get_web3)
):
    try:
        contract = await ERC20Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ER20, web3=web3)
        tx_hash = await contract.decrease_allowance(
            request.spender_address,
            request.subtracted_value,
            default_wallet,
        )
        return tx_hash
    except Exception as e:
        handle_erc20_error(e)