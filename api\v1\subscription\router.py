from fastapi import APIRouter, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession
from api.v1.user import schemas as user_schema
from api.db.database import get_db
from api.core.dependencies import is_authenticated
from .services import Subscription
from . import schemas
from .exceptions import handle_subscription_notification
from api.core.auth import require_super_admin


app = APIRouter(tags=["Subscriptions"])

subscriptionService = Subscription()

#"""
@app.post("/subscriptions", status_code=status.HTTP_201_CREATED)
async def create_subscription_plan(
    tier: schemas.SubscriptionTier,
    plan: schemas.SubscriptionPlanCreate,
    db: AsyncSession = Depends(get_db),
    #user: user_schema.User = Depends(is_authenticated)
    admin = Depends(require_super_admin)
):
    try:
        # Use await with async service method
        plan = await subscriptionService.create_subscription_plan(tier=tier, plan=plan, db=db)
        #await handle_subscription_notification(db=db, user_id=user.id, action="create_subscription")
        return {"message": "Subscription plan created successfully.", "detail": plan}
    except Exception:
        raise


@app.patch("/subscriptions/{plan_id}", status_code=status.HTTP_200_OK)
async def update_subscription_plan(
    tier: schemas.SubscriptionTier,
    plan_data: schemas.SubscriptionPlanUpdate,
    db: AsyncSession = Depends(get_db),
    #user: user_schema.User = Depends(is_authenticated)
    admin = Depends(require_super_admin)
):
    try:
        updated_plan = await subscriptionService.update_subscription_plan(db=db, plan_data=plan_data, tier=tier)
        #await handle_subscription_notification(db=db, user_id=user.id, action="update_subscription")
        return {"message": "Subscription plan updated successfully.", "detail": updated_plan}
    #"""
    except Exception:
        raise


@app.get("/subscriptions", status_code=status.HTTP_200_OK)
async def get_subscription_plans(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    user: user_schema.User = Depends(is_authenticated)  # Required for authentication, even if not directly used
):
    try:
        # Use await with async service method
        plans = await subscriptionService.get_subscription_plans(db=db, skip=skip, limit=limit)
        #await handle_subscription_notification(db=db, user_id=user.id, action="get_subscriptions")
        return plans
    except Exception:
        raise


@app.get("/subscriptions/{plan_id}", status_code=status.HTTP_200_OK)
async def get_subscription_plan(
    tier: schemas.SubscriptionTier,
    db: AsyncSession = Depends(get_db),
    user: user_schema.User = Depends(is_authenticated)  # Required for authentication, even if not directly used
):
    try:
        # Use await with async service method
        plan = await subscriptionService.get_subscription_plan(tier=tier, db=db)
        #await handle_subscription_notification(db=db, user_id=user.id, action="get_subscription")
        return plan
    except Exception:
        raise

"""
@app.post("/subscriptions/subscribe/{subscription_id}", status_code=status.HTTP_201_CREATED)
async def subscribe_user(
    tier: schemas.SubscriptionTier,
    db: Session = Depends(get_db),
    user: user_schema.User = Depends(is_authenticated)
):
    user_subscription = subscriptionService.subscribe_user(tier=tier, user=user, db=db)
    await handle_subscription_notification(db=db, user_id=user.id, action="subscribe_user")
    return user_subscription
"""

@app.get("/users/{user_id}/subscriptions", status_code=status.HTTP_200_OK)
async def get_user_subscriptions(
    db: AsyncSession = Depends(get_db),
    user: user_schema.User = Depends(is_authenticated)
):
    try:
        user_subscriptions = await subscriptionService.get_user_subscriptions(user=user, db=db)
        #await handle_subscription_notification(db=db, user_id=user.id, action="get_user_subscriptions")
        return user_subscriptions
    except Exception:
        raise


@app.delete("/subscriptions/cancel/{subscription_id}", status_code=status.HTTP_200_OK)
async def cancel_user_subscription(
    db: AsyncSession = Depends(get_db),
    user: user_schema.User = Depends(is_authenticated)
):
    try:
        message = await subscriptionService.cancel_user_subscription(user=user, db=db)
        handle_subscription_notification(user_id=user.id, action="cancel_subscription")
        return message
    except Exception:
        raise

"""
# Paystack Integration
PAYSTACK_SECRET_KEY = "your_paystack_secret_key"

@app.post("/payments/initialize", response_model=schemas.PaymentResponse)
async def initialize_payment(
    payment: schemas.PaymentInitialize,
    service: services.SubscriptionService = Depends(get_services)
):
    headers = {
        "Authorization": f"Bearer {PAYSTACK_SECRET_KEY}",
        "Content-Type": "application/json"
    }

    payload = {
        "email": payment.email,
        "amount": payment.amount * 100,  # Convert to kobo
        "callback_url": payment.callback_url,
        "reference": payment.reference
    }

    response = requests.post(
        "https://api.paystack.co/transaction/initialize",
        json=payload,
        headers=headers
    )

    if response.status_code != 200:
        raise HTTPException(status_code=400, detail="Payment initialization failed")

    return response.json()["data"]

@app.get("/payments/verify/{reference}", response_model=schemas.PaymentVerificationResponse)
async def verify_payment(
    reference: str,
    service: services.SubscriptionService = Depends(get_services)
):
    headers = {
        "Authorization": f"Bearer {PAYSTACK_SECRET_KEY}",
        "Content-Type": "application/json"
    }

    response = requests.get(
        f"https://api.paystack.co/transaction/verify/{reference}",
        headers=headers
    )

    if response.status_code != 200:
        raise HTTPException(status_code=400, detail="Payment verification failed")

    return response.json()
"""