from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import json
import time

from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from api.db.database import SessionLocal

from sqlalchemy.future import select
from web3 import Web3
from api.v1.account.models import Account as AccountWallet
from api.v1.account.services import Vault

from api.v1.account.models import WalletTransactions, ContractStatus as Status
from api.v1.erc721_contract.models import NFT
from api.v1.erc721_contract.schemas import TransactionDetails, ReceiptDetails, TransactionResponse
from .models import ToBeSigned, ContractDeployment, TransactionType, make_json_serializable
from .exceptions import (
    logger, ContractNotFoundError, BlockchainConnectionError,
    TransactionTimeoutError, InsufficientFundsError, SignatureError,
    DatabaseError, DataProcessingError, handle_deployment_error, CacheError
)
from .services import BlockchainBase
from api.core.blockchain_dep import class_exponential_backoff, load_contract_artifacts, CONTRACT_CONFIG
import asyncio
from web3 import AsyncWeb3
from config import config
from redis.asyncio import Redis
from api.core.general_dep import AsyncTaskManager
from api.core.wallet_security import WalletSecurity


# Initialize Web3 provider
PROVIDER = config.PROVIDER
PRODUCTION = config.PRODUCTION
PLATFORM_WALLET = config.PLATFORM_WALLET
PLATFORM_PRIVATE_KEY = config.PLATFORM_PRIVATE_KEY
PLATFORM_FEE_PERCENTAGE = config.PLATFORM_FEE_PERCENTAGE
PLATFORM_FLAT_FEE = config.PLATFORM_FLAT_FEE
GAS_BUFFER_PERCENT = 15
CACHE_TIME = 600
LONGEST_CACHE_TIME = 28800
SHORTEST_CACHE_TIME = 180



class TokenBase:
    """Base class for token operations (ERC20, ERC721, etc.)"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 web3: AsyncWeb3, redis: Optional[Redis] = None):
        """Initialize token base functionality"""
        try:
            self.db = db
            self.user_id = user_id
            self.contract_id = contract_id
            self.w3 = web3

            self.general_cache_time = CACHE_TIME
            self.longest_cache_time = LONGEST_CACHE_TIME
            self.shortest_cache_time = SHORTEST_CACHE_TIME
            self.redis = redis
            self.wallet = None
            self.private_key = None
            self.abi = None
            self.bytecode = None
            self.symbol = None
            self.name = None
            self.contract_instance = None
            self.platform_fee_wei = None
        except Exception as e:
            logger.error(f"Error initializing token base: {e}")
            raise

    @classmethod
    async def create(cls, db: AsyncSession, user_id: int, contract_id: int, contract_type: TransactionType,
                    web3: AsyncWeb3, redis: Optional[Redis] = None):
        """Factory method to create and initialize a TokenBase instance"""
        instance = cls(db, user_id, contract_id, web3, redis)

        try:
            # Initialize wallet and contract
            await asyncio.gather(
                instance._initialize_wallet(),
                instance._initialize_contract(contract_type)
            )

            return instance
        except Exception as e:
            logger.error(f"Failed to create TokenBase instance: {str(e)}")
            raise

    async def _initialize_wallet(self) -> None:
        """Initialize wallet details - common for all token types"""
        #cache this
        try:
            if PRODUCTION:
                result = await self.db.execute(
                    select(AccountWallet).where(AccountWallet.user_id == self.user_id)
                )
                account = result.scalar_one_or_none()

                if not account:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="User does not have a vault"
                    )

                self.wallet = account.address
                self.private_key = await Vault()._get_decrypted_private_key(self.user_id, self.db)
            else:
                self.wallet = PLATFORM_WALLET
                self.private_key = PLATFORM_PRIVATE_KEY

        except Exception as e:
            logger.error(f"Error initializing wallet: {e}")
            raise


    async def _initialize_contract(self, contract_type) -> None:
        """Initialize contract instance"""
        #cache this!
        try:
            abi, bytecode = await load_contract_artifacts(contract_type)
            self.abi = abi
            self.bytecode = bytecode

            result = await self.db.execute(
                select(ContractDeployment).where(
                    ContractDeployment.user_id == self.user_id,
                    ContractDeployment.id == self.contract_id
                )
            )
            contract = result.scalar_one_or_none()

            if not contract:
                raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Contract not found")

            self.symbol = contract.symbol
            self.name = contract.name
            self.contract_instance = self.w3.eth.contract(abi=self.abi, address=contract.contract_address)
            self.platform_fee_wei = self.w3.to_wei(PLATFORM_FLAT_FEE, 'ether')
        except Exception as e:
            logger.error(f"Error initializing contract: {e}")
            raise


    def _calculate_platform_fee(self, amount: int, fee_percentage: int) -> int:
        """Calculate platform fee based on amount and percentage"""
        platform_fee = ((amount * fee_percentage) // 100) + self.platform_fee_wei
        return platform_fee



    async def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache with error handling"""
        if not self.redis:
            return None

        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Cache data corruption detected: {str(e)}")
            await self.redis.delete(cache_key)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval failed: {str(e)}")
            return None

    async def _cache_data(self, cache_key: str, data: Any, cache_time: int) -> None:
        """Cache data with error handling"""
        if not self.redis:
            return None

        try:
            await self.redis.set(cache_key, json.dumps(data))
            await self.redis.expire(cache_key, cache_time)
        except Exception as e:
            logger.warning(f"Failed to cache data: {str(e)}")





class TransactionSigner(BlockchainBase):
    """Base class for signing and processing transactions"""

    async def _get_pending_transaction(self, signature_id: int) -> ToBeSigned:
        """Get pending transaction by ID with locking"""
        result = await self.db.execute(
            select(ToBeSigned).where(
                ToBeSigned.user_id == self.user_id,
                ToBeSigned.id == signature_id,
                ToBeSigned.status == 'pending'
            ).with_for_update()
        )
        pending_tx = result.scalar_one_or_none()

        if not pending_tx:
            raise ContractNotFoundError("Transaction not found or already processed")

        return pending_tx

    async def _process_unsigned_transaction(self, pending_tx: ToBeSigned) -> str:
        """Process, sign and send an unsigned transaction"""
        unsigned_tx = pending_tx.transaction_data
        if isinstance(unsigned_tx, str):
            unsigned_tx = json.loads(unsigned_tx)

        # Fix null address
        if 'to' in unsigned_tx and unsigned_tx['to'] == '0x':
            unsigned_tx['to'] = None

        # Check if transaction expired
        if datetime.now(timezone.utc) > pending_tx.expires_at.replace(tzinfo=timezone.utc):
            try:
                pending_tx.status = 'expired'
                await self.db.commit()
            except Exception as db_error:
                raise DatabaseError(f"Failed to update transaction status: {str(db_error)}")
            raise TransactionTimeoutError("Transaction expired")

        # Update nonce
        try:
            unsigned_tx["nonce"] = await self.w3.eth.get_transaction_count(self.wallet)
            #pending_tx.transaction_data = json.dumps(unsigned_tx, default=make_json_serializable)
        except Exception as blockchain_error:
            raise BlockchainConnectionError(f"Failed to get nonce: {str(blockchain_error)}")

        await self._check_wallet_balance(pending_tx.total_cost_wei)

        return await self._sign_and_send_transaction(unsigned_tx)



    async def _get_transaction_and_receipt(self, tx_hash) -> tuple:
        """Get transaction details and receipt"""
        try:
            receipt, transaction = await asyncio.gather(
                self.w3.eth.wait_for_transaction_receipt(tx_hash),
                self.w3.eth.get_transaction(tx_hash)
            )
            return transaction, receipt
        except Exception as tx_error:
            raise TransactionTimeoutError(f"Failed to get transaction details: {str(tx_error)}")

    async def _process_transaction_logs(self, receipt) -> list:
        """Process transaction logs"""
        logs = []
        for log in receipt.logs:
            try:
                decoded_log = self.w3.eth.LogFeeTransfer().process_log(log)
                logs.append(decoded_log)
            except Exception:
                logs.append(log)
        return logs

    async def _update_transaction_status(self, pending_tx: ToBeSigned, status: str, tx_hash: hex) -> None:
        """Update pending transaction status and related records"""
        try:
            pending_tx.status = status

            # Update NFT status if applicable
            if pending_tx.nft_id is not None and status == "signed":
                result = await self.db.execute(
                    select(NFT).where(NFT.id == pending_tx.nft_id)
                )
                nft = result.scalar_one_or_none()
                if nft:
                    nft.status = "success"

            # Convert operation string to dict if needed
            if isinstance(pending_tx.operation, str):
                try:
                    operation_dict = json.loads(pending_tx.operation)
                except json.JSONDecodeError:
                    logger.error(f"Failed to parse operation JSON: {pending_tx.operation}")
                    operation_dict = {
                        "metadata": {
                            "transaction_details": {
                                "hash": tx_hash
                            }
                        }
                    }
            else:
                operation_dict = pending_tx.operation if isinstance(pending_tx.operation, dict) else {}

            # Update operation with transaction hash
            if isinstance(operation_dict, dict):
                #if 'metadata' not in operation_dict:
                    #operation_dict['metadata'] = {}
                #if 'transaction_details' not in operation_dict['metadata']:
                    #operation_dict['metadata']['transaction_details'] = {}
                #operation_dict['metadata']['transaction_details']['hash'] = tx_hash

                metadata = operation_dict.setdefault('metadata', {})
                tx_details = metadata.setdefault('transaction_details', {})
                tx_details['hash'] = tx_hash

            pending_tx.operation = json.dumps(operation_dict)

            await self.db.commit()
            await self.db.refresh(pending_tx)
        except Exception as db_error:
            raise DatabaseError(f"Failed to update transaction status: {str(db_error)}")

    async def _create_transaction_response(self, transactions: Any, receipt: Any, logs: list) -> TransactionResponse:
        """Create transaction response from transaction details"""
        try:
            return TransactionResponse(
                transaction=TransactionDetails(
                    hash=transactions.hash.hex(),
                    from_address=transactions['from'],
                    to=transactions.to,
                    value=float(self.w3.from_wei(transactions.value, 'ether')),
                    gas=transactions.gas,
                    gas_price=float(self.w3.from_wei(transactions.gasPrice, 'ether')),
                    input_data=transactions.input.hex(),
                    transaction_index=transactions.transactionIndex,
                    type=transactions.type,
                ),
                receipt=ReceiptDetails(
                    status="success" if receipt.status == 1 else "failed",
                    contract_address=receipt.contractAddress,
                    gas_used=receipt.gasUsed,
                    block_number=receipt.blockNumber,
                )
            )

        except Exception as format_error:
            raise DataProcessingError(f"Failed to format transaction details: {str(format_error)}")

    #async def _save_wallet_transaction(self, details: TransactionResponse) -> None:
    async def _save_wallet_transaction_task(self, user_id: int, details: TransactionResponse) -> None:

        """Save transaction details to wallet transactions"""

        async with SessionLocal() as background_db:
            try:
                stmt = select(AccountWallet).where(AccountWallet.user_id == user_id)
                result = await background_db.execute(stmt)
                vault = result.scalar_one_or_none()
                if not vault:
                    logger.error(f"Background Task: User wallet not found for user_id {user_id} during save.")
                    return

                new_transaction = WalletTransactions(
                    user_id=self.user_id,
                    address_id=vault.id,
                    contract_status=Status.DEPLOYED if details.receipt.status == "success" else Status.FAILED,
                    value=details.transaction.value,
                    gas_used=details.receipt.gas_used,
                )

                background_db.add(new_transaction)
                await background_db.commit()
                logger.info(f"Background Task: Saved wallet transaction for user {user_id}, hash {details.transaction.hash}")

            except Exception as db_error:
                logger.error(f"Background Task: Failed to save transaction details for user {user_id}: {db_error}", exc_info=True)
                raise db_error


    """ 
    
    async def _save_wallet_transaction(self, details: TransactionResponse) -> None:
        Save transaction details to wallet transactions
        try:
            result = await self.db.execute(
                select(AccountWallet).where(AccountWallet.user_id == self.user_id)
            )
            vault = result.scalar_one_or_none()
            if not vault:
                raise ContractNotFoundError("User wallet not found")

            new_deployment = WalletTransactions(
                user_id=self.user_id,
                address_id=vault.id,
                contract_status=Status.DEPLOYED if details.receipt.status == "success" else Status.FAILED,
                value=details.transaction.value,
                gas_used=details.receipt.gas_used,
            )

            self.db.add(new_deployment)
            await self.db.commit()
            await self.db.refresh(new_deployment)

        except ContractNotFoundError:
            raise
        except Exception as db_error:
            raise DatabaseError(f"Failed to save transaction details: {str(db_error)}")


    """


@class_exponential_backoff()
class SignTransaction(TransactionSigner):
    """Handles transaction signing and processing"""

    def __init__(self, db: AsyncSession, user_id: int, web3: AsyncWeb3, abi, bytecode,
                 transaction_type: TransactionType = None, redis: Optional[Redis] = None):

        super().__init__(db, user_id, web3, abi, bytecode, transaction_type, redis)        
        self.task_manager = AsyncTaskManager()

    async def __aenter__(self):
        pass

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.task_manager.wait_all()


    async def transaction_sign(self, contract_id: int, signature_id: int, trx_hash: Optional[str] = None) -> TransactionResponse:
        """Sign and process a transaction by signature ID"""
        try:
            self.contract_id = contract_id

            # Get pending transaction
            pending_tx = await self._get_pending_transaction(signature_id)

            tx_hash = trx_hash
            if tx_hash is None:
                tx_hash = await self._process_unsigned_transaction(pending_tx)

            try:
                receipt = await self.w3.eth.wait_for_transaction_receipt(tx_hash)
                transaction = await self.w3.eth.get_transaction(tx_hash)
            except Exception as tx_error:
                raise TransactionTimeoutError(f"Failed to get transaction details: {str(tx_error)}")

            logs = await self._process_transaction_logs(receipt)
            details = await self._create_transaction_response(transaction, receipt, logs)

            if details.receipt.status == "success":
                await self._update_transaction_status(pending_tx, "signed", details.transaction.hash)

            #await self.task_manager.create_task(self._save_wallet_transaction(details))
                await self.task_manager.create_task(
                    self._save_wallet_transaction_task(self.user_id, details)
                )
            return details

        except Exception as e:
            logger.error(f"Error in transaction_sign: {e}")
            raise