from typing import Dict, Any, List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


# Define response Pydantic Model:
class MintResponse(BaseModel):
    transaction: Dict[str, Any]
    receipt: Dict[str, Any]

# Base request model for contract address
class ContractAddressRequest(BaseModel):
    contract_address: str


class ApprovalRequest(BaseModel):
    to_address: str
    token_id: int
    #owner_private_key: str = Field(default="682d70b616cfce1b158560d862b1a94e9ef0c4d6573f88fb7309d2d6520cc685")
    #owner_address: str = Field(default="0x342cF74c92DF872Fe1C6B6B08F8B87C030A9602B")

#class MintRequest(BaseModel):
    #minter_address: str = Field(default="0x342cF74c92DF872Fe1C6B6B08F8B87C030A9602B")
    #minter_private_key: str = Field(default="682d70b616cfce1b158560d862b1a94e9ef0c4d6573f88fb7309d2d6520cc685")

class OwnershipRequest(BaseModel):
    new_owner_address: str = None

class TransferRequest(BaseModel):
    #from_address: str = Field(default="0x342cF74c92DF872Fe1C6B6B08F8B87C030A9602B")
    to_address: str
    token_id: int
    #from_private_key: str = Field(default="682d70b616cfce1b158560d862b1a94e9ef0c4d6573f88fb7309d2d6520cc685")

class ApprovalForAllRequest(BaseModel):
    operator: str
    approved: bool
    #owner_address: str = Field(default="0x342cF74c92DF872Fe1C6B6B08F8B87C030A9602B")
    #owner_private_key: str = Field(default="682d70b616cfce1b158560d862b1a94e9ef0c4d6573f88fb7309d2d6520cc685")

class SafeTransferWithDataRequest(TransferRequest):
    data: str


class OwnerAddress(BaseModel):
    owner_address: str = Field(default="0x342cF74c92DF872Fe1C6B6B08F8B87C030A9602B")














from decimal import Decimal

class TransactionDetails(BaseModel):
    hash: str
    from_address: str
    to: Optional[str]
    value: Decimal
    gas: int
    gas_price: Decimal
    input_data: str
    transaction_index: int
    type: int

class ReceiptDetails(BaseModel):
    status: str
    contract_address: Optional[str]
    gas_used: int
    block_number: int
    #logs: str

class TransactionResponse(BaseModel):
    transaction: TransactionDetails
    receipt: ReceiptDetails






from pydantic import BaseModel, Field
from typing import List, Dict, Any

class Attribute(BaseModel):
    trait_type: Optional[str] = None
    value: Optional[Any] = None

class Metadata(BaseModel):
    name: Optional[str]
    description: Optional[str] = None
    #image: str
    attributes: Optional[List[Attribute]] = None








from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class NFTMetadataSchema(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    image: Optional[str] = None
    attributes: Optional[Any] = None

    class Config:
        from_attributes = True

class NFTSchema(BaseModel):
    metadata: NFTMetadataSchema
    created_by: int
    contract_id: int
    token_id: int
    metadata_uri: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


class NFTSchemaList(BaseModel):
    token_id: int
    image: Optional[str] = None

    class Config:
        from_attributes = True



class ERC721Write(str, Enum):
    TRANSFER_OWNER = "transferOwnership"
    MINT = "mint"
    APPROVE = "approve"
    SET_APPROVAL_FOR_ALL = "setApprovalForAll"
    TRANSFER_FROM = "transferFrom"
    SAFE_TRANSFER_FROM = "safeTransferFrom"
    SAFE_TRANSFER_FROM_WITH_DATA = "safeTransferFromWithData"
    RENOUNCE_OWNERSHIP = "renounceOwnership"