from api.v1.websockets.models import NotificationType, NotificationPriority
from api.v1.websockets.services import NotificationService
from api.v1.websockets.schemas import NotificationCreate
from typing import Optional

def handle_payment_notification(user_id: int, action: str, payment_data: Optional[dict] = None):
    """Handle payment-related notifications"""
    messages = {
        "initiate_payment": "Payment initiation successful. Awaiting confirmation.",
        "verify_payment": "Payment verified successfully.",
        "payment_failed": "Payment verification failed. Please try again.",
        "paystack_webhook": "Paystack payment notification received.",
        "stripe_webhook": "Stripe payment notification received.",
        "monnify_webhook": "Monnify payment notification received.",
        "subscription_active": "Your subscription is now active.",
        "subscription_expired": "Your subscription has expired."
    }

    titles = {
        "initiate_payment": "Payment Initiated",
        "verify_payment": "Payment Verified",
        "payment_failed": "Payment Failed",
        "paystack_webhook": "Paystack Payment Update",
        "stripe_webhook": "Stripe Payment Update",
        "monnify_webhook": "Monnify Payment Update",
        "subscription_active": "Subscription Active",
        "subscription_expired": "Subscription Expired"
    }

    if action in messages:
        message = messages[action]
        title = titles.get(action, "Payment Notification")

        # Create notification with enhanced structure
        notification = NotificationCreate(
            title=title,
            message=message,
            type=NotificationType.PAYMENT,
            priority=NotificationPriority.HIGH if "failed" in action else NotificationPriority.NORMAL,
            metadata={
                "payment_details": {
                    "action_type": action,
                    "status": "failed" if "failed" in action else "success",
                    "detail": {
                        "payment_method": payment_data.get("payment_method") if payment_data else None,
                        "amount": payment_data.get("amount") if payment_data else None,
                        "currency": payment_data.get("currency") if payment_data else None,
                        "transaction_ref": payment_data.get("transaction_ref") if payment_data else None
                    }
                }
            },
            action_url=payment_data.get("payment_url") if payment_data else None
        )

        notification_service = NotificationService()
        notification_service.publish_notification(user_id=user_id, notification=notification)