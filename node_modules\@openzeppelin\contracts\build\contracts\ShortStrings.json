{"_format": "hh-sol-artifact-1", "contractName": "ShortStrings", "sourceName": "contracts/utils/ShortStrings.sol", "abi": [{"inputs": [], "name": "InvalidShortString", "type": "error"}, {"inputs": [{"internalType": "string", "name": "str", "type": "string"}], "name": "StringTooLong", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220b6b527c873e86e4d70b0a27493ad7b42afe222f991207a6ceaf2aeafa71285dc64736f6c63430008180033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220b6b527c873e86e4d70b0a27493ad7b42afe222f991207a6ceaf2aeafa71285dc64736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}