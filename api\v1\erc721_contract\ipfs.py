import requests
import json
from pathlib import Path
from io import Bytes<PERSON>
from typing import Dict, List, Optional
from .schemas import <PERSON>ada<PERSON>
from config import config
from api.core.logging_config import get_logger, log_execution_time, LogContext
import asyncio
import aiofiles
import httpx


logger = get_logger(__name__)
class IPFSError(Exception): pass
class IPFSUploadError(IPFSError): pass
class PinataAPIError(IPFSError): pass

PINATA_API_KEY = config.PINATA_API_KEY
PINATA_SECRET_KEY = config.PINATA_SECRET_KEY


class IPFSUploader:
    def __init__(self):
        try:    
            """Initialize with Pinata or NFT.Storage credentials"""
            self.api_key = PINATA_API_KEY
            self.api_secret = PINATA_SECRET_KEY

            self.pinata_base_url = "https://api.pinata.cloud"
            self.pinata_file_endpoint = f"{self.pinata_base_url}/pinning/pinFileToIPFS"
            self.pinata_json_endpoint = f"{self.pinata_base_url}/pinning/pinJSONToIPFS"
            self.headers = {
                'pinata_api_key': self.api_key,
                'pinata_secret_api_key': self.api_secret
            }
        except Exception as e:
            logger.critical(f"Failed to initialize IPFS Uploader: {str(e)}", exc_info=True)
            raise

    async def upload_file_to_pinata(self, file_path: str) -> str:
        try:    
            """Upload a file to Pinata and return IPFS hash"""
            
            file_path_obj = Path(file_path)
            if not await asyncio.to_thread(file_path_obj.is_file):
                logger.error(f"File not found at path: {file_path}")
                raise FileNotFoundError(f"File not found: {file_path}")
            

            async with aiofiles.open(file_path_obj, "rb") as fp:
                files = {'file': (file_path_obj.name, await fp.read())}

                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        self.pinata_file_endpoint,
                        files=files,
                        headers=self.headers
                    )

                response.raise_for_status()
                response_data = response.json()
                ipfs_hash = response_data.get("IpfsHash")
                    
                if not ipfs_hash:
                    raise PinataAPIError("IPFS Hash not found in Pinata response.", response.text)
                logger.info(f"File {file_path} uploaded to Pinata. Hash: {ipfs_hash}")
                return ipfs_hash
                
        except httpx.HTTPStatusError as e:
            logger.error(f"Pinata API error (file upload) - Status {e.response.status_code}: {e.response.text}", exc_info=True)
            raise PinataAPIError(f"Pinata API error (file): {e.response.status_code}", e.response.text) from e
        except FileNotFoundError:
             raise
        except Exception as e:
            logger.error(f"Failed to upload file to IPFS: {str(e)}", exc_info=True)
            raise IPFSUploadError(f"Failed during IPFS file upload: {e}") from e



    async def upload_metadata_to_pinata(self, metadata: Dict) -> str:
        """Upload metadata JSON to Pinata and return IPFS hash"""
        try:    
            headers = {
                'Content-Type': 'application/json',
                'pinata_api_key': self.api_key,
                'pinata_secret_api_key': self.api_secret
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.pinata_json_endpoint,
                    json={
                        'pinataContent': metadata
                    },
                    headers=headers       
                )

                response.raise_for_status()
                response_data = response.json()
                ipfs_hash = response_data.get("IpfsHash")
                if not ipfs_hash:
                    raise PinataAPIError("IPFS Hash not found in Pinata JSON response.", response.text)
                logger.info(f"Metadata uploaded to Pinata. Hash: {ipfs_hash}")
                return ipfs_hash
            

        except httpx.HTTPStatusError as e:
            logger.error(f"Pinata API error (metadata upload) - Status {e.response.status_code}: {e.response.text}", exc_info=True)
            raise PinataAPIError(f"Pinata API error (metadata): {e.response.status_code}", e.response.text) from e
        except Exception as e:
            logger.error(f"Failed to upload metadata to IPFS: {str(e)}", exc_info=True)
            raise IPFSUploadError(f"Failed during IPFS metadata upload: {e}") from e


    async def generate_and_upload_collection(self, token_id: int,image_file: str,
                                     metadata: Optional[Metadata] = None) -> str:
        """Generate and upload entire collection with metadata"""
        try:    
            # Upload image first
            image_hash = await self.upload_file_to_pinata(str(image_file))
            image_uri = f"ipfs://{image_hash}"
            
            metadata_dict = {
                "name": f"{metadata.name} #{token_id}" if metadata and metadata.name else None,
                "description": metadata.description if metadata and metadata.description else None,
                "image": image_uri,
                "attributes": metadata.attributes if metadata and metadata.attributes else None
                }

            # Remove keys with None values
            metadata_dict = {k: v for k, v in metadata_dict.items() if v is not None}
            metadata_hash = await self.upload_metadata_to_pinata(metadata_dict)
            return metadata_hash
        
        except (IPFSUploadError, PinataAPIError, FileNotFoundError) as e:
            logger.error(f"Failed to generate/upload collection for token ID {token_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Failed to generate and upload collection: {str(e)}", exc_info=True)
            raise










"""

import requests
import json
from pathlib import Path
from io import BytesIO
from typing import Dict, List, Optional
from .schemas import Metadata
from config import config
from api.core.logging_config import get_logger, log_execution_time, LogContext

logger = get_logger(__name__)

QUICKNODE_API_KEY = "91386aae2ed884f3953aaf"

class IPFSUploader:
    def __init__(self):
        try:
            ""Initialize with QuickNode credentials""
            self.api_key = QUICKNODE_API_KEY
            self.base_url = "https://api.quicknode.com/ipfs/rest/v1"
            self.s3_endpoint = f"{self.base_url}/s3/put-object"
            self.pinning_endpoint = f"{self.base_url}/pinning"
            logger.info("IPFS Uploader initialized successfully with QuickNode configuration")
        except Exception as e:
            logger.critical(f"Failed to initialize IPFS Uploader: {str(e)}", exc_info=True)
            raise

    @log_execution_time()
    def upload_file_to_ipfs(self, file_path: str) -> str:
        try:
            ""Upload a file to IPFS via QuickNode and return IPFS hash""
            file_size = Path(file_path).stat().st_size
            filename = Path(file_path).name
            content_type = self._get_content_type(file_path)
            
            logger.info(f"Starting file upload to IPFS via QuickNode. File: {filename}, Size: {file_size} bytes, Type: {content_type}")
            
            headers = {
                'x-api-key': self.api_key
            }
            
            payload = {
                'Key': filename,
                'ContentType': content_type
            }
            
            with LogContext(logger, file_path=file_path, file_size=file_size, content_type=content_type):
                with Path(file_path).open("rb") as fp:
                    files = [
                        ('Body', (filename, fp, content_type))
                    ]
                    logger.debug("Sending file to QuickNode IPFS")
                    response = requests.post(
                        self.s3_endpoint,
                        headers=headers,
                        data=payload,
                        files=files
                    )
                
                if response.status_code == 200:
                    response_data = response.json()
                    cid = response_data["pin"]["cid"]
                    logger.info(f"File successfully uploaded to IPFS. CID: {cid}")
                    logger.debug(f"Full response: {json.dumps(response_data, indent=2)}")
                    return cid
                else:
                    error_msg = f"QuickNode upload failed: {response.text}"
                    logger.error(error_msg, extra={
                        "status_code": response.status_code,
                        "response_text": response.text,
                        "endpoint": self.s3_endpoint
                    })
                    raise Exception(error_msg)
            
        except Exception as e:
            logger.error(f"Failed to upload file to IPFS: {str(e)}", exc_info=True)
            raise

    @log_execution_time()
    def upload_metadata_to_ipfs(self, metadata: Dict) -> str:
        try:
            ""Upload metadata JSON to IPFS via QuickNode and return IPFS hash""
            logger.info("Starting metadata upload to IPFS via QuickNode")
            logger.debug(f"Metadata content: {json.dumps(metadata, indent=2)}")
            
            headers = {
                'x-api-key': self.api_key,
                'Content-Type': 'application/json'
            }
            
            metadata_str = json.dumps(metadata)
            metadata_size = len(metadata_str)
            
            payload = {
                'Key': 'metadata.json',
                'ContentType': 'application/json'
            }
            
            with LogContext(logger, metadata_size=metadata_size):
                files = [
                    ('Body', ('metadata.json', BytesIO(metadata_str.encode()), 'application/json'))
                ]
                
                logger.debug("Sending metadata to QuickNode IPFS")
                response = requests.post(
                    self.s3_endpoint,
                    headers=headers,
                    data=payload,
                    files=files
                )
                
                if response.status_code == 200:
                    response_data = response.json()
                    cid = response_data["pin"]["cid"]
                    logger.info(f"Metadata successfully uploaded to IPFS. CID: {cid}")
                    logger.debug(f"Full response: {json.dumps(response_data, indent=2)}")
                    return cid
                else:
                    error_msg = f"Metadata upload failed: {response.text}"
                    logger.error(error_msg, extra={
                        "status_code": response.status_code,
                        "response_text": response.text,
                        "endpoint": self.s3_endpoint
                    })
                    raise Exception(error_msg)
            
        except Exception as e:
            logger.error(f"Failed to upload metadata to IPFS: {str(e)}", exc_info=True)
            raise

    @log_execution_time()
    def generate_and_upload_collection(self, token_id: int, image_file: str,
                                     metadata: Optional[Metadata] = None) -> str:
        try:
            with LogContext(logger, token_id=token_id, image_file=image_file):
                logger.info(f"Starting collection generation for token {token_id}")
                
                # Upload image
                logger.debug(f"Uploading image file: {image_file}")
                image_hash = self.upload_file_to_ipfs(str(image_file))
                image_uri = f"ipfs://{image_hash}"
                logger.info(f"Image uploaded successfully. URI: {image_uri}")
                
                metadata_dict = {
                    "name": f"{metadata.name} #{token_id}" if metadata and metadata.name else None,
                    "description": metadata.description if metadata and metadata.description else None,
                    "image": image_uri,
                    "attributes": metadata.attributes if metadata and metadata.attributes else None
                }
                
                # Remove None values
                metadata_dict = {k: v for k, v in metadata_dict.items() if v is not None}
                logger.debug(f"Prepared metadata: {json.dumps(metadata_dict, indent=2)}")
                
                metadata_hash = self.upload_metadata_to_ipfs(metadata_dict)
                logger.info(f"Collection generated and uploaded successfully. Hash: {metadata_hash}")
                return metadata_hash
        
        except Exception as e:
            logger.error(f"Failed to generate and upload collection: {str(e)}", exc_info=True)
            raise

    def _get_content_type(self, file_path: str) -> str:
        ""Helper method to determine content type based on file extension""
        extension = Path(file_path).suffix.lower()
        content_types = {
            '.pdf': 'application/pdf',
            '.mp3': 'audio/mpeg',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.txt': 'text/plain',
            '.json': 'application/json'
        }
        content_type = content_types.get(extension, 'application/octet-stream')
        logger.debug(f"Determined content type {content_type} for file {file_path}")
        return content_type


"""
