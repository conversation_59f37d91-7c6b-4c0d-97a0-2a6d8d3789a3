from web3 import Web3, AsyncWeb3
from typing import List, Dict, Any, Optional, Tuple, Union, Set
from collections import defaultdict
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import math
import asyncio
from config import config
from fastapi import HTTPException, status
from api.v1.deploy_contracts.models import ContractDeployment, TransactionType
from sqlalchemy.ext.asyncio import AsyncSession
import json
from .schemas import LogModel
from api.core.blockchain_dep import class_exponential_backoff
from .exceptions import (logger, handle_chart_error, ContractError, DataProcessingError,
                         BlockchainError)
from redis.asyncio import Redis

from api.v1.account.models import Account as AccountWallet, WalletScore
from api.v1.user.models import User
from sqlalchemy.future import select

PROVIDER = config.PROVIDER
PRODUCTION = config.PRODUCTION
PLATFORM_WALLET = config.PLATFORM_WALLET
CACHE_TIME = 600
CHUNCK_SIZE = 50
LONG_CHUNCK_SIZE = 3600
MID_CHUNCK_SIZE = 1800
LOGS_CHUNK_SIZE = 10000
MAX_CONCURRENT_REQUESTS = 25  # get_unique_users


class ContractChatBase:
    """Base class for contract chat functionality"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                web3: AsyncWeb3, contract_address: str, earliest_block: int,
                redis: Optional[Redis] = None):

        self.db = db
        self.user_id = user_id
        self.redis = redis
        self.contract_id = contract_id
        self.web3 = web3
        self.contract_address = contract_address
        self.earliest_block = earliest_block
        self.general_cache_time = CACHE_TIME
        self.chunk_size  = CHUNCK_SIZE
        self.logs_cache_time = MID_CHUNCK_SIZE


    @classmethod
    async def create(cls, db: AsyncSession, user_id: int, contract_id: int,
                    web3: AsyncWeb3, redis: Optional[Redis] = None):

        """Initialize contract details"""
        try:
            result = await db.execute(select(ContractDeployment.contract_address, 
                                             ContractDeployment.block_number).filter(
                ContractDeployment.user_id == user_id,
                ContractDeployment.id == contract_id
            ))
            contract_data = result.one_or_none()


            contract_address, earliest_block = contract_data
            if not contract_address or earliest_block is None:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Contract ID {contract_id} has incomplete data (address/block)."
                )
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Database query failed: {str(e)}")
            raise ContractError(f"Failed to initialize contract: {str(e)}")
    
        return cls(db, user_id, contract_id, web3, contract_address, earliest_block, redis)



    async def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get data from cache with error handling"""
        if not self.redis:
            return None
        try:
            cached_data = await self.redis.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Cache data corruption detected: {str(e)}")
            await self.redis.delete(cache_key)
            return None
        except Exception as e:
            logger.error(f"Cache retrieval failed: {str(e)}")
            return None

    async def _cache_data(self, cache_key: str, data: Any, cache_time: int) -> None:
        """Cache data with error handling"""
        if not self.redis:
            return None
        try:
            await self.redis.set(cache_key, json.dumps(data))
            await self.redis.expire(cache_key, cache_time)
        except Exception as e:
            logger.warning(f"Failed to cache data: {str(e)}")


    async def _get_contract_logs(self) -> List[LogModel]:
        """Get contract logs with caching"""
        try:
            cache_key = f"chart_logs:{self.contract_address}"
            cached_logs = await self._get_cached_data(cache_key)
            if cached_logs:
                return [LogModel(**log) for log in cached_logs]

            logs = await self._fetch_logs()
            await self._cache_data(cache_key, [log.model_dump() for log in logs], self.logs_cache_time)
            return logs

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to fetch blockchain data: {str(e)}")
            raise

    #_process_logs_with_semaphore
    async def _fetch_logs(self) -> List[LogModel]:
        """Fetch logs from blockchain with concurrent processing"""
        try:

            start_block = self.earliest_block or (await self.web3.eth.get_block('earliest'))['number']
            end_block = await self.web3.eth.get_block_number()
            if start_block > end_block:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Contract has no activity"
                )
            async def fetch_chunk(chunk_start: int, chunk_end: int) -> List[Any]:
                filter_params = {
                    'fromBlock': chunk_start,
                    'toBlock': chunk_end,
                    'address': self.contract_address
                }
                try:
                    return await asyncio.wait_for(
                        self.web3.eth.get_logs(filter_params),
                        timeout=60.0 # Timeout for a single get_logs call
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout fetching logs chunk {chunk_start}-{chunk_end}")
                    return []
                except Exception as e:
                    logger.warning(f"Error fetching logs for blocks {chunk_start}-{chunk_end}: {str(e)}")
                    return []

            # Create chunks
            chunks = []
            for chunk_start in range(start_block, end_block + 1, LOGS_CHUNK_SIZE):
                chunk_end = min(chunk_start + LOGS_CHUNK_SIZE - 1, end_block)
                chunks.append((chunk_start, chunk_end))

            fetch_semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
            all_raw_logs = []
            tasks = []

            async def fetch_with_semaphore(start, end):
                async with fetch_semaphore:
                    return await fetch_chunk(start, end)
            tasks = [fetch_with_semaphore(start, end) for start, end in chunks]
            #for start, end in chunks:
                #tasks.append(fetch_with_semaphore(start, end))

            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Flatten results and filter out errors
            for result in results:
                if isinstance(result, list):
                    all_raw_logs.extend(result)
                elif isinstance(result, Exception):
                    logger.error(f"Exception during concurrent log fetching: {result}")

            if not all_raw_logs:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Contract has no activity"
                )

            return [self._create_log_model(log) for log in all_raw_logs if log]
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Failed to fetch logs: {str(e)}")


    def _create_log_model(self, log_data: Dict) -> LogModel:
        """Create LogModel from raw log data"""
        return LogModel(
            address=log_data["address"],
            blockHash=log_data["blockHash"].hex() if log_data["blockHash"] else None,
            blockNumber=log_data["blockNumber"],
            data=log_data["data"].hex() if log_data["data"] else None,
            logIndex=log_data["logIndex"],
            removed=log_data["removed"],
            topics=[topic.hex() for topic in log_data['topics']] if log_data["topics"] else None,
            transactionHash=log_data["transactionHash"].hex() if log_data["transactionHash"] else None,
            transactionIndex=log_data["transactionIndex"]
        )

    async def _process_logs_with_semaphore(self, logs: List[LogModel],
                                         process_func) -> List[Dict]:
        """Process logs with semaphore for controlled concurrency"""
        if not logs:
            return []

        valid_results = []
        exceptions_count = 0

        semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)

        async def process_with_semaphore(log):
            nonlocal exceptions_count
            try:
                async with semaphore:
                    result = await process_func(log)
                    if result is not None:
                        return result
                    else:
                        return None
            except Exception as e:
                logger.warning(
                    f"Error processing log {log.transactionHash if log.transactionHash else log.blockNumber}: {e}"
                )

                exceptions_count += 1
                return e
            
        tasks = [process_with_semaphore(log) for log in logs]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        valid_results = [
                result for result in results
                if result is not None and not isinstance(result, Exception)
            ]

        if exceptions_count > 0:
            logger.warning(
                f"Log processing completed with {exceptions_count} failures "
                f"out of {len(logs)} logs for contract {self.contract_id}."
            )

        return valid_results

    def _handle_chart_error(self, error: Exception) -> None:
        """Handle errors in event processing"""
        handle_chart_error(error)



class DataChartProcessor(ContractChatBase):
    """Handles data chart processing"""
    
    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 web3: AsyncWeb3, contract_address: str, earliest_block: int,
                 redis: Optional[Redis] = None):
        super().__init__(
            db=db,
            user_id=user_id,
            contract_id=contract_id,
            web3=web3,
            contract_address=contract_address,
            earliest_block=earliest_block,    
            redis=redis
        )


    def _get_dataframed_stats(self, transactions_data: List[Dict], freq: str) -> Tuple[pd.DataFrame, float]:
        """Process transaction data into statistics"""
        try:
            df = pd.DataFrame(transactions_data)
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_localize('UTC')
            df.set_index('timestamp', inplace=True)

            def calculate_period_stats(period_freq: str) -> pd.DataFrame:
                return pd.DataFrame({
                    'unique_wallets': df.resample(period_freq)['from_address'].nunique(),
                    'total_transactions': df.resample(period_freq)['block_number'].nunique(),
                    'total_events': df.resample(period_freq).size(),
                })

            stats = calculate_period_stats(freq)
            weekly_stats = calculate_period_stats('W')
            stats.ffill(inplace=True)

            # Calculate weekly metrics
            weekly_changes = weekly_stats.pct_change() * 100
            weekly_changes = weekly_changes.replace([np.inf, -np.inf], 0) # Handle infinite values and NaN
            latest_changes = weekly_changes.iloc[-1] if not weekly_changes.empty else pd.Series({
                'unique_wallets': 0, 'total_transactions': 0, 'total_events': 0
            })

            # Calculate combined growth rate
            normalized_changes = {
                'unique_wallets': latest_changes['unique_wallets'] / 100,
                'total_transactions': latest_changes['total_transactions'] / 100,
                'total_events': latest_changes['total_events'] / 100
            }
            combined_growth_rate = sum(normalized_changes.values()) / len(normalized_changes) * 100
            weekly_metrics = round(float(combined_growth_rate), 2) if not pd.isna(combined_growth_rate) else 0

            return stats, weekly_metrics

        except Exception as e:
            logger.error(f"Failed to process statistics: {str(e)}")
            raise DataProcessingError(f"Failed to process statistics: {str(e)}")

    def _prepare_frontend_data(self, time_series_stats: pd.DataFrame) -> List[Dict]:
        """Prepare data for frontend display"""
        try:
            return [{
                'timestamp': index.isoformat(),
                'uniqueWallets': int(row['unique_wallets']),
                'totalTransactions': float(row['total_transactions']) if not pd.isna(row['total_transactions']) else 0.0,
                'totalEvents': float(row['total_events']) if not pd.isna(row['total_events']) else 0.0
            } for index, row in time_series_stats.iterrows()]

        except Exception as e:
            logger.error(f"Failed to prepare frontend data: {str(e)}")
            raise DataProcessingError(f"Failed to prepare frontend data: {str(e)}")


    async def get_data_chart(self, freq: str):
        """Get data chart with caching"""
        start_time = time.time()
        cache_key = f"statistic_chart:{self.contract_address}"
        total_cache_key = f"statistic_chart:{self.contract_address}:{freq}"

        try:
            processed_chart_data = await self._get_cached_data(total_cache_key)
            if processed_chart_data:
                return processed_chart_data
            
            transactions_data = await self._get_cached_data(cache_key)
            if not transactions_data:
                transactions_data = await self._fetch_chart_data()
                if not transactions_data:
                    logger.warning(f"No transaction data found for chart: {self.contract_address}")
                    return {'status': 'success', 'data': [], 'weekly_rate_change': 0.0}
                
                await self._cache_data(
                    cache_key,
                    transactions_data,
                    self.general_cache_time
                )

            #loop = asyncio.get_running_loop()
            try:
                stats, weekly_metrics = await asyncio.to_thread(
                    self._get_dataframed_stats, transactions_data, freq
                )
                frontend_data = await asyncio.to_thread(
                    self._prepare_frontend_data, stats
                )

            except DataProcessingError as e:
                logger.error(f"Data processing failed during chart generation: {e}")
                raise

            result_data = {
                'status': 'success',
                'data': frontend_data[::-1],
                'weekly_rate_change': weekly_metrics
            }
            print("GOT HERE")
            await self._cache_data(
                total_cache_key,
                result_data,
                self.general_cache_time
            )
            #stats, weekly_metrics = self._get_dataframed_stats(transactions_data, freq)
            #frontend_data = self._prepare_frontend_data(stats)
            logger.info(f"Chart generation completed in {time.time() - start_time:.2f} seconds")
            return result_data

        except Exception as e:
            self._handle_chart_error(e)


    async def _fetch_chart_data(self) -> List[Dict]:
        """Fetch chart data"""
        async def process_log(log: LogModel) -> Optional[Dict]:
            try:
                block, tx = await asyncio.gather(
                    self.web3.eth.get_block(log.blockNumber),
                    self.web3.eth.get_transaction(log.transactionHash),
                )
                return {
                    'timestamp': datetime.fromtimestamp(block['timestamp']).isoformat(),
                    'from_address': tx['from'],
                    'block_number': log.blockNumber
                }
            except Exception as e:
                logger.warning(f"Failed to process log: {str(e)}")
                return None

        logs = await self._get_contract_logs()
        return await self._process_logs_with_semaphore(logs, process_log)




class AnalyticsProcessor(ContractChatBase):
    """Handles analytics processing"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 web3: AsyncWeb3, contract_address: str, earliest_block: int,
                 redis: Optional[Redis] = None):
        super().__init__(
            db=db,
            user_id=user_id,
            contract_id=contract_id,
            web3=web3,
            contract_address=contract_address,
            earliest_block=earliest_block,    
            redis=redis
        )
        approval_methods = [
            "approve(address,uint256)", "approve(address,address,uint256)",
            "setApprovalForAll(address,bool)", "approveAndCall(address,uint256,bytes)",
            "increaseAllowance(address,uint256)", "decreaseAllowance(address,uint256)"
        ]
        # Ensure Web3 is imported correctly for keccak
        self.approval_selectors = {Web3.keccak(text=sig)[:4].hex() for sig in approval_methods}

        
    async def _get_transaction_method(self, tx: Dict) -> Optional[str]:
        """Get transaction method from input data"""
        if not tx or not tx.get("input") or len(tx["input"]) < 10:
             return None
        try:
            method_selector = self.web3.to_hex(tx["input"])[:10]
            hex_selector = method_selector[2:]

            if hex_selector in self.approval_selectors:
                return "approve"
            return None
        except Exception as e:
            logger.error(f"Error getting transaction method: {e}")
            return None

    def _get_analytics_stats(self, transactions_data: List[Dict], freq: str) -> Dict:
        """Calculate analytics statistics"""
        try:
            df = pd.DataFrame(transactions_data)
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_localize('UTC')
            df.set_index('timestamp', inplace=True)

            def calculate_period_stats(period_freq: str) -> pd.DataFrame:
                stats = pd.DataFrame({
                    'unique_wallets': df.resample(period_freq)['from_address'].nunique(),
                    'total_events': df.resample(period_freq).size(),
                    'total_transactions': df.resample(period_freq)['transaction_hash'].nunique(),
                })
                if period_freq == freq:
                    stats['approve'] = df.resample(period_freq)['transaction_type'].sum()
                return stats

            stats = calculate_period_stats(freq)
            weekly_stats = calculate_period_stats('W')
            stats.ffill(inplace=True)

            # Calculate weekly changes with safety checks
            weekly_changes = pd.DataFrame({
                'unique_wallets_change': weekly_stats['unique_wallets'].pct_change() * 100,
                'total_events_change': weekly_stats['total_events'].pct_change() * 100,
                'total_transactions_change': weekly_stats['total_transactions'].pct_change() * 100
            })

            # Handle infinite values and NaN
            weekly_changes = weekly_changes.replace([np.inf, -np.inf], 0)
            latest_changes = weekly_changes.iloc[-1] if not weekly_changes.empty else pd.Series({
                'unique_wallets_change': 0,
                'total_events_change': 0,
                'total_transactions_change': 0
            })

            # Calculate totals
            total_unique_wallets = df['from_address'].nunique()
            total_transactions = df['transaction_hash'].nunique()
            total_events = len(df)

            return {
                'response': stats,
                'totalUniqueWallets': total_unique_wallets,
                'totalTransactions': total_transactions,
                'totalEvents': total_events,
                'unique_wallets_change': round(float(latest_changes['unique_wallets_change']), 2)
                    if not pd.isna(latest_changes['unique_wallets_change']) else 0,
                'total_transactions_change': round(float(latest_changes['total_transactions_change']), 2)
                    if not pd.isna(latest_changes['total_transactions_change']) else 0,
                'total_events_change': round(float(latest_changes['total_events_change']), 2)
                    if not pd.isna(latest_changes['total_events_change']) else 0,
            }

        except Exception as e:
            logger.error(f"Failed to process analytics statistics: {str(e)}")
            raise DataProcessingError(f"Failed to process analytics statistics: {str(e)}")

    def _prepare_analytics_data(self, data: Dict) -> Dict:
        """Prepare analytics data for frontend"""
        try:
            stats_df = data['response']

            def create_timeseries(df: pd.DataFrame, columns: List[str], name_mapping: Dict[str, str]) -> List[Dict]:
                return [{
                    'timestamp': index.isoformat(),
                    **{name_mapping[col]: int(row[col]) for col in columns}
                } for index, row in df.iterrows()][::-1]

            return {
                'status': 'success',
                'uniqueWallets_data': create_timeseries(
                    stats_df, ['unique_wallets'], {'unique_wallets': 'uniqueWallets'}
                ),
                'totalTransactions_data': create_timeseries(
                    stats_df, ['total_transactions'], {'total_transactions': 'totalTransactions'}
                ),
                'totalEvents_data': create_timeseries(
                    stats_df, ['total_events'], {'total_events': 'totalEvents'}
                ),
                'eventBreakdown': create_timeseries(
                    stats_df, ['total_events', 'approve'],
                    {'total_events': 'totalEvents', 'approve': 'approved_transactions'}
                ),
                'functionBreakdown': create_timeseries(
                    stats_df, ['total_transactions', 'approve'],
                    {'total_transactions': 'totalTransactions', 'approve': 'approved_transactions'}
                ),
                'overall_totals': {
                    'totalUniqueWallets': {
                        'total': data['totalUniqueWallets'],
                        'weekly_rate_change': data['unique_wallets_change'],
                    },
                    'totalTransactions': {
                        'total': data['totalTransactions'],
                        'weekly_rate_change': data['total_transactions_change'],
                    },
                    'totalEvents': {
                        'total': data['totalEvents'],
                        'weekly_rate_change': data['total_events_change'],
                    }
                }
            }

        except Exception as e:
            logger.error(f"Failed to prepare analytics data: {str(e)}")
            raise DataProcessingError(f"Failed to prepare analytics data: {str(e)}")

    async def get_analytics_chart(self, freq: str):
        """Get analytics chart with caching"""
        start_time = time.time()
        cache_key = f"analytics_chart:{self.contract_address}"
        total_cache_key = f"analytics_chart:{self.contract_address}:{freq}"

        try:
            processed_chart_data = await self._get_cached_data(total_cache_key)
            if processed_chart_data:
                return processed_chart_data
            
            transactions_data = await self._get_cached_data(cache_key)
            if not transactions_data:
                transactions_data = await self._fetch_analytics_data()
                if not transactions_data:
                    logger.warning(f"No transaction data found for chart: {self.contract_address}")
                    empty_response = self._prepare_analytics_data(self._get_analytics_stats([], freq)) # Generate structure
                    return empty_response 
                
                await self._cache_data(
                    cache_key,
                    transactions_data,
                    self.general_cache_time
                )

            #loop = asyncio.get_running_loop()
            try:
                monthly_stats = await asyncio.to_thread(
                    self._get_analytics_stats, transactions_data, freq
                )
                response = await asyncio.to_thread(
                    self._prepare_analytics_data, monthly_stats
                )

            except DataProcessingError as e:
                logger.error(f"Data processing failed during chart generation: {e}")
                raise

            #monthly_stats = self._get_analytics_stats(transactions_data, freq)
            #response = self._prepare_analytics_data(monthly_stats)
            await self._cache_data(
                total_cache_key,
                response,
                self.general_cache_time
            )
            logger.info(f"Analytics chart generation completed in {time.time() - start_time:.2f} seconds")
            return response

        except Exception as e:
            self._handle_chart_error(e)

    async def _fetch_analytics_data(self) -> List[Dict]:
        """Fetch analytics data"""
        async def process_log(log: LogModel) -> Optional[Dict]:
            try:
                block, tx = await asyncio.gather(
                    self.web3.eth.get_block(log.blockNumber),
                    self.web3.eth.get_transaction(log.transactionHash),
                )
                method = await self._get_transaction_method(tx)
                return {
                    'timestamp': datetime.fromtimestamp(block['timestamp']).isoformat(),
                    'from_address': tx['from'],
                    'transaction_hash': log.transactionHash,
                    'transaction_type': 1 if method == "approve" else 0
                }

            except Exception as e:
                logger.warning(f"Failed to process individual log {log.transactionHash}: {str(e)}")
                return None

        logs = await self._get_contract_logs()
        return await self._process_logs_with_semaphore(logs, process_log)



class UsersProcessor(ContractChatBase):
    """Handles user data processing"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 web3: AsyncWeb3, contract_address: str, earliest_block: int,
                 redis: Optional[Redis] = None):
        super().__init__(
            db=db,
            user_id=user_id,
            contract_id=contract_id,
            web3=web3,
            contract_address=contract_address,
            earliest_block=earliest_block,    
            redis=redis
        )

    def _get_contract_users_stats(self, transactions_data: List[Dict], freq: str) -> Dict:
        """Calculate contract users statistics"""
        try:
            df = pd.DataFrame(transactions_data)
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_localize('UTC')
            df.set_index('timestamp', inplace=True)

            def calculate_period_stats(period_freq: str) -> pd.DataFrame:
                return pd.DataFrame({
                    'unique_wallets': df.resample(period_freq)['from_address'].nunique(),
                    'new_users': df.resample(period_freq)['from_address'].apply(
                        lambda x: len(set(x) - set(df.loc[:x.index[0] if len(x.index) > 0 else x.index, 'from_address']))
                        if len(x) > 0 else 0
                    )
                })

            stats = calculate_period_stats(freq)
            weekly_stats = calculate_period_stats('W')
            stats.ffill(inplace=True)

            current_week = weekly_stats.iloc[-1] if not weekly_stats.empty else pd.Series({
                'unique_wallets': 0, 'new_users': 0
            })

            weekly_changes = weekly_stats.pct_change() * 100
            weekly_changes = weekly_changes.replace([np.inf, -np.inf], 0)
            latest_changes = weekly_changes.iloc[-1] if not weekly_changes.empty else pd.Series({
                'unique_wallets': 0, 'new_users': 0
            })

            total_users = df['from_address'].nunique()
            active_users = int(current_week['unique_wallets'])

            uniqueWallets_data = [{
                'timestamp': index.isoformat(),
                'uniqueWallets': int(row['unique_wallets'])
            } for index, row in stats.iterrows()][::-1]

            return {
                'status': 'success',
                'uniqueWallets_data': uniqueWallets_data,
                'overall_totals': {
                    'active_users': {
                        'total': active_users,
                        'weekly_rate_change': round(float(latest_changes['unique_wallets']), 2)
                            if not pd.isna(latest_changes['unique_wallets']) else 0,
                    },
                    'total_users': {
                        'total': total_users,
                        'weekly_rate_change': round(float(latest_changes['unique_wallets']), 2)
                            if not pd.isna(latest_changes['unique_wallets']) else 0,
                    },
                    'new_users': {
                        'total': int(current_week['new_users']),
                        'weekly_rate_change': round(float(latest_changes['new_users']), 2)
                            if not pd.isna(latest_changes['new_users']) else 0,
                    }
                }
            }

        except Exception as e:
            logger.error(f"Failed to process users statistics: {str(e)}")
            raise DataProcessingError(f"Failed to process users statistics: {str(e)}")

    async def get_contract_users(self, freq: str):
        """Get contract users data with caching"""
        start_time = time.time()
        cache_key = f"contract_users_chart:{self.contract_address}"
        total_cache_key = f"contract_users_chart:{self.contract_address}:{freq}"

        try:
            processed_chart_data = await self._get_cached_data(total_cache_key)
            if processed_chart_data:
                return processed_chart_data
            
            transactions_data = await self._get_cached_data(cache_key)
            if not transactions_data:
                transactions_data = await self._fetch_users_data()
                if not transactions_data:
                    logger.warning(f"No transaction data found for chart: {self.contract_address}")
                    return {'status': 'success', 'data': [], 'weekly_rate_change': 0.0}
                
                await self._cache_data(
                    cache_key,
                    transactions_data,
                    self.general_cache_time
                )

            #loop = asyncio.get_running_loop()
            try:
                response = await asyncio.to_thread(
                    self._get_contract_users_stats, transactions_data, freq
                )

            except DataProcessingError as e:
                logger.error(f"Data processing failed during chart generation: {e}")
                raise

            #response = self._get_contract_users_stats(transactions_data, freq)
            await self._cache_data(
                total_cache_key,
                response,
                self.general_cache_time
            )
            logger.info(f"Users data generation completed in {time.time() - start_time:.2f} seconds")
            return response

        except Exception as e:
            self._handle_chart_error(e)

    async def _fetch_users_data(self) -> List[Dict]:
        """Fetch users data"""
        async def process_log(log: LogModel) -> Optional[Dict]:
            try:
                block, tx = await asyncio.gather(
                    self.web3.eth.get_block(log.blockNumber),
                    self.web3.eth.get_transaction(log.transactionHash),
                )

                return {
                    'timestamp': datetime.fromtimestamp(block['timestamp']).isoformat(),
                    'from_address': tx['from']
                }

            except Exception as e:
                logger.warning(f"Failed to process log: {str(e)}")
                return None

        logs = await self._get_contract_logs()
        return await self._process_logs_with_semaphore(logs, process_log)









class UsersHistoryProcessor(ContractChatBase):
    """Handles user data processing"""

    def __init__(self, db: AsyncSession, user_id: int, contract_id: int,
                 web3: AsyncWeb3, contract_address: str, earliest_block: int,
                 redis: Optional[Redis] = None):
        super().__init__(
            db=db,
            user_id=user_id,
            contract_id=contract_id,
            web3=web3,
            contract_address=contract_address,
            earliest_block=earliest_block,    
            redis=redis
        )

    def _get_history_stats(self, transactions_data: List[Dict], freq: str) -> Tuple[pd.DataFrame, float]:
        """Process transaction data into statistics"""
        try:
            df = pd.DataFrame(transactions_data)
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.tz_localize('UTC')
            df.set_index('timestamp', inplace=True)

            def calculate_period_stats(period_freq: str) -> pd.DataFrame:
                return pd.DataFrame({
                    'unique_wallets': df.resample(period_freq)['from_address'].nunique(),
                    'total_transactions': df.resample(period_freq)['block_number'].nunique(),
                })

            stats = calculate_period_stats(freq)
            weekly_stats = calculate_period_stats('W')
            stats.ffill(inplace=True)

            # Calculate weekly metrics
            weekly_changes = weekly_stats.pct_change() * 100
            weekly_changes = weekly_changes.replace([np.inf, -np.inf], 0) # Handle infinite values and NaN
            latest_changes = weekly_changes.iloc[-1] if not weekly_changes.empty else pd.Series({
                'unique_wallets': 0, 'total_transactions': 0
            })

            # Calculate combined growth rate
            normalized_changes = {
                'unique_wallets': latest_changes['unique_wallets'] / 100,
                'total_transactions': latest_changes['total_transactions'] / 100,
            }
            combined_growth_rate = sum(normalized_changes.values()) / len(normalized_changes) * 100
            weekly_metrics = round(float(combined_growth_rate), 2) if not pd.isna(combined_growth_rate) else 0

            return stats, weekly_metrics

        except Exception as e:
            logger.error(f"Failed to process statistics: {str(e)}")
            raise DataProcessingError(f"Failed to process statistics: {str(e)}")

    def _prepare_history_data(self, time_series_stats: pd.DataFrame) -> List[Dict]:
        """Prepare data for frontend display"""
        try:
            return [{
                'timestamp': index.isoformat(),
                'uniqueWallets': int(row['unique_wallets']),
                'totalTransactions': float(row['total_transactions']) if not pd.isna(row['total_transactions']) else 0.0,
            } for index, row in time_series_stats.iterrows()]

        except Exception as e:
            logger.error(f"Failed to prepare frontend data: {str(e)}")
            raise DataProcessingError(f"Failed to prepare frontend data: {str(e)}")



    async def get_history_chart(self, freq: str):
        """Get data chart with caching"""
        start_time = time.time()
        cache_key = f"history_chart:{self.contract_address}"
        total_cache_key = f"history_chart:{self.contract_address}:{freq}"

        try:
            processed_chart_data = await self._get_cached_data(total_cache_key)
            if processed_chart_data:
                return processed_chart_data
            
            transactions_data = await self._get_cached_data(cache_key)
            if not transactions_data:
                transactions_data = await self._fetch_history_data()
                if not transactions_data:
                    logger.warning(f"No transaction data found for chart: {self.contract_address}")
                    return {'status': 'success', 'data': [], 'weekly_rate_change': 0.0}
                
                await self._cache_data(
                    cache_key,
                    transactions_data,
                    self.general_cache_time
                )

            #loop = asyncio.get_running_loop()
            try:
                stats, weekly_metrics = await asyncio.to_thread(
                    self._get_history_stats, transactions_data, freq
                )
                frontend_data = await asyncio.to_thread(
                    self._prepare_history_data, stats
                )

            except DataProcessingError as e:
                logger.error(f"Data processing failed during chart generation: {e}")
                raise

            result_data = {
                'status': 'success',
                'data': frontend_data[::-1],
                'weekly_rate_change': weekly_metrics
            }
            await self._cache_data(
                total_cache_key,
                result_data,
                self.general_cache_time
            )
            #stats, weekly_metrics = self._get_history_stats(transactions_data, freq)
            #frontend_data = self._prepare_history_data(stats)
            logger.info(f"Chart generation completed in {time.time() - start_time:.2f} seconds")
            return result_data

        except Exception as e:
            self._handle_chart_error(e)

    async def _fetch_history_data(self) -> List[Dict]:
        """Fetch chart data"""
        async def process_log(log: LogModel) -> Optional[Dict]:
            try:
                block, tx = await asyncio.gather(
                    self.web3.eth.get_block(log.blockNumber),
                    self.web3.eth.get_transaction(log.transactionHash),
                )

                return {
                    'timestamp': datetime.fromtimestamp(block['timestamp']).isoformat(),
                    'from_address': tx['from'],
                    'block_number': log.blockNumber
                }
            except Exception as e:
                logger.warning(f"Failed to process log: {str(e)}")
                return None

        logs = await self._get_contract_logs()
        return await self._process_logs_with_semaphore(logs, process_log)





    async def _get_user_info(self, from_address: str) -> Dict:
        """
        Get user information for a transaction address.
        Returns name, wallet address, credit score and interaction count if user exists in DB,
        otherwise returns just the address.
        """
        try:
            # Query the Account table to find the user_id for this address
            result = await self.db.execute(select(AccountWallet).filter(
                AccountWallet.address == from_address
            ))
            account = result.scalar_one_or_none()

            if not account:
                return {
                    'address': from_address,
                    'exists_in_db': False,
                    'name': None,
                    "created_at": None,
                    'credit_score': None,
                    'user_id': None
                }

            # Get the wallet score for this user
            result = await self.db.execute(select(WalletScore).filter(
                WalletScore.user_id == account.user_id,
                WalletScore.wallet_address == account.id
            ).order_by(WalletScore.created_at.desc()))
            wallet_score = result.scalar_one_or_none()

            # Get user details
            result = await self.db.execute(select(User).filter(User.id == account.user_id))
            user = result.scalar_one_or_none()

            return {
                'address': from_address,
                'exists_in_db': True,
                'name': user.first_name if user else None,
                "created_at": user.date_created if user else None,
                'credit_score': wallet_score.total_score if wallet_score else None,
                'user_id': account.user_id
            }

        except Exception as e:
            logger.error(f"Error getting user info for address {from_address}: {str(e)}")
            return {
                'address': from_address,
                'exists_in_db': False,
                'name': None,
                "created_at": None,
                'credit_score': None,
                'user_id': None
            }



    async def get_unique_users(self) -> Dict:
        """Get list of unique users who have interacted with the contract"""

        start_time = time.time()
        cache_key = f"get_unique_users:{self.contract_address}"

        try:
            processed_chart_data = await self._get_cached_data(cache_key)
            if processed_chart_data:
                return processed_chart_data

            logs = await self._get_contract_logs()
            if not logs:
                 return {
                    'status': 'success',
                    'total_unique_users': 0,
                    'total_interactions': 0,
                    'average_interactions_per_user': 0,
                    'users': []
                }
            
            processed_log_data = [] # To store results from gather

            async def fetch_log_tx_info(log: LogModel) -> Optional[Dict]:
                """Fetches transaction hash and sender for a single log."""
                try:
                    # Fetch ONLY the transaction object, avoid processing yet
                    # Add retries/timeouts if needed around get_transaction
                    tx = await asyncio.wait_for(
                         self.web3.eth.get_transaction(log.transactionHash),
                         timeout=30.0 # Example timeout
                    )
                    if tx and tx.get('from'):
                        return {
                            'tx_hash': log.transactionHash, # Store as hex string
                            'from_address': tx['from'],
                            # Include other log details if needed for interaction definition
                            'log_index': log.logIndex
                        }
                    else:
                         logger.warning(f"Transaction data missing or incomplete for hash")
                         return None
                except asyncio.TimeoutError:
                     logger.warning(f"Timeout fetching transaction")
                     return None
                except Exception as e:
                    logger.warning(f"Failed to process log for tx", exc_info=True)
                    return None

            # Limit concurrency of get_transaction calls
            semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
            """
            tasks = []
            for log in logs:
                async def task_wrapper(log_item):
                    async with semaphore:
                        return await fetch_log_tx_info(log_item)
                tasks.append(task_wrapper(log))

            results = await asyncio.gather(*tasks, return_exceptions=True)
            """

            async def task_wrapper(log_item):
                async with semaphore:
                    return await fetch_log_tx_info(log_item)

            results = await asyncio.gather(*(task_wrapper(log) for log in logs), return_exceptions=True)

            # Filter out None results (failures)
            processed_log_data = [
                res for res in results 
                if res is not None and not isinstance(res, Exception)
            ]
            
            if not processed_log_data:
                logger.warning("No logs could be processed successfully.")
                return { 'status': 'success', 'total_unique_users': 0, 'total_interactions': 0, 'average_interactions_per_user': 0, 'users': [] }


            user_tx_hash = defaultdict(set)
            user_interaction_count = defaultdict(int)

            for log_data in processed_log_data:
                addr = log_data['from_address']
                tx_hash = log_data['tx_hash']
                user_tx_hash[addr].add(tx_hash)
                user_interaction_count[addr] += 1

            unique_addresses = list(user_tx_hash.keys())


            user_info_map = {}
            if unique_addresses:
                info_tasks = []

                async def fetch_user_info_task(address):
                    try:
                        info = await self._get_user_info(address)
                        return address, info
                    except Exception as e:
                        logger.warning(f"Failed to get user info for {address}", exc_info=True)
                        #return address, {'address': address, 'name': None, 'label': 'Error'}
            
            for addr in unique_addresses:
                info_tasks.append(fetch_user_info_task(addr))

            info_results = await asyncio.gather(*info_tasks)
            user_info_map = dict(info_results)

            #user_interactions = {}

            final_users_list = []
            total_interactions_overall = 0

            for address in unique_addresses:
                num_unique_tx = len(user_tx_hash[address])
                interaction_count = user_interaction_count[address]
                user_info = user_info_map.get(address, {'address': address})

                user_data = {
                    **user_info,
                    'interaction_count': interaction_count,
                    'unique_transactions': num_unique_tx
                }
                final_users_list.append(user_data)
                total_interactions_overall += interaction_count

            final_users_list.sort(key=lambda x: x['interaction_count'], reverse=True)

            total_unique_users_count = len(final_users_list)
            avg_interactions_per_user = total_interactions_overall / total_unique_users_count if total_unique_users_count > 0 else 0

            """
            async def process_log(log: LogModel) -> None:
                try:
                    tx = await self.web3.eth.get_transaction(log.transactionHash)
                    from_address = tx['from']
                    tx_hash = log.transactionHash

                    if from_address not in user_interactions:
                        user_info = await self._get_user_info(from_address)
                        user_interactions[from_address] = {
                            'user_info': user_info,
                            'interaction_count': 1,
                            'transactions': [tx_hash]
                        }
                    else:
                        if tx_hash not in user_interactions[from_address]['transactions']:
                            user_interactions[from_address]['interaction_count'] += 1
                            user_interactions[from_address]['transactions'].append(tx_hash)

                except Exception as e:
                    logger.warning(f"Failed to process log for transaction {log.transactionHash}: {str(e)}")
            
            semaphore = asyncio.Semaphore(MAX_CONCURRENT_REQUESTS)
            async with semaphore:
                await asyncio.gather(*[process_log(log) for log in logs])

            unique_users = []
            total_interactions = 0

            for address, data in user_interactions.items():
                user_data = {
                    **data['user_info'],
                    'interaction_count': data['interaction_count'],
                    'unique_transactions': len(data['transactions'])
                }
                unique_users.append(user_data)
                total_interactions += data['interaction_count']

            # Sort by interaction count in descending order
            unique_users.sort(key=lambda x: x['interaction_count'], reverse=True)
            """
            transactions_data = {
                'status': 'success',
                'total_unique_users': total_unique_users_count,
                'total_interactions': total_interactions_overall, # Based on log count
                'average_interactions_per_user': avg_interactions_per_user,
                'users': final_users_list
            }
            
            await self._cache_data(
                cache_key,
                transactions_data,
                self.general_cache_time
            )
            logger.info(f"Chart generation completed in {time.time() - start_time:.2f} seconds")

            return transactions_data
        except Exception as e:
            logger.error(f"Failed to get unique users: {str(e)}", exc_info=True)
            raise DataProcessingError(f"Failed to get unique users: {str(e)}")




@class_exponential_backoff()
class ContractChat(DataChartProcessor, AnalyticsProcessor, UsersProcessor, UsersHistoryProcessor):
    """Main class combining all processors"""

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass
