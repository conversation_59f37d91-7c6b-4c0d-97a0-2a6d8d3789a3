from fastapi import FastAP<PERSON>, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from starlette.requests import Request
from api.db.database import create_database, async_db_engine
from api.v1.auth.router import app as auths
from api.v1.user.router import app as users
from api.v1.subscription.router import app as subscription
from api.v1.wallet.router import app as wallet
from api.v1.payment.router import app as payment
from api.v1.plugins.router import app as plugins
from api.v1.websockets.router import app as websockets
from api.v1.erc20_contract.router import app as erc20_contract
from api.v1.deploy_contracts.router import app as deploy_contracts
from api.v1.erc721_contract.router import app as erc721_contract
from api.v1.contracts.router import app as contracts
from api.v1.account.router import app as account
from api.v1.backup.router import app as backup

from starlette.middleware.sessions import SessionMiddleware
from config import config  # Import from config.py 
from fastapi.staticfiles import StaticFiles
import os

# Import our new logging configuration
from api.core.logging_config import setup_logging, get_logger

# Import Prometheus metrics setup
from api.core.metrics import MetricsManager

# Initialize logging with settings from config
setup_logging(
    log_level=config.LOG_LEVEL,
    log_to_file=config.LOG_TO_FILE,
    log_to_console=config.LOG_TO_CONSOLE,
    use_json_format=config.PYTHON_ENV == "production",  # Use JSON format in production
    environment=config.PYTHON_ENV
)

app = FastAPI()

from api.v1.websockets.services import notification_manager
from api.v1.websockets.connection_manager import ConnectionManager
from api.core.general_dep import RedisManager
from api.core.blockchain_dep import Web3Provider

from contextlib import asynccontextmanager
import logging
import asyncio
import signal
import time

# Use our get_logger function instead of direct logging.getLogger
logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    services = []
    notification_task = None

    try:

        try:
            await create_database()
            logger.info("Database tables creation process finished.")
        except Exception as e:
            logger.critical(f"Failed to create database tables: {e}", exc_info=True)
            

        try:
            redis = await RedisManager.get_client()
            if redis:
                logger.info("Redis connection initialized")
                services.append(("Redis", RedisManager.close))
            else:
                logger.warning("Redis not available, app will continue without cache")
        except Exception as e:
            logger.error(f"Redis initialization failed: {e}")

        try:
            await notification_manager.initialize()
            notification_task = asyncio.create_task(
                notification_manager.listen_for_notifications()
                )
            logger.info("Notification manager initialized")
        except Exception as e:
            logger.error(f"Notification manager initialization failed: {e}")

        try:
            connection_manager = ConnectionManager()
            await connection_manager.start()
            services.append(("ConnectionManager", connection_manager.stop))
            logger.info("Connection manager started")
        except Exception as e:
            logger.error(f"Connection manager initialization failed: {e}")

        yield
        
    finally:
        try:
            if Web3Provider._instance:
                await Web3Provider.close()
        except Exception as e:
            logger.error(f"Web3 cleanup failed: {e}")

        if notification_task:
            try:
                notification_task.cancel()
                await notification_task
            except asyncio.CancelledError:
                pass  # Expected when canceling
            except Exception as e:
                logger.info(f"An error occurred during task cancellation: {e}")

        for service_name, service_func in services:
            try:
                await service_func()
                logger.info(f"{service_name} closed")
            except Exception as e:
                logger.error(f"Failed to close {service_name}: {e}")

        if async_db_engine:
            logger.info("Disposing database engine...")
            await async_db_engine.dispose()
            logger.info("Database engine disposed.")


app = FastAPI(lifespan=lifespan)

# Setup Prometheus metrics
metrics = MetricsManager.get_instance(app).get_metrics()

# Add middleware to track request metrics
@app.middleware("http")
async def track_requests(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    
    metrics['request_count'].labels(
        method=request.method,
        endpoint=request.url.path,
        status_code=response.status_code
    ).inc()
    
    metrics['request_latency'].labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(duration)
    
    return response

# CORS origins
origins = [
    "https://ola-atlas-apis.azurewebsites.net",
    "https://atlas-api.services.stellus.io",
    "http://localhost:8000",
    "http://localhost:3000",
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins, change to strict as needed
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Use SECRET_KEY from config.py
SECRET_KEY = config.JWT_SECRET
if not SECRET_KEY:
    raise Exception('SECRET_KEY is missing in config.py')

ERC721_MINT = "./uploads/mint_erc721"
AVATER_FOLDER = "./uploads/avatars"
CONTRACT_LOGO = "./uploads/contract_logo"

try:
    os.makedirs(AVATER_FOLDER, exist_ok=True)
    os.makedirs(ERC721_MINT, exist_ok=True)
    os.makedirs(CONTRACT_LOGO, exist_ok=True)
except OSError as e:
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail="Failed to create upload directory"
    )

app.mount("/avatars", StaticFiles(directory="./uploads/avatars"), name="avatars")
app.mount("/mint_erc721", StaticFiles(directory="./uploads/mint_erc721"), name="mint_erc721")
app.mount("/contract_logo", StaticFiles(directory="./uploads/contract_logo"), name="contract_logo")

# Session Middleware
app.add_middleware(SessionMiddleware, secret_key=SECRET_KEY)

# Include Routers
app.include_router(backup, tags=["Backup"])
app.include_router(deploy_contracts, tags=["Deploy Contracts"])
app.include_router(account, tags=["Account"])
app.include_router(auths, tags=["Auth"])
app.include_router(websockets, tags=["WebSockets"])
app.include_router(wallet, tags=["Wallet"])
app.include_router(users, tags=["Users"])
app.include_router(plugins, tags=["RWA Plugin"])
app.include_router(subscription, tags=["Subscriptions"])
app.include_router(payment, tags=["Payment"])
app.include_router(websockets, tags=["WebSockets"])
app.include_router(erc721_contract, tags=["Erc721 Contract"])
app.include_router(erc20_contract, tags=["Erc20 Contract"])
app.include_router(contracts, tags=["Contracts"])



# Root endpoint
@app.get("/")
async def root(request: Request):
    return {"message": "Hello World"}





#redis
# Uvicorn runs the app on Azure via Gunicorn with Uvicorn worker, so this is not needed
# if __name__ == "__main__":
#     uvicorn.run("main:app", host="0.0.0.0", port=int(os.getenv("PORT", 8000)), reload=True, log_level="debug")
