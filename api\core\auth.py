from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Annotated, Optional
from api.v1.auth.services import Auth, oauth2_scheme
from api.db.database import get_db
import os
from jose import jwt
import secrets
import logging
from config import config


logger = logging.getLogger(__name__)

# Get secret key from environment
SECRET_KEY = os.environ.get("ADMIN_SECRET_KEY")
if not SECRET_KEY:
    from config import config
    SECRET_KEY = config.JWT_SECRET
    logger.warning("ADMIN_SECRET_KEY not found in environment. Using JWT_SECRET instead (less secure).")


SUPER_ADMIN_EMAILS = config.SUPER_ADMIN_EMAILS

# Add a constant-time comparison function to prevent timing attacks
def constant_time_compare(val1, val2):
    """
    Compare two strings in constant time to prevent timing attacks
    """
    if len(val1) != len(val2):
        return False
    
    result = 0
    for x, y in zip(val1, val2):
        result |= ord(x) ^ ord(y)
    
    return result == 0

async def require_super_admin(
    token: Annotated[str, Depends(oauth2_scheme)],
    db: Session = Depends(get_db)
):
    """
    Dependency that ensures the current user is a super admin.
    Requires valid authentication and email must be in the SUPER_ADMIN_EMAILS list.
    
    This is a high-security function that should only be used for critical operations.
    
    Returns the authenticated user if they are a super admin.
    Raises HTTPException with 403 status code if not a super admin.
    """
    try:
        # Get the current user using the existing authentication
        user = await Auth.get_current_user(token=token, db=db)
        
        # Check if user's email is in the super admin list
        is_super_admin = False
        for admin_email in SUPER_ADMIN_EMAILS:
            if constant_time_compare(user.email.lower().strip(), admin_email.lower().strip()):
            #if user.email.lower().strip() == admin_email.lower().strip():
                is_super_admin = True
                break
        
        if not is_super_admin:
            logger.warning(f"Unauthorized super admin access attempt: {user.id}")
            
            # Add a small random delay to further prevent timing attacks
            secrets.token_bytes(32)  # This introduces a small random delay
            
            # Use a generic error message to avoid revealing admin emails
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to perform this action",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Log successful super admin access (audit trail)
        logger.info(f"Super admin action performed by: {user.id}")
        
        return user
    
    except Exception as e:
        # Generic error to prevent information disclosure
        logger.error(f"Error in super admin authentication: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You don't have permission to perform this action",
            headers={"WWW-Authenticate": "Bearer"},
        ) 