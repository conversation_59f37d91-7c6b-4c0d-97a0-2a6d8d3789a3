from sqlalchemy.ext.asyncio import AsyncSession
from api.v1.websockets.models import NotificationType, NotificationPriority
from api.v1.websockets.services import NotificationService
from api.v1.websockets.schemas import NotificationCreate
from datetime import datetime
from typing import Optional

def handle_subscription_notification(user_id: int, action: str, subscription_data: Optional[dict] = None):
    """Handle subscription-related notifications"""
    messages = {
        "create_subscription": "New subscription plan created successfully.",
        "update_subscription": "Subscription plan updated successfully.",
        "get_subscriptions": "Subscription plans retrieved successfully.",
        "get_subscription": "Subscription plan details retrieved.",
        "subscribe_user": "Successfully subscribed to the plan.",
        "get_user_subscriptions": "Your subscription details retrieved.",
        "cancel_subscription": "Subscription cancelled successfully.",
        "subscription_expired": "Your subscription has expired."
    }

    titles = {
        "create_subscription": "New Subscription Plan",
        "update_subscription": "Plan Updated",
        "get_subscriptions": "Plans Retrieved",
        "get_subscription": "Plan Details",
        "subscribe_user": "Subscription Active",
        "get_user_subscriptions": "Subscription Details",
        "cancel_subscription": "Subscription Cancelled",
        "subscription_expired": "Subscription Expired"
    }

    if action in messages:
        message = messages[action]
        title = titles.get(action, "Subscription Update")

        # Create notification with enhanced structure
        notification = NotificationCreate(
            title=title,
            message=message,
            type=NotificationType.SUBSCRIPTION,
            priority=NotificationPriority.HIGH if action in ["subscription_expired", "cancel_subscription"]
                    else NotificationPriority.NORMAL,
            metadata={
                "subscription_details": {
                    "action_type": action,
                    "status": "expired" if action == "subscription_expired"
                             else "cancelled" if action == "cancel_subscription"
                             else "active",
                    "detail": {
                        "plan_name": subscription_data.get("plan_name") if subscription_data else None,
                        "plan_duration": subscription_data.get("duration") if subscription_data else None,
                        "plan_price": subscription_data.get("price") if subscription_data else None,
                        "expiry_date": subscription_data.get("expiry_date") if subscription_data else None
                    }
                }
            },
            action_url=subscription_data.get("management_url") if subscription_data else None
        )

        notification_service = NotificationService()
        notification_service.publish_notification(user_id=user_id, notification=notification)