from logging.config import fileConfig
from api.db.database import Base
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context
from config import config as myconfig

# Import all models to ensure they are registered with Base.metadata
# This is important for Alembic autogenerate to work correctly
from api.v1.user.models import *
from api.v1.auth.models import *
from api.v1.account.models import *
from api.v1.erc721_contract.models import *
from api.v1.erc20_contract.models import *
from api.v1.deploy_contracts.models import *
from api.v1.contracts.models import *
from api.v1.subscription.models import *
from api.v1.payment.models import *
from api.v1.websockets.models import *
from api.v1.wallet.models import *
from api.v1.plugins.models import *


"""
DB_TYPE = config("DB_TYPE")
DB_NAME = config("DB_NAME")
DB_USER = config("DB_USER")
DB_PASSWORD = config("DB_PASSWORD")
DB_HOST = config("DB_HOST")
DB_PORT = config("DB_PORT")
MYSQL_DRIVER = config("MYSQL_DRIVER")
"""
DB_TYPE = myconfig.DB_TYPE
DB_NAME = myconfig.DB_NAME
DB_USER = myconfig.DB_USER
DB_PASSWORD = myconfig.DB_PASSWORD
DB_HOST = myconfig.DB_HOST
DB_PORT = myconfig.DB_PORT
MYSQL_DRIVER = myconfig.MYSQL_DRIVER
DATABASE_URL = ""

if DB_TYPE == "mysql":
    DATABASE_URL = f'mysql+{MYSQL_DRIVER}://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}'
elif DB_TYPE == "postgresql":
    DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
else:
    DATABASE_URL = "sqlite:///./database.db"

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

config.set_main_option('sqlalchemy.url', DATABASE_URL)
# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata
target_metadata = Base.metadata

# other values from the config, defined by the needs of env.py,
# can be acquired:
# my_important_option = config.get_main_option("my_important_option")
# ... etc.


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.

    """
    #url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=DATABASE_URL,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.
    """

    connectable = engine_from_config(
        config.get_section(config.config_ini_section, {}),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )
    
    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
