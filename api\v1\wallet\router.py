from fastapi import Depends, APIRouter, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from . import services, schemas
from api.db.database import get_db
from api.v1.user import schemas as user_schema
from api.core.dependencies import is_authenticated
from .exceptions import handle_wallet_notification


app = APIRouter(tags=["Wallet"])

@app.post("/wallet/connect", response_model=schemas.WalletAddressResponse)
async def connect_wallet(
    wallet_address: str,
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db)
):
    """
    API endpoint to connect a wallet
    """
    try:
        result = await services.wallet_router(
            wallet_address=wallet_address,
            user=user,
            db=db
        )
        handle_wallet_notification(user_id=user.id, action="connect_wallet",
                                         wallet_address=wallet_address)
        return result
    except Exception as e:
        handle_wallet_notification(user_id=user.id, action="wallet_connect_failed")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/wallet/details", response_model=schemas.WalletAddressResponse)
async def get_wallet(
    id: int,
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db)
):
    """
    API endpoint to get wallet details
    """
    try:
        result = await services.wallet_details(
            id=id,
            user=user,
            db=db
        )
        #await handle_wallet_notification(db=db, user_id=user.id, action="get_wallet_details")
        return result
    except Exception as e:
        #await handle_wallet_notification(db=db, user_id=user.id, action="wallet_error")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/wallet/get", response_model=schemas.WalletAddressList)
async def get_wallets(
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db)
):
    """
    API endpoint to get all user wallets
    """
    try:
        # Use the async wallet_get service function instead of direct db query
        all_wallets = await services.wallet_get(user=user, db=db)
        return {"wallets": all_wallets}
    except ValueError as e:
        #await handle_wallet_notification(db=db, user_id=user.id, action="wallet_error" )
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException as e:
        # Re-raise HTTPExceptions from the service
        raise e
    except Exception as e:
        #await handle_wallet_notification(db=db, user_id=user.id, action="wallet_error")
        raise HTTPException(status_code=500, detail=f"Wallet connection failed; {str(e)}")

"""
@app.post("/wallet/connect", response_model=schemas.WalletAddressResponse)
async def connect_wallet(
    wallet_connect: schemas.WalletConnectSession,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db),
    wc_client: WCClient = Depends(get_wc_client)
):
    try:
        # Initialize WalletConnect session
        wc_client = wc_client.from_wc_uri(wallet_connect.uri)
        session_data = wc_client.open_session()

        # Extract wallet address from session data
        wallet_address = session_data[1]  # Adjust index based on your session data structure
        chain_id = session_data[2].get('chainId', 1)  # Default to mainnet if not specified

        # Create or get existing wallet record
        wallet_data = schemas.WalletAddressCreate(
            address=wallet_address,
            chain_id=chain_id
        )

        db_wallet = services.get_wallet_address(db, wallet_address)
        if not db_wallet:
            db_wallet = services.create_wallet_address(db, wallet_data)

        # Update verification status
        db_wallet = services.update_wallet_verification(
            db,
            wallet_address,
            session_id=session_data[0]
        )

        return db_wallet

    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Failed to establish WalletConnect session: {str(e)}"
        )
"""