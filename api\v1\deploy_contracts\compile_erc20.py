import solcx
from solcx import compile_standard
import os
import json
import asyncio
import aiofiles
from .exceptions import CompilationError, FileUploadError, logger


async def setup_solc():
    try:
        # Using run_in_executor to make synchronous solcx operations non-blocking
        await asyncio.to_thread(solcx.install_solc, '0.8.28')
        await asyncio.to_thread(solcx.set_solc_version, '0.8.28')
    except Exception as e:
        logger.error(f"Failed to install/set solc version: {str(e)}")
        raise CompilationError(f"Solidity compiler setup failed: {str(e)}")


class Compile:
    def __init__(self):
        self.contract_file = None

    @classmethod
    async def create(cls):
        """Factory method to create and initialize the class asynchronously"""
        instance = cls()
        await instance.initialize()
        return instance

    async def initialize(self):
        try:
            contract_path = os.path.join("api", "v1", "deploy_contracts", "erc_twenty.sol")

            if not os.path.exists(contract_path):
                raise FileNotFoundError(f"Contract file not found at {contract_path}")
            
            # Using aiofiles for non-blocking file operations
            async with aiofiles.open(contract_path, 'r') as file:
                contract_file = await file.read()
        
            if not contract_file.strip():
                raise ValueError("Contract file is empty")
            
            self.contract_file = contract_file

        except FileNotFoundError as e:
            logger.error(f"Contract file error: {str(e)}")
            raise FileUploadError(f"Failed to load ERC20 contract: {str(e)}")
        except Exception as e:
            logger.error(f"Initialization error: {str(e)}")
            raise CompilationError(f"Failed to initialize ERC20 compilation: {str(e)}")

    async def compile(self):
        try:                
            # Run compile_standard in a thread pool to avoid blocking
            compiled_solidity = await asyncio.to_thread(
                compile_standard,
                {
                    "language": "Solidity",
                    "sources": {
                        "erc_twenty.sol": {
                            "content": self.contract_file
                        }
                    },
                    "settings": {
                        "outputSelection": {
                            "*": {
                                "*": ["abi", "metadata", "evm.bytecode", "evm.sourceMap"]
                            }
                        }
                    }
                },
                solc_version="0.8.28"
            )
                
            output_path = os.path.join("api", "v1", "deploy_contracts", "compiled_erc20.json")

            try:    
                # Asynchronous file writing
                async with aiofiles.open(output_path, "w") as file:
                    await file.write(json.dumps(compiled_solidity))
            except Exception as e:
                logger.error(f"Failed to save compiled contract: {str(e)}")
                raise FileUploadError(f"Failed to save compiled ERC20 contract: {str(e)}")

            return compiled_solidity

        except solcx.exceptions.SolcError as e:
            logger.error(f"Solidity compilation error: {str(e)}")
            raise CompilationError(f"ERC20 contract compilation failed: {str(e)}")
        except Exception as e:
            logger.error(f"Compilation error: {str(e)}")
            raise CompilationError(f"Unexpected error during ERC20 compilation: {str(e)}")





class Compile721:
    def __init__(self):
        self.contract_file = None

    @classmethod
    async def create(cls):
        instance = cls()
        await instance.initialize()
        return instance
    
    async def initialize(self):
        try:
            contract_path = os.path.join("api", "v1", "deploy_contracts", "erc_seventwoone.sol")

            if not os.path.exists(contract_path):
                raise FileNotFoundError(f"Contract file not found at {contract_path}")
            
            # Using aiofiles for non-blocking file operations
            async with aiofiles.open(contract_path, 'r') as file:
                contract_file = await file.read()
        
            if not contract_file.strip():
                raise ValueError("Contract file is empty")
                        
            self.contract_file = contract_file

        except FileNotFoundError as e:
            logger.error(f"Contract file error: {str(e)}")
            raise FileUploadError(f"Failed to load ERC721 contract: {str(e)}")
        except Exception as e:
            logger.error(f"Initialization error: {str(e)}")
            raise CompilationError(f"Failed to initialize ERC721 compilation: {str(e)}")

    async def compile(self):
        try:
            # Run compile_standard in a thread pool to avoid blocking
            compiled_solidity = await asyncio.to_thread(
                compile_standard,
                {
                    "language": "Solidity",
                    "sources": {"erc_seventwoone.sol": {"content": self.contract_file}},
                    "settings": {
                        "outputSelection": {
                            "*": {
                                "*": ["abi", "metadata", "evm.bytecode", "evm.sourceMap"]
                            }
                        },
                        "remappings": [
                            "@openzeppelin/contracts=node_modules/@openzeppelin/contracts"
                        ]
                    },
                },
                allow_paths=["node_modules"]
            )
            output_path = os.path.join("api", "v1", "deploy_contracts", "compiled_erc721.json")
            
            try:
                # Asynchronous file writing
                async with aiofiles.open(output_path, "w") as file:
                    await file.write(json.dumps(compiled_solidity))
            except Exception as e:
                logger.error(f"Failed to save compiled contract: {str(e)}")
                raise FileUploadError(f"Failed to save compiled ERC721 contract: {str(e)}")

            return compiled_solidity

        except solcx.exceptions.SolcError as e:
            logger.error(f"Solidity compilation error: {str(e)}")
            raise CompilationError(f"ERC721 contract compilation failed: {str(e)}")
        except Exception as e:
            logger.error(f"Compilation error: {str(e)}")
            raise CompilationError(f"Unexpected error during ERC721 compilation: {str(e)}")
