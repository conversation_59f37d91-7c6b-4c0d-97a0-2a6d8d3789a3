from pydantic import BaseModel, EmailStr
from api.v1.user.schemas import ShowUser
from datetime import datetime
from typing import Optional
from pydantic import BaseModel

class Login(BaseModel):
    email: EmailStr
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

class APIAuth(BaseModel):
    api_key: str
    #app_id: str


class ResetPassword(BaseModel):
    current_password: str
    new_password: str
    confirm_password: str

class APIKEY(BaseModel):
    id: int
    name: str
    api_key: str
    subscription_plan_id: int
    date_created: datetime
    active: bool



class create_APIKEY(BaseModel):
    id: int
    name: str


class TokenData(BaseModel):
    id:int
    email:str

class SignUpResponse(BaseModel):
    data: ShowUser
    access_token: str 
    subscription_plan: str
    token_type: str
    message: str
    pass

class LoginResponse(BaseModel):
    data: ShowUser
    access_token: str 
    #refresh_token: str
    token_type: str
    pass




class GoogleUserInfo(BaseModel):
    id: Optional[str] = None
    email: Optional[str] = None
    verified_email: Optional[bool] = None
    name: Optional[str] = None
    given_name: Optional[str] = None
    family_name: Optional[str] = None
    picture: Optional[str] = None