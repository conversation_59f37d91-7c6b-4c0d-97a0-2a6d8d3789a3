"""Initial schema based on current models

Revision ID: 6e94baa2a6a3
Revises: 
Create Date: 2025-04-21 13:34:40.486345

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6e94baa2a6a3'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('nft_metadata',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image', sa.String(), nullable=True),
    sa.Column('attributes', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_nft_metadata_id'), 'nft_metadata', ['id'], unique=False)
    op.create_table('subscription_plan',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('tier', sa.Enum('BASIC', 'PROFESSIONAL', 'ENTERPRISE', name='subscriptiontier'), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('price_monthly', sa.BIGINT(), nullable=False),
    sa.Column('price_yearly', sa.BIGINT(), nullable=False),
    sa.Column('api_rate_limit', sa.Integer(), nullable=False),
    sa.Column('daily_request_limit', sa.Integer(), nullable=False),
    sa.Column('monthly_request_limit', sa.Integer(), nullable=False),
    sa.Column('allowed_endpoints', sa.JSON(), nullable=False),
    sa.Column('max_payload_size', sa.Integer(), nullable=False),
    sa.Column('has_priority_support', sa.Boolean(), nullable=True),
    sa.Column('has_advanced_features', sa.Boolean(), nullable=True),
    sa.Column('concurrent_requests', sa.Integer(), nullable=False),
    sa.Column('max_response_time', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_subscription_plan_id'), 'subscription_plan', ['id'], unique=False)
    op.create_index(op.f('ix_subscription_plan_name'), 'subscription_plan', ['name'], unique=True)
    op.create_table('token_balances',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('token_id', sa.Integer(), nullable=True),
    sa.Column('balance', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_token_balances_id'), 'token_balances', ['id'], unique=False)
    op.create_table('tokens',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('symbol', sa.String(), nullable=True),
    sa.Column('contract_address', sa.String(), nullable=True),
    sa.Column('decimals', sa.Integer(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('contract_address')
    )
    op.create_index(op.f('ix_tokens_id'), 'tokens', ['id'], unique=False)
    op.create_table('transactions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tx_hash', sa.String(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('contract_address', sa.String(), nullable=True),
    sa.Column('timestamp', sa.String(), nullable=True),
    sa.Column('method', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_transactions_id'), 'transactions', ['id'], unique=False)
    op.create_index(op.f('ix_transactions_tx_hash'), 'transactions', ['tx_hash'], unique=True)
    op.create_table('user',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('first_name', sa.String(length=255), nullable=True),
    sa.Column('last_name', sa.String(length=255), nullable=True),
    sa.Column('email', sa.String(length=500), nullable=False),
    sa.Column('password', sa.String(length=500), nullable=False),
    sa.Column('google_sub', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('verification_token', sa.String(length=255), nullable=True),
    sa.Column('verification_token_expiry', sa.DateTime(timezone=True), nullable=True),
    sa.Column('reset_token', sa.String(length=255), nullable=True),
    sa.Column('reset_token_expiry', sa.DateTime(timezone=True), nullable=True),
    sa.Column('date_created', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_updated', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('google_sub')
    )
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=False)
    op.create_index(op.f('ix_user_id'), 'user', ['id'], unique=True)
    op.create_table('account',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.BIGINT(), nullable=False),
    sa.Column('address', sa.String(), nullable=True),
    sa.Column('encrypted_private_key', sa.String(), nullable=False),
    sa.Column('encryption_salt', sa.String(length=40), nullable=False),
    sa.Column('encryption_nonce', sa.String(length=24), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_account_address'), 'account', ['address'], unique=True)
    op.create_index(op.f('ix_account_id'), 'account', ['id'], unique=False)
    op.create_index(op.f('ix_account_user_id'), 'account', ['user_id'], unique=False)
    op.create_table('api_keys',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.BIGINT(), nullable=True),
    sa.Column('subscription_plan_id', sa.BIGINT(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=True),
    sa.Column('api_key', sa.String(length=500), nullable=False),
    sa.Column('key_hash', sa.Text(), nullable=False),
    sa.Column('active', sa.Boolean(), nullable=True),
    sa.Column('current_monthly_requests', sa.Integer(), nullable=True),
    sa.Column('current_daily_requests', sa.Integer(), nullable=True),
    sa.Column('last_request_time', sa.DateTime(timezone=True), nullable=True),
    sa.Column('requests_this_minute', sa.Integer(), nullable=True),
    sa.Column('last_reset_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('date_created', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_used', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['subscription_plan.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_api_keys_api_key'), 'api_keys', ['api_key'], unique=True)
    op.create_index(op.f('ix_api_keys_id'), 'api_keys', ['id'], unique=False)
    op.create_index(op.f('ix_api_keys_user_id'), 'api_keys', ['user_id'], unique=False)
    op.create_table('blacklist_tokens',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('created_by', sa.BIGINT(), nullable=True),
    sa.Column('token', sa.String(length=255), nullable=True),
    sa.Column('date_created', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_blacklist_tokens_created_by'), 'blacklist_tokens', ['created_by'], unique=False)
    op.create_index(op.f('ix_blacklist_tokens_id'), 'blacklist_tokens', ['id'], unique=False)
    op.create_index(op.f('ix_blacklist_tokens_token'), 'blacklist_tokens', ['token'], unique=False)
    op.create_table('contract_deployments',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('symbol', sa.String(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image', sa.String(), nullable=True),
    sa.Column('contract_address', sa.String(), nullable=False),
    sa.Column('contract_type', sa.Enum('ER20', 'ERC721', 'NONE', name='transactiontype'), nullable=False),
    sa.Column('contract_status', sa.Enum('DEPLOYED', 'FAILED', name='contractstatus'), nullable=False),
    sa.Column('transaction_hash', sa.String(), nullable=False),
    sa.Column('transaction_from', sa.String(), nullable=False),
    sa.Column('transaction_to', sa.String(), nullable=True),
    sa.Column('transaction_status', sa.Enum('SUCCESS', 'FAILURE', name='transactionstatus'), nullable=True),
    sa.Column('transaction_index', sa.Integer(), nullable=True),
    sa.Column('gas_used', sa.BIGINT(), nullable=True),
    sa.Column('effective_gas_price', sa.BIGINT(), nullable=True),
    sa.Column('cumulative_gas_used', sa.BIGINT(), nullable=True),
    sa.Column('transaction_type', sa.Integer(), nullable=True),
    sa.Column('block_hash', sa.String(), nullable=False),
    sa.Column('block_number', sa.BIGINT(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_contract_deployments_user_id'), 'contract_deployments', ['user_id'], unique=False)
    op.create_table('notificaiton_prefrence',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('email_notification', sa.Boolean(), nullable=True),
    sa.Column('sms_notification', sa.Boolean(), nullable=True),
    sa.Column('push_notification', sa.Boolean(), nullable=True),
    sa.Column('date_created', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_updated', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notificaiton_prefrence_id'), 'notificaiton_prefrence', ['id'], unique=True)
    op.create_index(op.f('ix_notificaiton_prefrence_user_id'), 'notificaiton_prefrence', ['user_id'], unique=False)
    op.create_table('notifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('message', sa.Text(), nullable=False),
    sa.Column('type', sa.Enum('SYSTEM', 'TRANSACTION', 'CONTRACT', 'AUTH', 'USER', 'WALLET', 'PAYMENT', 'SUBSCRIPTION', name='notificationtype'), nullable=False),
    sa.Column('priority', sa.Enum('LOW', 'NORMAL', 'HIGH', 'URGENT', name='notificationpriority'), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'DELIVERED', 'READ', 'FAILED', 'DELETED', name='notificationstatus'), nullable=False),
    sa.Column('is_read', sa.Boolean(), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('noti_metadata', sa.JSON(), nullable=True),
    sa.Column('action_url', sa.String(length=512), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('read_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('delivered_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notifications_id'), 'notifications', ['id'], unique=False)
    op.create_index(op.f('ix_notifications_is_deleted'), 'notifications', ['is_deleted'], unique=False)
    op.create_index(op.f('ix_notifications_is_read'), 'notifications', ['is_read'], unique=False)
    op.create_index(op.f('ix_notifications_user_id'), 'notifications', ['user_id'], unique=False)
    op.create_table('payments',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.BIGINT(), nullable=False),
    sa.Column('subscription_plan_id', sa.BIGINT(), nullable=False),
    sa.Column('amount', sa.BIGINT(), nullable=False),
    sa.Column('provider', sa.Enum('PAYSTACK', 'STRIPE', 'MONNIFY', name='paymentprovider'), nullable=False),
    sa.Column('provider_reference', sa.String(length=255), nullable=True),
    sa.Column('status', sa.Enum('PENDING', 'SUCCESS', 'FAILED', name='paymentstatus'), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('duration_days', sa.BIGINT(), nullable=True),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['subscription_plan.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_payments_id'), 'payments', ['id'], unique=False)
    op.create_index(op.f('ix_payments_subscription_plan_id'), 'payments', ['subscription_plan_id'], unique=False)
    op.create_index(op.f('ix_payments_user_id'), 'payments', ['user_id'], unique=False)
    op.create_table('real_world_assets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.String(length=1000), nullable=True),
    sa.Column('asset_type', sa.Enum('REAL_ESTATE', 'ARTWORK', 'COMMODITY', 'INTELLECTUAL_PROPERTY', 'VEHICLE', 'OTHER', name='rwatype'), nullable=False),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('initial_value', sa.Float(), nullable=False),
    sa.Column('current_value', sa.Float(), nullable=True),
    sa.Column('blockchain_contract_address', sa.String(length=255), nullable=True),
    sa.Column('token_standard', sa.Enum('ERC_721', 'ERC_1155', 'ERC_20', name='tokenstandard'), nullable=True),
    sa.Column('status', sa.Enum('DRAFT', 'PENDING_VERIFICATION', 'VERIFIED', 'TOKENIZED', 'LISTED', 'SOLD', name='rwastatus'), nullable=True),
    sa.Column('verification_documents', sa.String(length=500), nullable=True),
    sa.Column('total_tokens', sa.Integer(), nullable=True),
    sa.Column('tokens_available', sa.Integer(), nullable=True),
    sa.Column('token_price', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_real_world_assets_id'), 'real_world_assets', ['id'], unique=False)
    op.create_table('user_subscription',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.BIGINT(), nullable=False),
    sa.Column('subscription_plan_id', sa.BIGINT(), nullable=False),
    sa.Column('start_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('end_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('status', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['subscription_plan_id'], ['subscription_plan.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_subscription_id'), 'user_subscription', ['id'], unique=False)
    op.create_index(op.f('ix_user_subscription_subscription_plan_id'), 'user_subscription', ['subscription_plan_id'], unique=False)
    op.create_index(op.f('ix_user_subscription_user_id'), 'user_subscription', ['user_id'], unique=False)
    op.create_table('users_avatar',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('avatar_url', sa.String(length=500), nullable=True),
    sa.Column('date_created', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_updated', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_avatar_id'), 'users_avatar', ['id'], unique=True)
    op.create_index(op.f('ix_users_avatar_user_id'), 'users_avatar', ['user_id'], unique=False)
    op.create_table('wallet_addresses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.BIGINT(), nullable=False),
    sa.Column('address', sa.String(), nullable=True),
    sa.Column('chain_id', sa.Integer(), nullable=True),
    sa.Column('is_verified', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_verified', sa.DateTime(timezone=True), nullable=True),
    sa.Column('wc_session_id', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_wallet_addresses_address'), 'wallet_addresses', ['address'], unique=True)
    op.create_index(op.f('ix_wallet_addresses_id'), 'wallet_addresses', ['id'], unique=False)
    op.create_index(op.f('ix_wallet_addresses_user_id'), 'wallet_addresses', ['user_id'], unique=False)
    op.create_table('account_balance_history',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('account_id', sa.Integer(), nullable=False),
    sa.Column('balance', sa.Numeric(precision=36, scale=18), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['account_id'], ['account.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_account_balance_history_user_id'), 'account_balance_history', ['user_id'], unique=False)
    op.create_table('asset_verifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('asset_id', sa.Integer(), nullable=False),
    sa.Column('verifier_id', sa.Integer(), nullable=False),
    sa.Column('verification_type', sa.String(length=100), nullable=False),
    sa.Column('verification_document_url', sa.String(length=500), nullable=False),
    sa.Column('verification_status', sa.String(length=50), nullable=True),
    sa.Column('verified_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['asset_id'], ['real_world_assets.id'], ),
    sa.ForeignKeyConstraint(['verifier_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_asset_verifications_id'), 'asset_verifications', ['id'], unique=False)
    op.create_table('nfts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_by', sa.Integer(), nullable=True),
    sa.Column('contract_id', sa.Integer(), nullable=True),
    sa.Column('metadata_id', sa.Integer(), nullable=True),
    sa.Column('token_id', sa.Integer(), nullable=False),
    sa.Column('metadata_uri', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['contract_id'], ['contract_deployments.id'], ),
    sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
    sa.ForeignKeyConstraint(['metadata_id'], ['nft_metadata.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_nfts_contract_id'), 'nfts', ['contract_id'], unique=False)
    op.create_index(op.f('ix_nfts_created_by'), 'nfts', ['created_by'], unique=False)
    op.create_index(op.f('ix_nfts_id'), 'nfts', ['id'], unique=False)
    op.create_index(op.f('ix_nfts_metadata_id'), 'nfts', ['metadata_id'], unique=False)
    op.create_table('wallet_score_history',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('wallet_address', sa.Integer(), nullable=False),
    sa.Column('total_score', sa.Integer(), nullable=False),
    sa.Column('historical_score', sa.Integer(), nullable=False),
    sa.Column('activity_score', sa.Integer(), nullable=False),
    sa.Column('financial_score', sa.Integer(), nullable=False),
    sa.Column('network_score', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['wallet_address'], ['account.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_wallet_score_history_user_id'), 'wallet_score_history', ['user_id'], unique=False)
    op.create_table('wallet_scores',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('wallet_address', sa.BIGINT(), nullable=False),
    sa.Column('user_id', sa.BIGINT(), nullable=False),
    sa.Column('total_score', sa.Integer(), nullable=True),
    sa.Column('historical_score', sa.Integer(), nullable=True),
    sa.Column('activity_score', sa.Integer(), nullable=True),
    sa.Column('financial_score', sa.Integer(), nullable=True),
    sa.Column('network_score', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.ForeignKeyConstraint(['wallet_address'], ['account.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_wallet_scores_id'), 'wallet_scores', ['id'], unique=False)
    op.create_index(op.f('ix_wallet_scores_user_id'), 'wallet_scores', ['user_id'], unique=False)
    op.create_index(op.f('ix_wallet_scores_wallet_address'), 'wallet_scores', ['wallet_address'], unique=False)
    op.create_table('wallet_transactions',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('address_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('contract_status', sa.Enum('DEPLOYED', 'FAILED', name='contractstatus'), nullable=False),
    sa.Column('value', sa.BIGINT(), nullable=True),
    sa.Column('gas_used', sa.BIGINT(), nullable=True),
    sa.Column('contract_type', sa.Enum('ER20', 'ERC721', 'NONE', name='transactiontype'), nullable=True),
    sa.ForeignKeyConstraint(['address_id'], ['account.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_wallet_transactions_address_id'), 'wallet_transactions', ['address_id'], unique=False)
    op.create_index(op.f('ix_wallet_transactions_user_id'), 'wallet_transactions', ['user_id'], unique=False)
    op.create_table('tobesigned',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('nft_id', sa.Integer(), nullable=True),
    sa.Column('operation', sa.JSON(), nullable=True),
    sa.Column('transaction_data', sa.JSON(), nullable=False),
    sa.Column('total_cost_wei', sa.Numeric(precision=32), nullable=False),
    sa.Column('total_cost_eth', sa.Numeric(precision=32, scale=18), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('symbol', sa.String(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image', sa.String(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['nft_id'], ['nfts.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tobesigned_nft_id'), 'tobesigned', ['nft_id'], unique=False)
    op.create_index(op.f('ix_tobesigned_user_id'), 'tobesigned', ['user_id'], unique=False)
    op.create_table('tobesigned_contract',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('nft_id', sa.Integer(), nullable=True),
    sa.Column('transaction_data', sa.JSON(), nullable=False),
    sa.Column('total_cost_wei', sa.Numeric(precision=32), nullable=False),
    sa.Column('total_cost_eth', sa.Numeric(precision=32, scale=18), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['nft_id'], ['nfts.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tobesigned_contract_nft_id'), 'tobesigned_contract', ['nft_id'], unique=False)
    op.create_index(op.f('ix_tobesigned_contract_user_id'), 'tobesigned_contract', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_tobesigned_contract_user_id'), table_name='tobesigned_contract')
    op.drop_index(op.f('ix_tobesigned_contract_nft_id'), table_name='tobesigned_contract')
    op.drop_table('tobesigned_contract')
    op.drop_index(op.f('ix_tobesigned_user_id'), table_name='tobesigned')
    op.drop_index(op.f('ix_tobesigned_nft_id'), table_name='tobesigned')
    op.drop_table('tobesigned')
    op.drop_index(op.f('ix_wallet_transactions_user_id'), table_name='wallet_transactions')
    op.drop_index(op.f('ix_wallet_transactions_address_id'), table_name='wallet_transactions')
    op.drop_table('wallet_transactions')
    op.drop_index(op.f('ix_wallet_scores_wallet_address'), table_name='wallet_scores')
    op.drop_index(op.f('ix_wallet_scores_user_id'), table_name='wallet_scores')
    op.drop_index(op.f('ix_wallet_scores_id'), table_name='wallet_scores')
    op.drop_table('wallet_scores')
    op.drop_index(op.f('ix_wallet_score_history_user_id'), table_name='wallet_score_history')
    op.drop_table('wallet_score_history')
    op.drop_index(op.f('ix_nfts_metadata_id'), table_name='nfts')
    op.drop_index(op.f('ix_nfts_id'), table_name='nfts')
    op.drop_index(op.f('ix_nfts_created_by'), table_name='nfts')
    op.drop_index(op.f('ix_nfts_contract_id'), table_name='nfts')
    op.drop_table('nfts')
    op.drop_index(op.f('ix_asset_verifications_id'), table_name='asset_verifications')
    op.drop_table('asset_verifications')
    op.drop_index(op.f('ix_account_balance_history_user_id'), table_name='account_balance_history')
    op.drop_table('account_balance_history')
    op.drop_index(op.f('ix_wallet_addresses_user_id'), table_name='wallet_addresses')
    op.drop_index(op.f('ix_wallet_addresses_id'), table_name='wallet_addresses')
    op.drop_index(op.f('ix_wallet_addresses_address'), table_name='wallet_addresses')
    op.drop_table('wallet_addresses')
    op.drop_index(op.f('ix_users_avatar_user_id'), table_name='users_avatar')
    op.drop_index(op.f('ix_users_avatar_id'), table_name='users_avatar')
    op.drop_table('users_avatar')
    op.drop_index(op.f('ix_user_subscription_user_id'), table_name='user_subscription')
    op.drop_index(op.f('ix_user_subscription_subscription_plan_id'), table_name='user_subscription')
    op.drop_index(op.f('ix_user_subscription_id'), table_name='user_subscription')
    op.drop_table('user_subscription')
    op.drop_index(op.f('ix_real_world_assets_id'), table_name='real_world_assets')
    op.drop_table('real_world_assets')
    op.drop_index(op.f('ix_payments_user_id'), table_name='payments')
    op.drop_index(op.f('ix_payments_subscription_plan_id'), table_name='payments')
    op.drop_index(op.f('ix_payments_id'), table_name='payments')
    op.drop_table('payments')
    op.drop_index(op.f('ix_notifications_user_id'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_is_read'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_is_deleted'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_id'), table_name='notifications')
    op.drop_table('notifications')
    op.drop_index(op.f('ix_notificaiton_prefrence_user_id'), table_name='notificaiton_prefrence')
    op.drop_index(op.f('ix_notificaiton_prefrence_id'), table_name='notificaiton_prefrence')
    op.drop_table('notificaiton_prefrence')
    op.drop_index(op.f('ix_contract_deployments_user_id'), table_name='contract_deployments')
    op.drop_table('contract_deployments')
    op.drop_index(op.f('ix_blacklist_tokens_token'), table_name='blacklist_tokens')
    op.drop_index(op.f('ix_blacklist_tokens_id'), table_name='blacklist_tokens')
    op.drop_index(op.f('ix_blacklist_tokens_created_by'), table_name='blacklist_tokens')
    op.drop_table('blacklist_tokens')
    op.drop_index(op.f('ix_api_keys_user_id'), table_name='api_keys')
    op.drop_index(op.f('ix_api_keys_id'), table_name='api_keys')
    op.drop_index(op.f('ix_api_keys_api_key'), table_name='api_keys')
    op.drop_table('api_keys')
    op.drop_index(op.f('ix_account_user_id'), table_name='account')
    op.drop_index(op.f('ix_account_id'), table_name='account')
    op.drop_index(op.f('ix_account_address'), table_name='account')
    op.drop_table('account')
    op.drop_index(op.f('ix_user_id'), table_name='user')
    op.drop_index(op.f('ix_user_email'), table_name='user')
    op.drop_table('user')
    op.drop_index(op.f('ix_transactions_tx_hash'), table_name='transactions')
    op.drop_index(op.f('ix_transactions_id'), table_name='transactions')
    op.drop_table('transactions')
    op.drop_index(op.f('ix_tokens_id'), table_name='tokens')
    op.drop_table('tokens')
    op.drop_index(op.f('ix_token_balances_id'), table_name='token_balances')
    op.drop_table('token_balances')
    op.drop_index(op.f('ix_subscription_plan_name'), table_name='subscription_plan')
    op.drop_index(op.f('ix_subscription_plan_id'), table_name='subscription_plan')
    op.drop_table('subscription_plan')
    op.drop_index(op.f('ix_nft_metadata_id'), table_name='nft_metadata')
    op.drop_table('nft_metadata')
    # ### end Alembic commands ###
