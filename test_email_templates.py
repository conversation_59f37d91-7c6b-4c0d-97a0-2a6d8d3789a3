#!/usr/bin/env python
"""
Test Email Templates

This script tests the email templates by rendering them with sample data
and saving the output to HTML files for visual inspection.
"""

import os
from datetime import datetime
from api.v1.auth.services_email import (
    EmailVerification, 
    VERIFICATION_EMAIL_TEMPLATE,
    PASSWORD_RESET_EMAIL_TEMPLATE,
    COMPANY_NAME,
    COMPANY_LOGO_URL,
    COMPANY_ADDRESS,
    SUPPORT_EMAIL,
    VERIFICATION_TOKEN_EXPIRY_HOURS,
    PASSWORD_RESET_TOKEN_EXPIRY_HOURS
)

def test_verification_email():
    """Test the verification email template"""
    email_service = EmailVerification()
    
    # Load the template
    template = email_service.load_template(VERIFICATION_EMAIL_TEMPLATE)
    
    # Sample data
    verification_code = "123456"
    current_year = datetime.now().year
    
    # Replace template variables
    html_content = template.replace("{{ verification_code }}", verification_code)\
                          .replace("{{ expiry_hours }}", str(VERIFICATION_TOKEN_EXPIRY_HOURS))\
                          .replace("{{ company_name }}", COMPANY_NAME)\
                          .replace("{{ company_logo }}", COMPANY_LOGO_URL)\
                          .replace("{{ company_address }}", COMPANY_ADDRESS)\
                          .replace("{{ support_email }}", SUPPORT_EMAIL)\
                          .replace("{{ current_year }}", str(current_year))
    
    # Save to file for inspection
    with open("verification_email_test.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print(f"Verification email template saved to verification_email_test.html")

def test_password_reset_email():
    """Test the password reset email template"""
    email_service = EmailVerification()
    
    # Load the template
    template = email_service.load_template(PASSWORD_RESET_EMAIL_TEMPLATE)
    
    # Sample data
    reset_code = "987654"
    current_year = datetime.now().year
    
    # Replace template variables
    html_content = template.replace("{{ reset_code }}", reset_code)\
                          .replace("{{ expiry_hours }}", str(PASSWORD_RESET_TOKEN_EXPIRY_HOURS))\
                          .replace("{{ company_name }}", COMPANY_NAME)\
                          .replace("{{ company_logo }}", COMPANY_LOGO_URL)\
                          .replace("{{ company_address }}", COMPANY_ADDRESS)\
                          .replace("{{ support_email }}", SUPPORT_EMAIL)\
                          .replace("{{ current_year }}", str(current_year))
    
    # Save to file for inspection
    with open("password_reset_email_test.html", "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print(f"Password reset email template saved to password_reset_email_test.html")

if __name__ == "__main__":
    test_verification_email()
    test_password_reset_email()
    print("Done! Open the HTML files in your browser to see how the emails look.")
