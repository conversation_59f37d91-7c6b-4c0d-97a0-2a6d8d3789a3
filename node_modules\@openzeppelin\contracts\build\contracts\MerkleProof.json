{"_format": "hh-sol-artifact-1", "contractName": "MerkleProof", "sourceName": "contracts/utils/cryptography/MerkleProof.sol", "abi": [{"inputs": [], "name": "MerkleProofInvalidMultiproof", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f80fdfea26469706673582212202a578e0ee33df5da5254baedcc290f35f789cc06faa798a3b7b1c1c76cd4c27c64736f6c63430008180033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f80fdfea26469706673582212202a578e0ee33df5da5254baedcc290f35f789cc06faa798a3b7b1c1c76cd4c27c64736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}