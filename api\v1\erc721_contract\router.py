from sqlalchemy.ext.asyncio import AsyncSession
from api.v1.user import schemas as user_schema
from fastapi import Depends, Form, HTTPException, UploadFile, File, APIRouter, Depends, Query
from api.core.dependencies import is_authenticated
from .schemas import TransferRequest, ApprovalRequest, OwnershipRequest, ApprovalForAllRequest, SafeTransferWithDataRequest
from .services import ERC721Interaction
from api.db.database import get_db
from .exceptions import handle_contract_notification
from .schemas import Metadata
from typing import Optional
from api.v1.account.models import Account as AccountWallet
from config import config
from api.v1.contracts.schemas import Order
from .exceptions import handle_erc721_error, logger

from web3 import AsyncWeb3
from api.core.blockchain_dep import get_web3
from api.core.general_dep import get_redis
from redis.asyncio import Redis
import asyncio
from api.v1.deploy_contracts.services import ToBeSigned, ContractDeployment, TransactionType


app = APIRouter(tags=["Erc721 Contract"])

PRODUCTION = config.PRODUCTION




@app.post("/nft/log_metadata")
async def log_metadata(contract_id: int, metadata: Metadata, db: AsyncSession = Depends(get_db),
                      user: user_schema.User = Depends(is_authenticated),
                      web3: AsyncWeb3 = Depends(get_web3)):
    try:
        contract_instance = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ERC721, web3=web3)
        tx_hash = await contract_instance.nft_metadata(metadata)

        return {"status": "success", "uri": tx_hash}
    except Exception as e:
        handle_erc721_error(e)




@app.post("/nft/mint")
async def mint_nft(contract_id: int, metadata_id: Optional[int] = None,
                   file: UploadFile = File(...), default_wallet:Optional[bool]=True,
                   db: AsyncSession = Depends(get_db), user: user_schema.User = Depends(is_authenticated),
                   web3: AsyncWeb3 = Depends(get_web3)):

    try:

        contract_instance = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ERC721, web3=web3)
        tx_hash = await contract_instance.mint_nft(metadata_id, file, default_wallet)

        return tx_hash
    except Exception as e:
        handle_erc721_error(e)



@app.get("/nft/owned-by-user")
async def owned_by_you(contract_id: int,
                     db: AsyncSession = Depends(get_db),
                     user: user_schema.User = Depends(is_authenticated),
                     web3: AsyncWeb3 = Depends(get_web3),
                     redis: Optional[Redis] = Depends(get_redis)):
    """
    likely returns the latest token ID or the total number of tokens minted.
    """
    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        owned_by_you = await contract.owned_by_you()
        return {"status": "success", "balance": owned_by_you}
    except Exception as e:
        handle_erc721_error(e)



@app.get("/nft/balance")
async def get_balance(contract_id: int,
                    account_address: str,
                    db: AsyncSession = Depends(get_db),
                    user: user_schema.User = Depends(is_authenticated),
                    web3: AsyncWeb3 = Depends(get_web3),
                    redis: Optional[Redis] = Depends(get_redis)):

    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        balance = await contract.get_balance_of(account_address)
        return {"status": "success", "balance": balance}
    except Exception as e:
        handle_erc721_error(e)



@app.get("/nft/token-id")
async def get_token_id(contract_id: int,
                     db: AsyncSession = Depends(get_db),
                     user: user_schema.User = Depends(is_authenticated),
                     web3: AsyncWeb3 = Depends(get_web3),
                     redis: Optional[Redis] = Depends(get_redis)):
    """
    likely returns the latest token ID or the total number of tokens minted.
    """

    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        token_id = await contract.get_token_id()
        return {"status": "success", "token_id": token_id}
    except Exception as e:
        handle_erc721_error(e)



@app.get("/nft/nfts")
async def get_all_nfts(contract_id: int, page:Optional[int]=Query(1, ge=1),
                       page_size:Optional[int]=Query(10, ge=1), order:Optional[Order]="desc",
                     db: AsyncSession = Depends(get_db),
                     user: user_schema.User = Depends(is_authenticated),
                     web3: AsyncWeb3 = Depends(get_web3),
                     redis: Optional[Redis] = Depends(get_redis)):
    """
    Get all NFTs for a contract with pagination.
    """
    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        token_id = await contract.get_all_nfts(page, page_size, order)
        return {"status": "success", "body":token_id}
    except Exception as e:
        handle_erc721_error(e)


@app.get("/nft/nft/{token_id}")
async def get_one_nft(contract_id: int, token_id: int,
                     db: AsyncSession = Depends(get_db),
                     user: user_schema.User = Depends(is_authenticated),
                     web3: AsyncWeb3 = Depends(get_web3),
                     redis: Optional[Redis] = Depends(get_redis)):
    """
    Get details for a specific NFT by token ID.
    """
    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        nft = await contract.get_one_nft(token_id)
        return {"status": "success", "token_id": nft}
    except Exception as e:
        handle_erc721_error(e)



@app.get("/nft/approved")
async def get_approved(contract_id: int,
                      token_id: int,
                      db: AsyncSession = Depends(get_db),
                     user: user_schema.User = Depends(is_authenticated),
                     web3: AsyncWeb3 = Depends(get_web3),
                     redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        approved_address = await contract.get_approved(token_id)
        return {"status": "success", "approved_address": approved_address}
    except Exception as e:
        handle_erc721_error(e)


@app.get("/nft/is-approved-for-all")
async def check_approval_for_all(operator: str, contract_id: int, db: AsyncSession = Depends(get_db),
                                user: user_schema.User = Depends(is_authenticated),
                                web3: AsyncWeb3 = Depends(get_web3),
                                redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        is_approved = await contract.is_approved_for_all(operator)
        return {"status": "success", "is_approved": is_approved}
    except Exception as e:
        handle_erc721_error(e)



@app.get("/nft/contract-name")
async def get_contract_name(contract_id: int, db: AsyncSession = Depends(get_db),
                        user: user_schema.User = Depends(is_authenticated),
                        web3: AsyncWeb3 = Depends(get_web3),
                        redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        name = await contract.get_contract_name()
        return {"status": "success", "name": name}
    except Exception as e:
        handle_erc721_error(e)


@app.get("/nft/contract-owner")
async def get_contract_owner(contract_id: int, db: AsyncSession = Depends(get_db),
                            user: user_schema.User = Depends(is_authenticated),
                            web3: AsyncWeb3 = Depends(get_web3),
                            redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        owner = await contract.get_contract_owner()
        return {"status": "success", "owner": owner}
    except Exception as e:
        handle_erc721_error(e)


@app.get("/nft/owner-of/{token_id}")
async def get_token_owner(contract_id: int,token_id: int, db: AsyncSession = Depends(get_db),
                          user: user_schema.User = Depends(is_authenticated),
                            web3: AsyncWeb3 = Depends(get_web3),
                            redis: Optional[Redis] = Depends(get_redis)):
    try:

        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        owner = await contract.get_owner_of_token(token_id)
        return {"status": "success", "owner": owner}
    except Exception as e:
        handle_erc721_error(e)


@app.get("/nft/token-uri/{token_id}")
async def get_token_uri(contract_id: int, token_id: int, db: AsyncSession = Depends(get_db),
                        user: user_schema.User = Depends(is_authenticated),
                        web3: AsyncWeb3 = Depends(get_web3),
                        redis: Optional[Redis] = Depends(get_redis)):
    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        uri = await contract.get_token_uri(token_id)
        return {"status": "success", "uri": uri}
    except Exception as e:
        handle_erc721_error(e)


@app.get("/nft/supports-interface")
async def supports_interface(contract_id: int, interface_id: str,
                            db: AsyncSession = Depends(get_db),
                            user: user_schema.User = Depends(is_authenticated),
                            web3: AsyncWeb3 = Depends(get_web3),
                            redis: Optional[Redis] = Depends(get_redis)):

    """sumary_line

    # Common ERC721 interface IDs
    ERC721_INTERFACE_ID = '0x80ac58cd'
    ERC721_METADATA_INTERFACE_ID = '0x5b5e139f'
    ERC721_ENUMERABLE_INTERFACE_ID = '0x780e9d63'
    """

    try:

        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        supports = await contract.supports_interface(interface_id)
        return {"status": "success", "supports_interface": supports}
    except Exception as e:
        handle_erc721_error(e)


@app.get("/nft/total-supply")
async def get_total_supply(contract_id: int, db: AsyncSession = Depends(get_db),
                            user: user_schema.User = Depends(is_authenticated),
                            web3: AsyncWeb3 = Depends(get_web3),
                            redis: Optional[Redis] = Depends(get_redis)):
    try:

        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, 
                                            contract_type=TransactionType.ERC721, web3=web3, redis=redis)
        total_supply = await contract.get_total_supply()
        return {"status": "success", "total_supply": total_supply}
    except Exception as e:
        handle_erc721_error(e)



# Write function routes
@app.post("/nft/approve")
async def approve_nft(contract_id: int,
                      approval_request: ApprovalRequest,
                      default_wallet:Optional[bool]=True,
                      db: AsyncSession = Depends(get_db),
                      user: user_schema.User = Depends(is_authenticated),
                      web3: AsyncWeb3 = Depends(get_web3)):

    try:

        contract_instance = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ERC721, web3=web3)
        tx_hash = await contract_instance.approve_nft(
            approval_request.to_address,
            approval_request.token_id,
            default_wallet
        )

        return tx_hash
    except Exception as e:
        handle_erc721_error(e)


@app.post("/nft/renounce-ownership")
async def renounce_ownership(contract_id: int,
                            #ownership_request: OwnershipRenouce,
                            default_wallet:Optional[bool]=True,
                            db: AsyncSession = Depends(get_db),
                            user: user_schema.User = Depends(is_authenticated),
                            web3: AsyncWeb3 = Depends(get_web3)):
    try:

        contract_instance = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ERC721, web3=web3)
        tx_hash = await contract_instance.renounce_ownership(default_wallet)

        return tx_hash

    except Exception as e:
        handle_erc721_error(e)



@app.post("/nft/safe-transfer")
async def safe_transfer(contract_id: int,
                        transfer_request: TransferRequest,
                        default_wallet:Optional[bool]=True,
                        db: AsyncSession = Depends(get_db),
                        user: user_schema.User = Depends(is_authenticated),
                        web3: AsyncWeb3 = Depends(get_web3)):
    try:

        contract_instance = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ERC721, web3=web3)
        tx_hash = await contract_instance.safe_transfer_from(
            transfer_request.to_address,
            transfer_request.token_id,
            default_wallet)

        return tx_hash

    except Exception as e:
        handle_erc721_error(e)



@app.post("/nft/safe-transfer-with-data")
async def safe_transfer_with_data(contract_id: int,
                                  transfer_request: SafeTransferWithDataRequest,
                                  default_wallet:Optional[bool]=True,
                                  db: AsyncSession = Depends(get_db),
                                  user: user_schema.User = Depends(is_authenticated),
                                  web3: AsyncWeb3 = Depends(get_web3)):
    try:

        contract_instance = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ERC721, web3=web3)
        tx_hash = await contract_instance.safe_transfer_from_with_data(
            transfer_request.to_address,
            transfer_request.token_id,
            transfer_request.data,
            default_wallet
        )

        return tx_hash
    except Exception as e:
        handle_erc721_error(e)


@app.post("/nft/set-approval-for-all")
async def set_approval_for_all(contract_id: int,
                                approval_request: ApprovalForAllRequest,
                                default_wallet:Optional[bool]=True,
                                db: AsyncSession = Depends(get_db),
                                user: user_schema.User = Depends(is_authenticated),
                                web3: AsyncWeb3 = Depends(get_web3)):

    try:
        contract_instance = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ERC721, web3=web3)
        tx_hash = await contract_instance.set_approval_for_all(
            approval_request.operator,
            approval_request.approved,
            default_wallet
        )

        return tx_hash
    except Exception as e:
        handle_erc721_error(e)



@app.post("/nft/transfer")
async def transfer(contract_id: int,
                  transfer_request: TransferRequest,
                  default_wallet:Optional[bool]=True,
                  db: AsyncSession = Depends(get_db),
                  user: user_schema.User = Depends(is_authenticated),
                  web3: AsyncWeb3 = Depends(get_web3)):
    try:
        contract = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ERC721, web3=web3)

        tx_hash = await contract.transfer_from(
            transfer_request.to_address,
            transfer_request.token_id,
            default_wallet
        )
        return tx_hash

    except Exception as e:
        handle_erc721_error(e)


@app.post("/nft/transfer-ownership")
async def transfer_ownership(contract_id: int,
                            ownership_request: OwnershipRequest,
                            default_wallet:Optional[bool]=True,
                            db: AsyncSession = Depends(get_db),
                            user: user_schema.User = Depends(is_authenticated),
                            web3: AsyncWeb3 = Depends(get_web3)):

    try:

        if not ownership_request.new_owner_address:
            raise HTTPException(status_code=400, detail="new_owner_address is required")

        contract_instance = await ERC721Interaction.create(db=db, user_id=user.id, contract_id=contract_id, contract_type=TransactionType.ERC721, web3=web3)
        tx_hash = await contract_instance.transfer_ownership(
            ownership_request.new_owner_address,
            default_wallet
        )
        """
        if PRODUCTION == True:
            new_owner = db.query(AccountWallet).filter(AccountWallet.address == ownership_request.new_owner_address).first()
            new_owner_id = new_owner.user_id if new_owner else None
            if new_owner_id is None:
                logger.warning("New owner not found in the system")
        else:
            #new_owner_id = user.id
            pass

        # Save notification
        action = {
            "type": "transfer_ownership_from",
            "new_owner_address": ownership_request.new_owner_address
        }
        asyncio.create_task(handle_contract_notification(
            user_id=user.id,
            contract_type="erc721",
            action=action
        ))
        if PRODUCTION and new_owner_id:
            action1 = {
                "type": "transfer_ownership_to",
                "current_owner_address": user.id,
                #"new_owner_address": ownership_request.new_owner_address
            }
            asyncio.create_task(handle_contract_notification(
                user_id=new_owner,
                contract_type="erc721",
                action=action1
            ))"
        """

        return tx_hash

    except Exception as e:
        handle_erc721_error(e)