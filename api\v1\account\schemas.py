from pydantic import BaseModel
from datetime import datetime
from typing import List


class Account(BaseModel):
    id: int
    address: str

    class Config:
        from_attributes = True

class AccountCreate(Account):
    id: int
    created_at: datetime
    #private_key: str

    class Config:
        from_attributes = True

class AccountResponse(Account):
    is_active: bool
    user_id: int
    balance: float

    class Config:
        from_attributes = True


class AccountBalance(Account):
    balance: float

    class Config:
        from_attributes = True




from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List

class WalletStats(BaseModel):
    wallet_age_days: int
    transaction_count: int
    daily_transactions_30d: float
    transaction_success_rate: float
    defi_interaction_count: int
    peak_balance: float
    current_balance: float
    token_count: int
    has_nfts: bool
    avg_transaction_value: float
    network_avg_transaction_value: float
    gas_price_ratio: float
    unique_counterparties: int
    verified_contract_interactions: int
    defi_lending_count: int

class CreditScoreResponse(BaseModel):
    wallet_address: str
    total_score: int
    historical_score: int
    activity_score: int
    financial_score: int
    network_score: int
    risk_level: str
    score_breakdown: dict
    created_at: datetime

from pydantic import BaseModel, Field



class WalletScoreSchema(BaseModel):
    total_score: int = 0
    wallet_age_years: int = 0
    wallet_age_months: int = 0
    transaction_count: int = 0
    historical_score: int = 0
    activity_score: int = 0
    financial_score: int = 0
    network_score: int = 0
    weekly_rate_change: float
    created_at: datetime = Field(default_factory=datetime.now)
    class Config:
        from_attributes = True




class AccountBalance(BaseModel):
    id: int
    address: str
    balance: float
    weekly_rate_change: float
    previous_week_balance: float
    current_week_balance: float

    class Config:
        from_attributes = True