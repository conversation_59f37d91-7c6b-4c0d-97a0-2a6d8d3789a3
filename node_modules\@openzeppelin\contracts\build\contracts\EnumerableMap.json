{"_format": "hh-sol-artifact-1", "contractName": "EnumerableMap", "sourceName": "contracts/utils/structs/EnumerableMap.sol", "abi": [{"inputs": [{"internalType": "bytes32", "name": "key", "type": "bytes32"}], "name": "EnumerableMapNonexistentKey", "type": "error"}], "bytecode": "0x60556032600b8282823980515f1a607314602657634e487b7160e01b5f525f60045260245ffd5b305f52607381538281f3fe730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220d94719610d117cb21a552f9e058662286280b1eb20d7fa9aa7263c88029ea54a64736f6c63430008180033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040525f80fdfea2646970667358221220d94719610d117cb21a552f9e058662286280b1eb20d7fa9aa7263c88029ea54a64736f6c63430008180033", "linkReferences": {}, "deployedLinkReferences": {}}