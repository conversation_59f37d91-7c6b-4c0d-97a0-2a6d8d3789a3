from api.core.logging_config import get_logger
from fastapi import HTTPException, status, Request
from api.v1.websockets.models import NotificationType, NotificationPriority
from api.v1.websockets.services import NotificationService
from api.v1.websockets.schemas import NotificationCreate
from datetime import datetime, timezone
from typing import Optional
import user_agents  # pip install user-agents

# Configure logging
logger = get_logger(__name__)

# Base exception classes
class DeploymentError(Exception):
    """Base exception for contract deployment-related errors"""
    pass

class CompilationError(DeploymentError):
    """Exception raised for contract compilation errors"""
    pass

class ValidationError(DeploymentError):
    """Exception raised for contract validation errors"""
    pass

class TransactionError(DeploymentError):
    """Exception raised for transaction-related errors"""
    pass

class SignatureError(DeploymentError):
    """Exception raised for signature-related errors"""
    pass

class GasEstimationError(DeploymentError):
    """Exception raised for gas estimation errors"""
    pass

class BlockchainConnectionError(DeploymentError):
    """Exception raised for blockchain connection errors"""
    pass

class ContractVerificationError(DeploymentError):
    """Exception raised for contract verification errors"""
    pass

class FileUploadError(DeploymentError):
    """Exception raised for file upload errors"""
    pass

class ContractNotFoundError(DeploymentError):
    """Exception raised when contract is not found"""
    pass

class DatabaseError(DeploymentError):
    """Exception raised for database-related errors"""
    pass

class DataProcessingError(DeploymentError):
    """Exception raised for data processing errors"""
    pass

class CacheError(DeploymentError):
    """Exception raised for caching-related errors"""
    pass

class BuildError(TransactionError):
    """Exception raised when build fails"""
    pass

class InsufficientFundsError(TransactionError):
    """Exception raised when wallet has insufficient funds"""
    pass

class TransactionTimeoutError(TransactionError):
    """Exception raised when transaction times out"""
    pass

class TransactionRejectedError(TransactionError):
    """Exception raised when transaction is rejected"""
    pass

class InvalidContractTypeError(ValidationError):
    """Exception raised for invalid contract types"""
    pass

class InvalidParameterError(ValidationError):
    """Exception raised for invalid parameters"""
    pass

# Error handler function
def handle_deployment_error(error: Exception) -> None:
    """
    Convert deployment errors to appropriate HTTP exceptions
    """
    logger.error(f"Deployment error: {str(error)}")

    if isinstance(error, HTTPException):
        # If it's already an HTTPException, re-raise it
        raise error

    elif isinstance(error, CompilationError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Contract compilation failed: {str(error)}"
        )

    elif isinstance(error, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Contract validation failed: {str(error)}"
        )

    elif isinstance(error, InvalidContractTypeError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid contract type: {str(error)}"
        )

    elif isinstance(error, InvalidParameterError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid parameter: {str(error)}"
        )

    elif isinstance(error, SignatureError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Signature error: {str(error)}"
        )

    elif isinstance(error, GasEstimationError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Gas estimation failed: {str(error)}"
        )

    elif isinstance(error, InsufficientFundsError):
        raise HTTPException(
            status_code=status.HTTP_402_PAYMENT_REQUIRED,
            detail=f"Insufficient funds: {str(error)}"
        )

    elif isinstance(error, BuildError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Build error: {str(error)}"
        )

    elif isinstance(error, ContractNotFoundError):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Contract not found: {str(error)}"
        )

    elif isinstance(error, FileUploadError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File upload failed: {str(error)}"
        )

    elif isinstance(error, TransactionError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Transaction error: {str(error)}"
        )

    elif isinstance(error, TransactionTimeoutError):
        raise HTTPException(
            status_code=status.HTTP_408_REQUEST_TIMEOUT,
            detail=f"Transaction timed out: {str(error)}"
        )

    elif isinstance(error, TransactionRejectedError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Transaction rejected: {str(error)}"
        )

    elif isinstance(error, BlockchainConnectionError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Blockchain connection error: {str(error)}"
        )

    elif isinstance(error, ContractVerificationError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Contract verification failed: {str(error)}"
        )

    elif isinstance(error, DataProcessingError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Data processing error: {str(error)}"
        )

    elif isinstance(error, DatabaseError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Database error: {str(error)}"
        )

    elif isinstance(error, ValueError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(error)
        )

    elif isinstance(error, ConnectionError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Blockchain service connection failed"
        )

    elif isinstance(error, TimeoutError):
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="Blockchain request timed out"
        )
    elif isinstance(error, CacheError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service temporarily unavailable"
        )
    else:
        # Fallback for unexpected errors
        logger.error(f"Unexpected deployment error: {str(error)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during contract deployment"
        )

# Notification helper functions
def handle_deploy_notification(user_id: int, action: dict, request: Optional[Request] = None, wallet_address: str = None):
    """Handle contract deployment notifications"""
    messages = {
        "deploy": f"You deployed an {action.get('contract')} contract",
        "compile": f"You recompiled the {action.get('contract')} contract code",
        "sign": f"Transaction signed for {action.get('contract')} contract",
        "verify": f"{action.get('contract')} contract verified successfully",
        "error": f"Error in {action.get('contract')} contract: {action.get('message', '')}"
    }

    titles = {
        "deploy": f"Contract Deployment Successful",
        "compile": f"Contract Compilation Successful",
        "sign": f"Transaction Signing Successful",
        "verify": f"Contract Verification Successful",
        "error": f"Contract Operation Failed"
    }

    if action["type"] in messages:
        message = messages[action["type"]]
        title = titles.get(action["type"], "Contract Deployment Notification")

        # Get browser and device info if request is provided
        browser_info = {}
        if request:
            user_agent = user_agents.parse(request.headers.get("user-agent", ""))
            browser_info = {
                "browser": f"{user_agent.browser.family} {user_agent.browser.version_string}",
                "os": f"{user_agent.os.family} {user_agent.os.version_string}",
                "device": user_agent.device.family,
                "ip_address": request.client.host,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

        # Set priority based on action type
        priority = NotificationPriority.HIGH if action["type"] == "error" else NotificationPriority.NORMAL

        # Create notification with enhanced structure
        notification = NotificationCreate(
            title=title,
            message=message,
            type=NotificationType.CONTRACT,
            priority=priority,
            metadata={
                "device_info": browser_info if browser_info else None,
                "transaction_details": {
                    "action_type": action["type"],
                    "contract_type": action.get("contract", "Unknown") if isinstance(action.get("contract"), str) else str(action.get("contract", "Unknown")),
                    "wallet_address": wallet_address,
                    "status": "failed" if action["type"] == "error" else "success",
                    "detail": action.get("message", "")
                }
            },
            action_url=None
        )

        notification_service = NotificationService()
        notification_service.publish_notification(user_id=user_id, notification=notification)


class NotificationError(Exception):
    """Exception raised for errors in the notification process."""

    def __init__(self, message: str = "Notification operation failed"):
        self.message = message
        super().__init__(self.message)

    def __str__(self):
        return f"NotificationError: {self.message}"