from fastapi import Depends, APIRouter
from sqlalchemy.ext.asyncio import AsyncSession
from api.db.database import get_db
from api.v1.user import schemas as user_schema
from api.core.dependencies import is_authenticated
from api.v1.account.services import Vault
from api.v1.account.services_creditscore import CreditScore
import api.v1.account.schemas as schemas
from .exceptions import handle_wallet_notification
from api.core.general_dep import get_redis
from redis.asyncio import Redis
from typing import Optional
from web3 import AsyncWeb3
from api.core.blockchain_dep import get_web3
import asyncio

app = APIRouter(tags=["Account"])



@app.post("/account/vault/create", response_model=schemas.AccountCreate)
async def create_vault(
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db),
    web3: AsyncWeb3 = Depends(get_web3),
    redis: Optional[Redis] = Depends(get_redis)
):
    """
    API to create a vault
    """
    try:
        vault = Vault(web3=web3, redis=redis)
        result = await vault.create_wallet(db=db, user_id=user.id)
        handle_wallet_notification(user_id=user.id, action="create_vault",
                                            wallet_address=result.address)
        return result
    except Exception as e:
        handle_wallet_notification(user_id=user.id, action="create_vault_failed")
        raise

@app.get("/account/vault/get", response_model=schemas.AccountCreate)
async def get_vault(
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db),
    web3: AsyncWeb3 = Depends(get_web3),
    redis: Optional[Redis] = Depends(get_redis)

):
    """
    API endpoint to get user vault
    """ 
    try:
        vault = Vault(web3=web3, redis=redis)  
        result = await vault.get_vault(db=db, user_id=user.id)
        return result
    except Exception as e:
        raise

@app.get("/account/vault/get_balance", response_model=schemas.AccountBalance)
async def get_vault(
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db),
    web3: AsyncWeb3 = Depends(get_web3),
    redis: Optional[Redis] = Depends(get_redis)
):
    """
    API endpoint to get user vault balance
    """
    try:
        vault = Vault(web3=web3, redis=redis)
        result = await vault.check_balance(db=db, user_id=user.id)
        return result
    except Exception as e:
        raise



@app.get("/account/credit-score")
async def calculate_credit_score(
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db),
    redis: Optional[Redis] = Depends(get_redis)
):

    try:
        stats_service = await CreditScore.create(db=db, user_id=user.id, redis=redis)
        wallet_stats = await stats_service.calculate_and_store_wallet_score()

        return {
            "status": "sucess",
            "score": wallet_stats
        }
    except Exception as e:
        raise