
from enum import Enum
from typing import Optional
from datetime import datetime, <PERSON><PERSON><PERSON>
from pydantic import BaseModel
from .models import PaymentProvider
from ..subscription.models import SubscriptionTier


class PaymentProvider(str, Enum):
    PAYSTACK = "PAYSTACK"
    STRIPE = "STRIPE"
    MONNIFY = "MONNIFY"

class Duration(str, Enum):
    MONTHLY = "MONTHLY"
    YEARLY = "YEARLY"

# Pydantic schemas
class PaymentCreate(BaseModel):
    provider: PaymentProvider
    subscription_tier: SubscriptionTier

class PaymentResponse(BaseModel):
    payment_url: str
    reference: str
    amount: int
