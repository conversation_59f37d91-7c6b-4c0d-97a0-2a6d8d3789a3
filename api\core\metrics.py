from fastapi import FastAPI
from prometheus_client import Counter, Histogram, Gauge
from typing import Optional, Dict, Any
from starlette_prometheus import PrometheusMiddleware, metrics

class MetricsManager:
    _instance = None
    
    def __init__(self, app: Optional[FastAPI] = None):
        if app:
            app.add_middleware(PrometheusMiddleware)
            app.add_route("/metrics", metrics)

        # Define custom metrics
        self.request_count = Counter(
            'atlas_request_count', 
            'Number of requests received',
            ['method', 'endpoint', 'status_code']
        )
        
        self.request_latency = Histogram(
            'atlas_request_latency_seconds',
            'Request latency in seconds',
            ['method', 'endpoint']
        )
        
        self.active_users = Gauge(
            'atlas_active_users',
            'Number of currently active users'
        )

        # Add blockchain metrics
        self.blockchain_ops = Counter(
            'atlas_blockchain_operations_total',
            'Number of blockchain operations',
            ['operation_type', 'status']
        )

        self.deployment_time = Histogram(
            'atlas_contract_deployment_seconds',
            'Time taken to deploy contracts',
            ['contract_type']
        )

        self.gas_usage = Histogram(
            'atlas_gas_usage',
            'Gas used by operations',
            ['operation_type']
        )

    @classmethod
    def get_instance(cls, app: Optional[FastAPI] = None) -> 'MetricsManager':
        if not cls._instance:
            cls._instance = MetricsManager(app)
        return cls._instance

    def get_metrics(self) -> Dict[str, Any]:
        return {
            'request_count': self.request_count,
            'request_latency': self.request_latency,
            'active_users': self.active_users,
            'blockchain_ops': self.blockchain_ops,
            'deployment_time': self.deployment_time,
            'gas_usage': self.gas_usage
        }