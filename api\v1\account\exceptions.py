from sqlalchemy.orm import Session
from api.v1.websockets.models import Notification, NotificationType, NotificationPriority
from api.v1.websockets.services import NotificationService
from api.v1.websockets.schemas import NotificationCreate
from datetime import datetime
from typing import Optional
from api.core.logging_config import get_logger
from fastapi import HTTPException, status
logger = get_logger(__name__)


def handle_wallet_notification(user_id: int, action: str, wallet_address: Optional[str] = None):
    """Handle wallet-related notifications"""
    messages = {
        "create_vault": f"Vault created successfully.",
        "create_vault_failed": f"Failed to create vault."
    }
    
    titles = {
        "create_vault": "Vault Creation",
        "create_vault_failed": "Vault Creation Failed"
    }
    
    if action in messages:
        message = messages[action]
        title = titles.get(action, "Wallet Notification")
        
        # Create notification with enhanced structure
        notification = NotificationCreate(
            title=title,
            message=message,
            type=NotificationType.WALLET,
            priority=NotificationPriority.NORMAL,
            metadata={
                "transaction_details": {
                    "action_type": action,
                    "status": "success" if not "failed" in action else "failed",
                    "detail": f"Wallet address - {wallet_address}" if wallet_address else None
                }
            },
            action_url=None
        )
        
        notification_service = NotificationService()
        notification_service.publish_notification(user_id=user_id, notification=notification)






class ContractError(Exception):
    """Base exception for contract-related errors"""
    pass

class DataProcessingError(ContractError):
    """Exception raised for errors during data processing"""
    pass

class BlockchainError(ContractError):
    """Exception raised for blockchain interaction errors"""
    pass

class CacheError(ContractError):
    """Exception raised for caching-related errors"""
    pass




def handle_chart_error(error: Exception) -> None:
    """Handle errors in event processing"""
    #logger.error(f"Error processing events: {str(error)}")
    if isinstance(error, HTTPException):
        raise error
    elif isinstance(error, ValueError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(error)
        )
    elif isinstance(error, CacheError):
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Cache service temporarily unavailable"
        )
    elif isinstance(error, BlockchainError):
        raise HTTPException(
            status_code=status.HTTP_502_BAD_GATEWAY,
            detail="Failed to interact with blockchain"
        )
    elif isinstance(error, DataProcessingError):
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process contract data"
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )
