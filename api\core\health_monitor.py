from api.db.backup import BackupManager as AzureBackupManager
from api.core.logging_config import get_logger
import asyncio
import os
import json
from typing import Dict, Any, Optional
from config import config
from datetime import datetime

logger = get_logger(__name__)

class HealthMonitor:
    """Helper class for health monitoring operations"""
    
    def __init__(self):
        self.replica_config = {
            "host": config.REPLICA_DB_HOST,
            "port": config.REPLICA_DB_PORT,
            "user": config.REPLICA_DB_USER,
            "password": config.REPLICA_DB_PASSWORD,
            "name": config.REPLICA_DB_NAME
        }
        self.backup_manager = AzureBackupManager() if config.PYTHON_ENV == "production" else None
        self.max_retries = 3

    async def check_replica_connection(self) -> bool:
        """Test connection to replica database"""
        if not self.replica_config.get("host"):
            return False
            
        test_cmd = (
            f"PGPASSWORD='{self.replica_config['password']}' "
            f"psql -h {self.replica_config['host']} "
            f"-p {self.replica_config['port']} "
            f"-U {self.replica_config['user']} "
            f"-d {self.replica_config['name']} "
            f"-c 'SELECT 1;'"
        )
        
        for attempt in range(self.max_retries):
            try:
                process = await asyncio.create_subprocess_shell(
                    test_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                if process.returncode == 0:
                    return True
                    
                logger.warning(f"Replica connection attempt {attempt + 1} failed: {stderr.decode()}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2)
                    
            except Exception as e:
                logger.error(f"Error testing replica connection: {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(2)
                    
        return False

    async def update_connection_settings(self) -> bool:
        """Update application connection settings to use replica"""
        try:
            # Update environment through config
            config.DB_HOST = self.replica_config["host"]
            config.DB_PORT = self.replica_config["port"]
            config.DB_USER = self.replica_config["user"]
            config.DB_PASSWORD = self.replica_config["password"]
            config.DB_NAME = self.replica_config["name"]
            
            logger.info("Database connection settings updated to use replica")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update connection settings: {e}")
            return False

    async def promote_replica_to_primary(self) -> bool:
        """Promote replica to primary after failover"""
        try:
            logger.info("Promoting replica to primary database")
            # Update status in config
            config.USING_REPLICA = False
            config.PROMOTION_TIME = datetime.now().isoformat()
                
            logger.info("Replica promoted to primary successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to promote replica to primary: {e}")
            return False