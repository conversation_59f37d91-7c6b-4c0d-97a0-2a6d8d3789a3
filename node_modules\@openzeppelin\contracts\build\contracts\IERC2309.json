{"_format": "hh-sol-artifact-1", "contractName": "IERC2309", "sourceName": "contracts/interfaces/IERC2309.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "fromTokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "toTokenId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "fromAddress", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to<PERSON><PERSON><PERSON>", "type": "address"}], "name": "ConsecutiveTransfer", "type": "event"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}