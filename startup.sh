#!/bin/bash
set -e  # Exit on error

# Load environment variables if present
if [ -f .env ]; then
    source .env
fi

# Activate virtual environment
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# Start Redis if not running externally
#redis-server --daemonize yes || true
#celery -A api.core.tasks beat --loglevel=info
#celery -A api.core.tasks worker --loglevel=info --pool=gevent

# Start Celery worker with proper settings


# Run Gunicorn with proper settings
exec gunicorn main:app \
    --config gunicorn.conf.py \
    --pid gunicorn.pid \
    --capture-output