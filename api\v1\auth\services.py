import passlib.hash as _hash
import secrets
from passlib.context import <PERSON>pt<PERSON>ontext
from fastapi.security import <PERSON>Auth2Pass<PERSON><PERSON>earer
from fastapi import Depends
from jose import JWTError, jwt
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session  # Keep for backward compatibility during transition
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTT<PERSON>Ex<PERSON>, status
from datetime import datetime, timedelta, timezone
from typing import Annotated, Union
from uuid import uuid4
from api.v1.auth.models import  BlackListToken, APIKey
from api.v1.user.models import  User as UserModel
from api.v1.user.models import  Avater as AvaterModel
from api.v1.user import schemas as user_schema
from api.v1.auth import schemas as auth_schema
from api.core import responses
from api.v1.subscription.models import user_subscription, SubscriptionPlan, SubscriptionTier
from config import config
from api.v1.wallet import models as user_wallet
from typing import Dict
import requests as requesting
from api.v1.wallet.services import WalletManager
from api.v1.auth.exceptions import (
    logger,
    AuthError,
    InvalidCredentialsError,
    TokenExpiredError,
    BlacklistedTokenError,
    MissingTokenError,
    PasswordMismatchError,
    GoogleAuthError,
    WalletAuthError,
    APIKeyError
)
from celery import Celery
from api.db.database import SessionLocal
from api.core.tasks import celery_app
from sqlalchemy.future import select
from asgiref.sync import async_to_sync
#from celery import shared_task

"""
SECRET_KEY = config('SECRET_KEY')
ALGORITHM = config("ALGORITHM")
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/login")
JWT_REFRESH_EXPIRY = int(config("JWT_REFRESH_EXPIRY"))
"""


SECRET_KEY = config.JWT_SECRET
ALGORITHM = config.ALGORITHM
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/login")
JWT_REFRESH_EXPIRY = config.JWT_REFRESH_EXPIRY

#NOTIFICATION_CHANNEL = "subscription_tasks"
#REDIS_URL = config.REDIS_URL
#celery_app = Celery(NOTIFICATION_CHANNEL, broker=REDIS_URL)

class Auth():

    def __init__(self) -> None:
        pass

    @classmethod
    async def get_current_user(cls, token: Annotated[str, Depends(oauth2_scheme)], db: AsyncSession) -> user_schema.User:
        credentials_exception = HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=responses.COULD_NOT_VALIDATE_CRED,
            headers={"WWW-Authenticate": "Bearer"},
        )
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            id: str = payload.get("id")
            if id is None:
                raise credentials_exception
            token_data = id
        except JWTError:
            raise credentials_exception

        # Use SQLAlchemy 2.0 style querying with async
        result = await db.execute(select(UserModel).filter(UserModel.id == id))
        user = result.scalar_one_or_none()

        if user is None:
            raise credentials_exception
        return user

    @classmethod
    async def authenticate_user(cls, db: AsyncSession, password: str, email: str) -> user_schema.User:
        result = await db.execute(select(UserModel).filter(UserModel.email == email))
        user = result.scalar_one_or_none()
        if not user:
            return None
        if not cls.verify_password(password, user.password):
            return None
        return user

    @classmethod
    async def authenticate_api_key(cls, db: AsyncSession, key: str):
        invalid_token = await cls.check_token_blacklist(db=db, token=key)

        if invalid_token == True:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,
                                detail=responses.INVALID_CREDENTIALS,
                                    headers={"WWW-Authenticate": "Bearer"})

        result = await db.execute(select(APIKey).filter(APIKey.api_key == key))
        fetched_api = result.scalar_one_or_none()

        if fetched_api is None:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=responses.INVALID_CREDENTIALS)

        if not cls.verify_password(password=key, hashed_password=fetched_api.key_hash):
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=responses.INVALID_CREDENTIALS)

        result = await db.execute(select(UserModel).filter(UserModel.id == fetched_api.user_id))
        user = result.scalar_one_or_none()

        if not user:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=responses.INVALID_CREDENTIALS)

        return user


    @staticmethod
    def verify_password(password, hashed_password):
        password = pwd_context.verify(password, hashed_password)
        if not password:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=responses.INVALID_CREDENTIALS)
        return password

    @staticmethod
    def hash_password(password) -> str:
        return pwd_context.hash(password)

    @staticmethod
    async def create_access_token(data: dict, db: AsyncSession, expires_delta: timedelta = None) -> str:
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.now(timezone.utc) + expires_delta
        else:
            expire = datetime.now(timezone.utc) + timedelta(minutes=30)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

        await db.commit()

        return encoded_jwt

    @staticmethod
    async def create_refresh_token(data: dict, db: AsyncSession) -> str:
        to_encode = data.copy()

        expire = datetime.now(timezone.utc) + timedelta(seconds=int(JWT_REFRESH_EXPIRY))
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

        return encoded_jwt

    @classmethod
    async def verify_access_token(cls, token: str, db: AsyncSession) -> auth_schema.TokenData:
        try:
            invalid_token = await cls.check_token_blacklist(db=db, token=token)
            if invalid_token == True:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,
                                    detail=responses.INVALID_CREDENTIALS,
                                     headers={"WWW-Authenticate": "Bearer"})

            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            id: int = payload.get("id")

            if id is None:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,detail=responses.INVALID_CREDENTIALS,
                                     headers={"WWW-Authenticate": "Bearer"})

            result = await db.execute(select(UserModel).filter(UserModel.id == id))
            user = result.scalar_one_or_none()

            if user is None:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=responses.INVALID_CREDENTIALS,
                                    headers={"WWW-Authenticate": "Bearer"})


            token_data = auth_schema.TokenData(email=user.email, id=id)

            return token_data

        except JWTError as error:
            logger.error(f"JWT error: {error}")
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,detail=responses.INVALID_CREDENTIALS,
                                     headers={"WWW-Authenticate": "Bearer"})

    @classmethod
    async def verify_refresh_token(cls, refresh_token: str, db: AsyncSession) ->  auth_schema.TokenData:
        try:
            if not refresh_token:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=responses.EXPIRED)

            payload = jwt.decode(refresh_token, SECRET_KEY, algorithms=[ALGORITHM])
            id: str = payload.get("id")

            if id is None:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,detail=responses.INVALID_CREDENTIALS,
                                     headers={"WWW-Authenticate": "Bearer"})

            result = await db.execute(select(UserModel).filter(UserModel.id == id))
            user = result.scalar_one_or_none()

            if user is None:
                raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=responses.INVALID_CREDENTIALS)


            token_data = auth_schema.TokenData(email=user.email, id=id)

        except JWTError:
            raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED,detail=responses.INVALID_CREDENTIALS,
                                     headers={"WWW-Authenticate": "Bearer"})

        return token_data

    @staticmethod
    async def check_token_blacklist(token: str, db: AsyncSession)-> bool:
        result = await db.execute(select(BlackListToken).filter(BlackListToken.token == token))
        fetched_token = result.scalar_one_or_none()

        if fetched_token:
            return True
        else:
            return False

    @staticmethod
    async def logout(token: str, user: user_schema.ShowUser, db: AsyncSession) -> str:
        blacklist_token = BlackListToken(
            token=token.split(' ')[1],
            created_by=user.id
        )

        db.add(blacklist_token)
        await db.commit()

        return {"message": responses.LOG_OUT_SUCCESSFULLY}


    async def reset_password(cls, password_change: auth_schema.ResetPassword, user: user_schema.CreateUser, db: AsyncSession) -> str:

        cls.verify_password(password_change.current_password, user.password)

        if password_change.new_password != password_change.confirm_password:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=responses.PASSWORD_MISMATCH)

        new_hashed_password = pwd_context.hash(password_change.new_password)

        user.password = new_hashed_password
        await db.commit()

        return {"message": responses.PASSWORD_RESET_SUCCESSFULLY}



    @classmethod
    async def create_google_user(cls, user_info: user_schema.GoogleUserInfo, db: AsyncSession) -> user_schema.BaseModel:
        try:
            # Find user by email
            result = await db.execute(select(UserModel).filter(UserModel.email == user_info.email))
            current_user = result.scalar_one_or_none()

            if current_user:
                current_user.first_name = user_info.given_name if user_info.given_name else "Unknown"
                current_user.last_name = user_info.family_name if user_info.family_name else "Unknown"
                current_user.last_updated = datetime.now(timezone.utc)

                # Find existing avatar
                avatar_result = await db.execute(select(AvaterModel).filter(AvaterModel.user_id == current_user.id))
                existing_avatar = avatar_result.scalar_one_or_none()

                if existing_avatar:
                    existing_avatar.avatar_url = str(user_info.picture)
                    existing_avatar.last_updated = datetime.now(timezone.utc)
                else:
                    new_avatar = AvaterModel(
                        user_id=current_user.id,
                        avatar_url=str(user_info.picture)
                    )
                    db.add(new_avatar)

                await db.commit()

            else:
                random_password = secrets.token_urlsafe(32)
                hashed_password = cls.hash_password(random_password)

                current_user = UserModel(
                    first_name=user_info.given_name if user_info.given_name else "Unknown",
                    last_name=user_info.family_name if user_info.family_name else "Unknown",
                    email=user_info.email,
                    password=hashed_password,
                    is_active=True
                )

                db.add(current_user)
                await db.flush()

                new_avatar = AvaterModel(
                    user_id=current_user.id,
                    avatar_url=str(user_info.picture)
                )
                db.add(new_avatar)
                await db.commit()

                # Create free subscription
                try:
                    await cls.create_free_subscription(current_user.id, db)
                except Exception as sub_error:
                    logger.error(f"Failed to create free subscription: {str(sub_error)}")

            return current_user

        except SQLAlchemyError as db_error:
            db.rollback()
            logger.error(f"Database error during Google user creation: {str(db_error)}")
            raise GoogleAuthError(f"Database error: {str(db_error)}")
        except ValueError as val_error:
            db.rollback()
            logger.error(f"Value error during Google user creation: {str(val_error)}")
            raise GoogleAuthError(f"Invalid data: {str(val_error)}")
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error during Google user creation: {str(e)}", exc_info=True)
            raise GoogleAuthError(f"Error creating/updating user: {str(e)}")



    @staticmethod
    async def create_free_subscription(user_id: int, db: AsyncSession) -> user_subscription:
        """
        Creates a free (BASIC) subscription for a user

        """
        try:
            result = await db.execute(select(SubscriptionPlan).filter(
                SubscriptionPlan.tier == SubscriptionTier.BASIC
            ))
            basic_plan = result.scalar_one_or_none()

            if not basic_plan:
                logger.error(f"Basic subscription plan not found for user_id: {user_id}")
                raise ValueError("Basic subscription plan not found")

            default_subscription = user_subscription(
                user_id=user_id,
                subscription_plan_id=basic_plan.id,
                start_date=datetime.now(timezone.utc),
                end_date=None,
                status='active'
            )

            db.add(default_subscription)
            await db.commit()
            return default_subscription

        except SQLAlchemyError as db_error:
            await db.rollback()
            logger.error(f"Database error creating subscription for user {user_id}: {str(db_error)}")
            raise AuthError(f"Database error creating subscription: {str(db_error)}")
        except ValueError as val_error:
            await db.rollback()
            logger.error(f"Value error creating subscription for user {user_id}: {str(val_error)}")
            raise AuthError(str(val_error))
        except Exception as e:
            await db.rollback()
            logger.error(f"Unexpected error creating subscription for user {user_id}: {str(e)}", exc_info=True)
            raise AuthError(f"Error creating free subscription: {str(e)}")




    async def create_or_get_polygon_wallet_user(cls, address: str, db: AsyncSession):

        try:
            if not address or len(address) < 10:
                raise ValueError("Invalid wallet address format")

            # Check if wallet exists
            result = await db.execute(select(user_wallet.WalletAddress).filter(
                user_wallet.WalletAddress.address == address))
            wallet = result.scalar_one_or_none()

            if wallet:
                # Get user associated with wallet
                user_result = await db.execute(select(UserModel).filter(UserModel.id == wallet.user_id))
                current_user = user_result.scalar_one_or_none()
                if not current_user:
                    logger.error(f"Wallet exists but associated user {wallet.user_id} not found")
                    raise WalletAuthError("User associated with wallet not found")
                return current_user

            # Create new user with wallet
            random_password = secrets.token_urlsafe(32)
            try:
                hashed_password = cls.hash_password(random_password)
            except Exception as hash_error:
                logger.error(f"Password hashing failed: {str(hash_error)}")
                raise WalletAuthError("Error creating secure password")

            current_user = UserModel(
                first_name=f"polygon_{address[:10]}",
                last_name=f"polygon_last_{address[:10]}",
                email=f"polygon{random_password[:5]}@amoy.com",
                password=hashed_password,
                is_active=True
            )

            db.add(current_user)
            await db.flush()

            try:
                # Create wallet address
                wallet_manager = WalletManager()
                await wallet_manager.create_wallet_address(
                    user=current_user,
                    wallet_address=address,
                    db=db
                )
            except Exception as wallet_error:
                await db.rollback()
                raise WalletAuthError(f"Failed to create wallet address: {str(wallet_error)}")

            await db.commit()

            # Create free subscription
            try:
                await cls.create_free_subscription(current_user.id, db)
            except AuthError as sub_error:
                logger.warning(f"Failed to create subscription for wallet user: {str(sub_error)}")

            return current_user

        except ValueError as val_error:
            db.rollback()
            logger.error(f"Value error during wallet user creation: {str(val_error)}")
            raise WalletAuthError(str(val_error))
        except SQLAlchemyError as db_error:
            db.rollback()
            logger.error(f"Database error during wallet user creation: {str(db_error)}")
            raise WalletAuthError(f"Database error: {str(db_error)}")
        except WalletAuthError:
            db.rollback()
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error creating wallet user: {str(e)}", exc_info=True)
            raise WalletAuthError(f"Error creating wallet user: {str(e)}")





class APIkey():

    def __init__(self) -> None:
        self.authenticate = Auth()

    async def create_api_key(self, name:str, user:user_schema.ShowUser, db: AsyncSession) -> auth_schema.APIKEY:
        try:
            api_key = secrets.token_hex(32)
            try:
                hash = self.authenticate.hash_password(api_key)
            except Exception as hash_error:
                logger.error(f"Failed to hash API key: {str(hash_error)}")
                raise APIKeyError("Failed to create secure API key")

            # Find non-basic subscription
            query = (
                select(user_subscription)
                .join(SubscriptionPlan)
                .filter(
                    user_subscription.user_id == user.id,
                    SubscriptionPlan.tier != SubscriptionTier.BASIC
                )
                .order_by(user_subscription.created_at.desc())  # Get the most recent subscription
                .limit(1)
            )
            result = await db.execute(query)
            non_basic_sub = result.scalar_one_or_none()

            # If no non-basic subscription, try to find basic subscription
            if not non_basic_sub:
                query = (
                    select(user_subscription)
                    .join(SubscriptionPlan)
                    .filter(
                        user_subscription.user_id == user.id,
                        SubscriptionPlan.tier == SubscriptionTier.BASIC
                    )
                    .order_by(user_subscription.created_at.desc())
                    .limit(1)
                )
                result = await db.execute(query)
                non_basic_sub = result.scalar_one_or_none()

            if not non_basic_sub:
                raise APIKeyError("No active subscription found for user")

            # Generate a unique name for the user's API keys
            base_name = name
            counter = 1

            # Check if name already exists
            query = select(APIKey).filter(APIKey.name == name, APIKey.user_id == user.id)
            result = await db.execute(query)
            while result.scalar_one_or_none():
                name = f"{base_name}_{counter}"
                counter += 1
                query = select(APIKey).filter(APIKey.name == name, APIKey.user_id == user.id)
                result = await db.execute(query)

            created_api_key = APIKey(
                user_id = user.id,
                name = name,
                key_hash = hash,
                api_key = api_key,
                subscription_plan_id=non_basic_sub.subscription_plan_id
            )

            db.add(created_api_key)
            await db.commit()
            await db.refresh(created_api_key)

            api_keys = auth_schema.APIKEY(
                    id=created_api_key.id,
                    api_key=created_api_key.api_key,
                    name=created_api_key.name,
                    subscription_plan_id = created_api_key.subscription_plan_id,
                    date_created=created_api_key.date_created,
                    active=created_api_key.active
                )
            return api_keys

        except APIKeyError:
            db.rollback()
            raise
        except SQLAlchemyError as db_error:
            db.rollback()
            logger.error(f"Database error creating API key: {str(db_error)}")
            raise
        except ValueError as val_error:
            db.rollback()
            logger.error(f"Value error creating API key: {str(val_error)}")
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error creating API key: {str(e)}", exc_info=True)
            raise


    @classmethod
    async def list_api_keys(cls, user:user_schema.ShowUser, db: AsyncSession) -> list[auth_schema.APIKEY]:
        try:
            result = await db.execute(select(APIKey).filter(APIKey.user_id == user.id))
            keys = result.scalars().all()

            api_keys = [
                auth_schema.APIKEY(
                    id=key.id,
                    name=key.name,
                    api_key=key.api_key,
                    subscription_plan_id = key.subscription_plan_id,
                    date_created=key.date_created,
                    active=key.active
                ) for key in keys
            ]
            return api_keys
        except SQLAlchemyError as db_error:
            logger.error(f"Database error listing API keys for user {user.id}: {str(db_error)}")
            raise APIKeyError(f"Failed to retrieve API keys: {str(db_error)}")
        except Exception as e:
            logger.error(f"Unexpected error listing API keys for user {user.id}: {str(e)}", exc_info=True)
            raise APIKeyError("Failed to retrieve API keys")

    async def regenerate_api_key(self, db: AsyncSession, user:user_schema.ShowUser, api_key_id: int) -> auth_schema.APIKEY:
        try:
            result = await db.execute(select(APIKey).filter(
                APIKey.id == api_key_id,
                APIKey.user_id == user.id
            ))
            existing_key = result.scalar_one_or_none()

            if not existing_key:
                logger.warning(f"API key {api_key_id} not found for user {user.id}")
                raise APIKeyError("API key not found")

            api_key = secrets.token_hex(32)
            try:
                hash = self.authenticate.hash_password(api_key)
            except Exception as hash_error:
                logger.error(f"Failed to hash API key: {str(hash_error)}")
                raise APIKeyError("Failed to create secure API key")

            existing_key.key_hash = hash
            existing_key.api_key = api_key
            await db.commit()

            api_keys = auth_schema.APIKEY(
                    id=existing_key.id,
                    name=existing_key.name,
                    api_key=existing_key.api_key,
                    subscription_plan_id = existing_key.subscription_plan_id,
                    date_created=existing_key.date_created,
                    active=existing_key.active
                )
            return api_keys

        except SQLAlchemyError as db_error:
            db.rollback()
            logger.error(f"Database error regenerating API key {api_key_id}: {str(db_error)}")
            raise APIKeyError(f"Database error: {str(db_error)}")
        except APIKeyError:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error regenerating API key {api_key_id}: {str(e)}", exc_info=True)
            raise APIKeyError(f"Error regenerating API key: {str(e)}")


    async def delete_api_key(self, user: user_schema.ShowUser, db: AsyncSession, api_key_id: int) -> str:
        try:
            result = await db.execute(select(APIKey).filter(
                APIKey.id == api_key_id,
                APIKey.user_id == user.id
            ))
            api_key = result.scalar_one_or_none()

            if not api_key:
                logger.warning(f"API key {api_key_id} not found for user {user.id}")
                raise APIKeyError("API key not found")

            await db.delete(api_key)
            await db.commit()
            return {"message": responses.API_DELTED_SUCCESSFULLY}

        except SQLAlchemyError as db_error:
            db.rollback()
            logger.error(f"Database error deleting API key {api_key_id}: {str(db_error)}")
            raise APIKeyError(f"Database error: {str(db_error)}")
        except APIKeyError:
            raise
        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error deleting API key {api_key_id}: {str(e)}", exc_info=True)
            raise APIKeyError(f"Error deleting API key: {str(e)}")




"""
@celery_app.task(
    bind=True,
    max_retries=3,
    retry_backoff=True,
    name="tasks.create_free_subscription"
)
def subscription_task(self, user_id: int):
    ""Separate background task function with proper error handling""
    try:
        return async_to_sync(_create_subscription)(user_id)
        # Create new db session for background task
        #import asyncio
        #return asyncio.run(_create_subscription(user_id))
    except Exception as e:
        logger.error(f"Failed to create subscription: {str(e)}")
        raise self.retry(exc=e)

async def _create_subscription(user_id: int):
    ""Async implementation of subscription creation""
    async with SessionLocal() as db:
        return await Auth.create_free_subscription(user_id, db)


def schedule_subscription_task(user_id: int) -> bool:
    ""Safely schedule subscription task with error handling""
    try:
        # Schedule task asynchronously
        task = subscription_task.delay(user_id)
        logger.info(f"Scheduled subscription task {task.id} for user {user_id}")
        return True

    except Exception as e:
        logger.error(f"Failed to schedule subscription task: {str(e)}")
        return False
"""