from sqlalchemy.ext.asyncio import AsyncSession
from api.db.database import get_db
from api.v1.user import schemas as user_schema
from api.core.dependencies import is_authenticated, not_authenticated
from fastapi import APIRouter, FastAPI, WebSocket, WebSocketDisconnect, Depends
from typing import List, Dict
from .services import NotificationService
import json
import asyncio
from api.db.database import SessionLocal
from .connection_manager import ConnectionManager
#from api.v1.contracts.services import ContractService
from .services import notification_manager

app = APIRouter(tags=["WebSockets"])

fastapp = FastAPI()
fastapp.include_router(app)




@app.websocket("/ws/notifications/")
async def notification_websocket(
    websocket: WebSocket,
    db: AsyncSession=Depends(get_db)
):
    try:
        current_user = websocket.query_params.get("token")
        user = await not_authenticated(current_user, db)
        await notification_manager.handle_websocket(websocket, user.id)
    except Exception as e:
        raise

"""
@app.post("/notification/read")
async def mark_notification(
    id: int,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    notification_service = NotificationService()
    notification_service.mark_notification(id, db, user)

    return {"detail": "Notification marked as read"}
"""


@app.get("/notification/")
async def get_notifications(
    notification_id: int,
    user: user_schema.User = Depends(is_authenticated),
    db: AsyncSession = Depends(get_db)
):
    notification_service = await NotificationService.create()
    notifications = await notification_service.get_notification(db, notification_id, user.id)
    return notifications







"""

@app.websocket("/ws/notifications/")
async def notifications_websocket(
    websocket: WebSocket,
    db: Session = Depends(get_db)
):
    #await connection_manager.connect(websocket, str(user.id))

    await websocket.accept()
    await websocket.send_text(json.dumps("Good"))

    current_user = websocket.query_params.get("token")

    user = is_authenticated(current_user, db)

    #user = oauth.get_current_user(current_user, db)

    try:
        while True:
            data = await websocket.receive_text()

            if data == "notification:new":
                await websocket.send_text(
                    f"notification:"
                )


                try:
                    # Create a new session for each iteration
                    with SessionLocal() as new_db:
                        print("starting new session")
                        notifications = notification_service.get_unread_notifications(new_db, user)
                        for notification in notifications:
                            print(notification)
                            await websocket.send_text(
                                f"notification:{notification.id}:{notification.message}"
                            )

                    try:
                        notification = await asyncio.wait_for(
                            notification_service.wait_for_notification(str(user.id)),
                            timeout=3600  # 30 seconds timeout
                        )
                        if notification:
                            await websocket.send_text(
                                f"notification:{notification.id}:{notification.message}"
                            )
                    except asyncio.TimeoutError:
                        # Send heartbeat or continue silently
                        await websocket.send_text("ping")
                        continue

                except Exception as e:
                    print(f"Error processing notifications: {e}")
                    continue

            elif data == "notification:read":
                with SessionLocal() as new_db:
                    notification_service.mark_notifications_read(new_db, user)

    except WebSocketDisconnect:
        connection_manager.disconnect(websocket, str(user.id))
    finally:
        # Cleanup
        if str(user.id) in notification_service.notification_queues:
            del notification_service.notification_queues[str(user.id)]




@app.post("/notification/read")
async def mark_notification(
    id: int,
    user: user_schema.User = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    notification_service.mark_notification(id, db, user)

    return {"detail": "Notification marked as read"}




"""
"""
@app.websocket("/ws/contract/activities")
async def contract_activities_websocket(
    websocket: WebSocket,
    user: dict = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    await connection_manager.connect(websocket, str(user.id))
    try:
        while True:
            data = await websocket.receive_text()
            if data == "contract:updated":
                # Fetch and send contract updates
                updates = contract_service.get_recent_contract_updates(db, user)
                for update in updates:
                    await connection_manager.send_personal_message(
                        f"contract:update:{update.contract_id}:{update.status}",
                        str(user.id)
                    )
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket, str(user.id))

@app.websocket("/ws/events")
async def events_websocket(
    websocket: WebSocket,
    user: dict = Depends(is_authenticated),
    db: Session = Depends(get_db)
):
    await connection_manager.connect(websocket, str(user.id))
    try:
        while True:
            data = await websocket.receive_text()
            if data == "events:all":
                # Get all events
                events = event_service.get_all_events(db, user)
                for event in events:
                    await connection_manager.send_personal_message(
                        f"event:{event.id}:{event.name}",
                        str(user.id)
                    )
            elif data == "events:latest":
                # Get latest top 20 events
                latest_events = event_service.get_latest_events(db, user, limit=20)
                for event in latest_events:
                    await connection_manager.send_personal_message(
                        f"event:{event.id}:{event.name}",
                        str(user.id)
                    )
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket, str(user.id))
"""
