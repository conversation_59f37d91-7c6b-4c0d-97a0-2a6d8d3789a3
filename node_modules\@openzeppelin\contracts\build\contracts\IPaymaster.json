{"_format": "hh-sol-artifact-1", "contractName": "IPaymaster", "sourceName": "contracts/interfaces/draft-IERC4337.sol", "abi": [{"inputs": [{"internalType": "enum IPaymaster.PostOpMode", "name": "mode", "type": "uint8"}, {"internalType": "bytes", "name": "context", "type": "bytes"}, {"internalType": "uint256", "name": "actualGasCost", "type": "uint256"}, {"internalType": "uint256", "name": "actualUserOpFeePerGas", "type": "uint256"}], "name": "postOp", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "bytes", "name": "initCode", "type": "bytes"}, {"internalType": "bytes", "name": "callData", "type": "bytes"}, {"internalType": "bytes32", "name": "accountGasLimits", "type": "bytes32"}, {"internalType": "uint256", "name": "preVerificationGas", "type": "uint256"}, {"internalType": "bytes32", "name": "gasFees", "type": "bytes32"}, {"internalType": "bytes", "name": "paymasterAndData", "type": "bytes"}, {"internalType": "bytes", "name": "signature", "type": "bytes"}], "internalType": "struct PackedUserOperation", "name": "userOp", "type": "tuple"}, {"internalType": "bytes32", "name": "userOpHash", "type": "bytes32"}, {"internalType": "uint256", "name": "maxCost", "type": "uint256"}], "name": "validatePaymasterUserOp", "outputs": [{"internalType": "bytes", "name": "context", "type": "bytes"}, {"internalType": "uint256", "name": "validationData", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}