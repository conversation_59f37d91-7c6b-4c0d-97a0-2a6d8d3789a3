# # Gunicorn configuration file
# import multiprocessing

# max_requests = 1000
# max_requests_jitter = 50

# worker_class = "uvicorn.workers.UvicornWorker"
# workers = multiprocessing.cpu_count() * 2 + 1

# bind = "0.0.0.0:8000"
# timeout = 600
# keepalive = 5
"""
import multiprocessing

# Gunicorn config for performance tuning
max_requests = 1000
max_requests_jitter = 50

worker_class = "uvicorn.workers.UvicornWorker"
workers = multiprocessing.cpu_count() * 2 + 1  # Auto-scaled workers

bind = "0.0.0.0:8000"  # Bind to the correct host and port for Azure
timeout = 600  # Adjust timeout for long-running requests
keepalive = 5  # Reduce keepalive timeout
"""




import multiprocessing
import os

# Performance tuning
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 50

# Networking
bind = "0.0.0.0:8000"
backlog = 2048
keepalive = 5
timeout = 600

# Security
limit_request_line = 4096
limit_request_fields = 100
limit_request_field_size = 8190

# Reload & Graceful handling
graceful_timeout = 30
reload = False  # Set to True only in development

# Process naming
proc_name = "atlas_api"
