import logging
import logging.handlers
import os
import sys
import time
import functools
import traceback
import json
from pathlib import Path
from typing import Dict, Any, Optional, Callable, TypeVar, cast

# Define log levels
LOG_LEVELS = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL,
}

# Type variables for function annotation
F = TypeVar('F', bound=Callable[..., Any])

# Create logs directory if it doesn't exist
def ensure_log_directory(log_dir: str = "logs") -> str:
    """Create log directory if it doesn't exist"""
    log_dir_path = Path(log_dir)
    log_dir_path.mkdir(exist_ok=True)
    return str(log_dir_path)

class JsonFormatter(logging.Formatter):
    """
    Formatter that outputs JSON strings after parsing the log record.
    """
    def __init__(self, **kwargs):
        self.default_fields = kwargs
        super().__init__()

    def format(self, record):
        log_record = {
            'timestamp': self.formatTime(record),
            'name': record.name,
            'level': record.levelname,
            'message': record.getMessage(),
        }
        
        # Add exception info if available
        if record.exc_info:
            log_record['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields from the record
        if hasattr(record, 'extra_fields'):
            log_record.update(record.extra_fields)
            
        # Add default fields
        log_record.update(self.default_fields)
        
        return json.dumps(log_record)

class LoggerManager:
    """Singleton class for managing loggers"""
    _instance = None
    _loggers: Dict[str, logging.Logger] = {}
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LoggerManager, cls).__new__(cls)
        return cls._instance
    
    def configure(
        self,
        log_level: str = "INFO",
        log_to_file: bool = True,
        log_to_console: bool = True,
        log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        log_file_path: Optional[str] = None,
        rotation_size: int = 10 * 1024 * 1024,  # 10 MB
        backup_count: int = 5,
        use_json_format: bool = False,
        environment: str = "dev"
    ):
        """Configure global logging settings"""
        if self._initialized:
            return
            
        level = LOG_LEVELS.get(log_level.upper(), logging.INFO)
        
        # Choose formatter based on settings
        if use_json_format:
            formatter = JsonFormatter(environment=environment)
        else:
            formatter = logging.Formatter(log_format)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # Clear existing handlers to avoid duplicates
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Console handler - always show debug in console for development
        if log_to_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            console_handler.setLevel(logging.DEBUG if environment == "dev" else level)
            root_logger.addHandler(console_handler)
        
        # File handlers
        if log_to_file:
            # Ensure logs directory exists
            log_dir = ensure_log_directory()
            
            # Application log - exclude debug in production
            app_log_path = log_file_path or os.path.join(log_dir, "app.log")
            file_handler = logging.handlers.RotatingFileHandler(
                app_log_path,
                maxBytes=rotation_size,
                backupCount=backup_count
            )
            file_handler.setFormatter(formatter)
            # In production, don't log debug messages to file
            file_handler.setLevel(logging.INFO if environment == "production" else level)
            root_logger.addHandler(file_handler)
            
            # Error log (ERROR and above)
            error_log_path = os.path.join(log_dir, "error.log")
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_path,
                maxBytes=rotation_size,
                backupCount=backup_count
            )
            error_handler.setFormatter(formatter)
            error_handler.setLevel(logging.ERROR)
            root_logger.addHandler(error_handler)
            
            # Debug log (only in development)
            if environment == "dev":
                debug_log_path = os.path.join(log_dir, "debug.log")
                debug_handler = logging.handlers.RotatingFileHandler(
                    debug_log_path,
                    maxBytes=rotation_size,
                    backupCount=backup_count
                )
                debug_handler.setFormatter(formatter)
                debug_handler.setLevel(logging.DEBUG)
                root_logger.addHandler(debug_handler)
        
        # Mark as initialized
        self._initialized = True
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get or create a logger with the given name"""
        if name not in self._loggers:
            self._loggers[name] = logging.getLogger(name)
        return self._loggers[name]

# Create singleton instance
logger_manager = LoggerManager()

def setup_logging(
    log_level: str = "INFO",
    log_to_file: bool = True,
    log_to_console: bool = True,
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(process)d - %(thread)d - %(message)s",
    log_file_path: Optional[str] = None,
    use_json_format: bool = False,
    environment: str = "dev"
) -> None:
    """Initialize logging configuration"""
    logger_manager.configure(
        log_level=log_level,
        log_to_file=log_to_file,
        log_to_console=log_to_console,
        log_format=log_format,
        log_file_path=log_file_path,
        use_json_format=use_json_format,
        environment=environment,
    )

def get_logger(name: str) -> logging.Logger:
    """Get a logger with the given name"""
    return logger_manager.get_logger(name)

# Utility decorators for enhanced logging

def log_execution_time(logger=None):
    """Decorator to log the execution time of a function"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            log = logger or get_logger(func.__module__)
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            execution_time = end_time - start_time
            log.debug(f"Function '{func.__name__}' executed in {execution_time:.4f} seconds")
            return result
        return cast(F, wrapper)
    return decorator

def log_exceptions(logger=None, level=logging.ERROR, reraise=True):
    """Decorator to log exceptions raised by a function"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            log = logger or get_logger(func.__module__)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                log.log(
                    level,
                    f"Exception in {func.__name__}: {str(e)}",
                    exc_info=True,
                    extra={"traceback": traceback.format_exc()}
                )
                if reraise:
                    raise
        return cast(F, wrapper)
    return decorator

class LogContext:
    """Context manager for adding additional context to log records"""
    def __init__(self, logger, **kwargs):
        self.logger = logger
        self.kwargs = kwargs
        self.old_factory = None

    def __enter__(self):
        # Save the old factory
        self.old_factory = logging.getLogRecordFactory()
        
        # Define a new factory that adds our extra fields
        old_factory = self.old_factory
        kwargs = self.kwargs
        
        def record_factory(*args, **kws):
            record = old_factory(*args, **kws)
            record.extra_fields = kwargs
            return record
            
        # Set our factory as the new factory
        logging.setLogRecordFactory(record_factory)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore the old factory
        logging.setLogRecordFactory(self.old_factory)
        
# Example usage:
# with LogContext(logger, user_id="123", action="login"):
#     logger.info("User login attempt") 