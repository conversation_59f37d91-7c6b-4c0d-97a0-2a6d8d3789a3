import logging
from fastapi import HTTPEx<PERSON>, status
from api.core.logging_config import get_logger

logger = get_logger(__name__)

class NotificationError(Exception):
    """Base exception for notification service errors"""
    pass

class NotificationCreationError(NotificationError):
    """Raised when creating a notification fails"""
    pass

class NotificationNotFoundError(NotificationError):
    """Raised when a notification cannot be found"""
    pass

class NotificationUpdateError(NotificationError):
    """Raised when updating a notification fails"""
    pass

class NotificationDeleteError(NotificationError):
    """Raised when deleting a notification fails"""
    pass

class WebSocketConnectionError(NotificationError):
    """Raised when WebSocket connection fails"""
    pass

class RedisConnectionError(NotificationError):
    """Raised when Redis connection fails"""
    pass

class InvalidNotificationDataError(NotificationError):
    """Raised when notification data is invalid"""
    pass

class UnauthorizedAccessError(NotificationError):
    """Raised when attempting to access unauthorized notifications"""
    pass

class DatabaseError(NotificationError):
    """Raised when database operations fail"""
    pass

class InfrastructureError(Exception):
    """Raised for infrastructure-related issues (e.g., queueing, network)."""
    pass

def handle_notification_error(error: Exception) -> None:
    """Convert notification errors to appropriate HTTP exceptions"""
    
    if isinstance(error, HTTPException):
        raise error
        
    error_mapping = {
        NotificationCreationError: {
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
            "prefix": "Failed to create notification"
        },
        NotificationNotFoundError: {
            "status_code": status.HTTP_404_NOT_FOUND,
            "prefix": "Notification not found"
        },
        NotificationUpdateError: {
            "status_code": status.HTTP_400_BAD_REQUEST,
            "prefix": "Failed to update notification"
        },
        NotificationDeleteError: {
            "status_code": status.HTTP_400_BAD_REQUEST,
            "prefix": "Failed to delete notification"
        },
        WebSocketConnectionError: {
            "status_code": status.HTTP_503_SERVICE_UNAVAILABLE,
            "prefix": "WebSocket connection error"
        },
        RedisConnectionError: {
            "status_code": status.HTTP_503_SERVICE_UNAVAILABLE,
            "prefix": "Redis connection error"
        },
        InvalidNotificationDataError: {
            "status_code": status.HTTP_400_BAD_REQUEST,
            "prefix": "Invalid notification data"
        },
        UnauthorizedAccessError: {
            "status_code": status.HTTP_403_FORBIDDEN,
            "prefix": "Unauthorized access"
        },
        DatabaseError: {
            "status_code": status.HTTP_500_INTERNAL_SERVER_ERROR,
            "prefix": "Database operation failed"
        }
    }

    for error_type, error_config in error_mapping.items():
        if isinstance(error, error_type):
            logger.error(f"{error_config['prefix']}: {str(error)}")
            raise HTTPException(
                status_code=error_config["status_code"],
                detail=f"{error_config['prefix']}: {str(error)}"
            )

    logger.error(f"Unexpected error: {str(error)}", exc_info=True)
    raise HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=f"An unexpected error occurred: {str(error)}"
    )
