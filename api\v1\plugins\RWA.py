contract_source_code = """// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

contract RWA is ERC721, Ownable {
    uint256 public nextTokenId;
    mapping(uint256 => string) private _tokenURIs;
    mapping(uint256 => string) public assetDetails; // Mapping for additional metadata

    constructor() ERC721("RealWorldAssetToken", "RWA") {}

    // Mint a new token with asset details
    function mint(
        address to,
        string memory tokenURI,
        string memory details
    ) public onlyOwner {
        uint256 tokenId = nextTokenId;
        _safeMint(to, tokenId);
        _setTokenURI(tokenId, tokenURI);
        assetDetails[tokenId] = details; // Store asset-specific details
        nextTokenId++;
    }

    // Internal function to set token URI
    function _setTokenURI(uint256 tokenId, string memory tokenURI) internal {
        _tokenURIs[tokenId] = tokenURI;
    }

    // Retrieve the token URI
    function tokenURI(uint256 tokenId) public view override returns (string memory) {
        return _tokenURIs[tokenId];
    }

    // Write function to update asset details
    function updateAssetDetails(uint256 tokenId, string memory details) public onlyOwner {
        require(_exists(tokenId), "Token ID does not exist");
        assetDetails[tokenId] = details;
    }

    // Read function to fetch asset details
    function getAssetDetails(uint256 tokenId) public view returns (string memory) {
        require(_exists(tokenId), "Token ID does not exist");
        return assetDetails[tokenId];
    }

    
    // Set the price for a token
    function setTokenPrice(uint256 tokenId, uint256 price) public {
        require(ownerOf(tokenId) == msg.sender, "Only the owner can set the price");
        tokenPrices[tokenId] = price;
    }

    // Purchase a token
    function purchaseToken(uint256 tokenId) public payable {
        uint256 price = tokenPrices[tokenId];
        address owner = ownerOf(tokenId);

        require(price > 0, "Token is not for sale");
        require(msg.value >= price, "Insufficient payment");
        require(msg.sender != owner, "Cannot purchase your own token");

        // Transfer ownership
        _transfer(owner, msg.sender, tokenId);

        // Transfer payment
        payable(owner).transfer(msg.value);

        // Reset token price
        tokenPrices[tokenId] = 0;
    }

    // Transfer token (inherits ERC721's transferFrom and safeTransferFrom)

}
"""