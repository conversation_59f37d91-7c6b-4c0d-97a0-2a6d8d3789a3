from api.db.backup import BackupManager
from api.core.security_config import SecurityManager
from api.core.monitoring import MonitoringSystem
from api.core.testing import TestingManager
from api.core.feedback import FeedbackManager
from api.core.proxy import ProxyManager
import asyncio
# Initialize managers
security_manager = SecurityManager()
monitoring_system = MonitoringSystem()
backup_manager = BackupManager()
testing_manager = TestingManager()
feedback_manager = FeedbackManager()
#proxy_manager = ProxyManager() 
