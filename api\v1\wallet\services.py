from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from fastapi import HTTPException
from . import models, schemas
from api.v1.user import schemas as user_schema
from web3 import Web3
import asyncio
import requests

POLYGONSCAN_API_KEY = "85W8PAP5F1Z2AWFRI3Q5QAN68VBQQNHATI"
ALCHEMY_API_KEY = "************************************"
POLYGONSCAN_BASE_URL = "https://api.polygonscan.com/api"
#WEB3_PROVIDER = f"https://polygon-mainnet.g.alchemy.com/v2/{ALCHEMY_API_KEY}"
WEB3_PROVIDER = "https://polished-distinguished-arm.matic-amoy.quiknode.pro/91386aae2ed884f3953aafd00d41b5a3c97351e3/"
web3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER))
import requests




async def wallet_router(
    wallet_address: str,
    user: user_schema.User,
    db: AsyncSession,
):
    """
    FastAPI router for wallet connection
    """

    try:
        wallet_manager = WalletManager()

        db_wallet = await wallet_manager.create_wallet_address(
            user=user,
            wallet_address=wallet_address,
            db=db
        )

        wallet_details = await wallet_manager.get_wallet_details(wallet_address)

        return schemas.WalletAddressResponse(
            id=db_wallet.id,
            user_id=db_wallet.user_id,
            address=db_wallet.address,
            balance=wallet_details['balance']['matic'],
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))





async def wallet_details(
    id: int,
    user: user_schema.User,  # Required for authorization, even if not directly used
    db: AsyncSession,
):
    """
    FastAPI router for wallet connection
    """

    try:
        wallet_manager = WalletManager()

        # Use SQLAlchemy's future select() instead of db.query
        result = await db.execute(
            select(models.WalletAddress).filter(models.WalletAddress.id == id)
        )
        wallet = result.scalar_one_or_none()
        if not wallet:
            raise HTTPException(status_code=404, detail="Wallet not found")

        wallet_details = await wallet_manager.get_wallet_details(wallet.address)
        print(wallet_details['transactions'])
        return {
            "id": wallet.id,
            "user_id": wallet.user_id,
            "address": wallet.address,
            "balance": wallet_details['balance']['matic'],
            "transactions": wallet_details['transactions']
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Wallet connection failed; {str(e)}")







async def wallet_get(
    user: user_schema.User,
    db: AsyncSession,
):
    """
    FastAPI router for wallet connection
    """

    try:
        # Use SQLAlchemy's future select() instead of db.query
        result = await db.execute(
            select(models.WalletAddress).filter(models.WalletAddress.user_id == user.id)
        )
        wallets = result.scalars().all()
        if not wallets:
            raise HTTPException(status_code=404, detail="No wallets found for this user")

        all_wallets = [{"id": wallet.id, "address": wallet.address} for wallet in wallets]
        print(all_wallets)
        return all_wallets
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Wallet connection failed; {str(e)}")




class WalletManager:

    @staticmethod
    def validate_wallet_address(address: str) -> bool:
        """
        Validate Ethereum/Polygon wallet address
        """
        if not address:
            return False
        try:
            web3.to_checksum_address(address)
            return True
        except ValueError:
            return False

    @classmethod
    async def create_wallet_address(
        cls,
        user: user_schema.User,
        wallet_address: str,
        db: AsyncSession,
    ) -> models.WalletAddress:
        """
        Create and validate a wallet address in the database

        """
        if not cls.validate_wallet_address(wallet_address):
            raise ValueError("Invalid wallet address")

        # Use SQLAlchemy's future select() instead of db.query
        result = await db.execute(
            select(models.WalletAddress).filter(models.WalletAddress.address == wallet_address)
        )
        wallet = result.scalar_one_or_none()

        if wallet:
            raise ValueError(f"Wallet address '{wallet_address}' already exists")

        db_wallet = models.WalletAddress(
            user_id=user.id,
            address=wallet_address,
            # chain_id=chain_id,
        )

        db.add(db_wallet)
        await db.commit()
        await db.refresh(db_wallet)

        return db_wallet

    @classmethod
    async def get_wallet_details(
        cls,
        address: str,
        chain: str = 'polygon'  # Chain parameter for future multi-chain support
    ):
        """
        Retrieve wallet details including balance and recent transactions

        """
        if not cls.validate_wallet_address(address):
            raise ValueError("Invalid wallet address")

        try:
            # Use asyncio to run the synchronous web3 call in a thread pool
            balance_wei = await asyncio.to_thread(web3.eth.get_balance, address)
            balance_matic = await asyncio.to_thread(web3.from_wei, balance_wei, 'ether')
        except Exception as e:
            balance_wei = None
            balance_matic = None

        try:
            # Use asyncio to run the synchronous requests call in a thread pool
            transactions_response = await asyncio.to_thread(
                requests.get,
                POLYGONSCAN_BASE_URL,
                params={
                    'module': 'account',
                    'action': 'txlist',
                    'address': address,
                    'startblock': 0,
                    'endblock': ********,
                    'page': 1,
                    'offset': 10,
                    'sort': 'desc',
                    'apikey': POLYGONSCAN_API_KEY
                }
            )
            transactions = transactions_response.json().get('result', [])
        except Exception as e:
            transactions = []

        return {
            'address': address,
            'balance': {
                'wei': balance_wei,
                'matic': balance_matic
            },
            'transactions': transactions
        }
