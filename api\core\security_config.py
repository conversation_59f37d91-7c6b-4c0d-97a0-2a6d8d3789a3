from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any
import jwt
from datetime import datetime, timedelta
import hashlib
import secrets
from config import config
from api.core.logging_config import get_logger

logger = get_logger(__name__)
security = HTTPBearer()

class SecurityManager:
    """Manages security-related operations and validations"""
    
    @staticmethod
    def generate_secure_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """Generate a secure JWT token with additional security measures"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.now() + expires_delta
        else:
            expire = datetime.now() + timedelta(minutes=15)
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, config.JWT_SECRET, algorithm=config.ALGORITHM)
        return encoded_jwt

    @staticmethod
    def verify_token(token: str) -> Dict[str, Any]:
        """Verify JWT token with additional security checks"""
        try:
            payload = jwt.decode(token, config.JWT_SECRET, algorithms=[config.ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired"
            )
        except jwt.JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )

    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password with salt using secure algorithm"""
        salt = secrets.token_hex(16)
        hash_obj = hashlib.sha256()
        hash_obj.update((password + salt).encode())
        return f"{salt}:{hash_obj.hexdigest()}"

    @staticmethod
    def verify_password(password: str, hashed_password: str) -> bool:
        """Verify password against hash"""
        salt, stored_hash = hashed_password.split(':')
        hash_obj = hashlib.sha256()
        hash_obj.update((password + salt).encode())
        return hash_obj.hexdigest() == stored_hash

    @staticmethod
    def validate_contract_input(data: Dict[str, Any]) -> bool:
        """Validate smart contract input data"""
        required_fields = ['contract_address', 'function_name', 'parameters']
        if not all(field in data for field in required_fields):
            return False
        
        # Add additional validation for contract-specific parameters
        if data['function_name'] == 'transfer':
            if not isinstance(data['parameters'].get('amount'), (int, float)):
                return False
            if not isinstance(data['parameters'].get('recipient'), str):
                return False
        
        return True

    @staticmethod
    def sanitize_input(input_str: str) -> str:
        """Sanitize user input to prevent injection attacks"""
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '&', '"', "'", ';', '--', '/*', '*/']
        sanitized = input_str
        for char in dangerous_chars:
            sanitized = sanitized.replace(char, '')
        return sanitized

    @staticmethod
    def validate_wallet_address(address: str) -> bool:
        """Validate Ethereum wallet address format"""
        if not address or not isinstance(address, str):
            return False
        if not address.startswith('0x'):
            return False
        if len(address) != 42:  # 0x + 40 hex characters
            return False
        try:
            int(address[2:], 16)  # Check if it's valid hex
            return True
        except ValueError:
            return False

    @staticmethod
    def validate_transaction_amount(amount: float) -> bool:
        """Validate transaction amount"""
        if not isinstance(amount, (int, float)):
            return False
        if amount <= 0:
            return False
        if amount > float('inf'):
            return False
        return True

    @staticmethod
    def generate_api_key() -> str:
        """Generate a secure API key"""
        return secrets.token_urlsafe(32)

    @staticmethod
    def validate_api_key(api_key: str) -> bool:
        """Validate API key format"""
        if not api_key or not isinstance(api_key, str):
            return False
        if len(api_key) < 32:  # Minimum length for security
            return False
        return True

    @staticmethod
    def check_rate_limit(ip_address: str) -> bool:
        """Check if request is within rate limits"""
        # Implement rate limiting logic here
        # This should integrate with Redis for distributed rate limiting
        return True

    @staticmethod
    def validate_contract_bytecode(bytecode: str) -> bool:
        """Validate smart contract bytecode"""
        if not bytecode or not isinstance(bytecode, str):
            return False
        if not bytecode.startswith('0x'):
            return False
        try:
            int(bytecode[2:], 16)  # Check if it's valid hex
            return True
        except ValueError:
            return False

    @staticmethod
    def validate_contract_abi(abi: list) -> bool:
        """Validate smart contract ABI"""
        if not isinstance(abi, list):
            return False
        required_fields = ['type', 'name', 'inputs']
        for item in abi:
            if not all(field in item for field in required_fields):
                return False
        return True 