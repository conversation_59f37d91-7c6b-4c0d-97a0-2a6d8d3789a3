# services/rwa_plugin_service.py
from sqlalchemy.orm import Session
from fastapi import HTTPException, status, BackgroundTasks
from typing import List, Optional, Tuple
from web3 import Web3
import logging
from api.core.logging_config import get_logger

from api.v1.plugins.models import (
    RealWorldAsset, 
    AssetVerification, 
    RWAStatus, 
    RWAType
)
from api.v1.plugins.schemas import (
    CreateRealWorldAssetRequest, 
    RealWorldAssetResponse, 
    AssetVerificationRequest,
    RWATokenizationRequest
)
from api.core import responses
from . import models


PRIVATE_KEY="c9771e1a1eef172c3fd85ac4510b937bb768def9af60a092472b9c3e671598f6"

YOUR_ALCHEMY_API_KEY= "************************************"
#rpc_url = "https://mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID" 
#rpc_url = f"https://eth-mainnet.g.alchemy.com/v2/{YOUR_ALCHEMY_API_KEY}"  
#rpc_url = "https://polygon-rpc.com"
#rpc_url = f"https://polygonzkevm-mainnet.g.alchemy.com/v2/demo"
#web3 = Web3(Web3.HTTPProvider(rpc_url))


WEB3_PROVIDER = f"https://polygon-mainnet.g.alchemy.com/v2/{YOUR_ALCHEMY_API_KEY}"
web3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER))


if web3.is_connected():
    print("Connected to the blockchain!")

POLYGONSCAN_API_KEY = "85W8PAP5F1Z2AWFRI3Q5QAN68VBQQNHATI"
#POLYGONSCAN_BASE_URL = "https://polygonscan.com"
POLYGONSCAN_BASE_URL = "https://api.polygonscan.com/api"



ETHERSCAN_API_KEY = "**********************************"
ETHERSCAN_BASE_URL = "https://api.etherscan.io/api"

API_DEPLOY_ERC20_ENDPOINT = "https://api.services.stellus.io/contracts/deploy_erc20"
API_LOGIN_ENDPOINT = "https://api.services.stellus.io/auth/login"





POLYGONSCAN_API_KEY = "85W8PAP5F1Z2AWFRI3Q5QAN68VBQQNHATI"
ALCHEMY_API_KEY = "************************************"
POLYGONSCAN_BASE_URL = "https://api.polygonscan.com/api"
WEB3_PROVIDER = f"https://polygon-mainnet.g.alchemy.com/v2/{ALCHEMY_API_KEY}"
web3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER))


class RWAPluginService:
    def __init__(self):
        self.contract_manager = RWAContractManager()
        self.blockchain_providers = {
            'polygon': {
                #'rpc_url': f"https://polygon-mainnet.g.alchemy.com/v2/{YOUR_ALCHEMY_API_KEY}",
                'rpc_url': "https://rpc-amoy.polygon.technology/",                
                # Using Centrifuge's RWA Market contract as default
                'contract_factory_address': RWAContractManager.KNOWN_RWA_CONTRACTS['centrifuge'],
            }
        }


    def create_real_world_asset(
        self, 
        db: Session, 
        user_id: int, 
        asset_data: CreateRealWorldAssetRequest,
        asset_type: models.RWAType,
        token_standard: models.TokenStandard,

    ) -> RealWorldAsset:
        """
        Create a new Real World Asset entry
        """
        try:
            new_asset = RealWorldAsset(
                name=asset_data.name,
                description=asset_data.description,
                asset_type=asset_type,
                owner_id=user_id,
                initial_value=asset_data.initial_value,
                current_value=asset_data.initial_value,
                status=RWAStatus.DRAFT,
                total_tokens=asset_data.total_tokens,
                tokens_available=asset_data.total_tokens,
                token_price=asset_data.token_price,
                token_standard=token_standard
            )
            
            db.add(new_asset)
            db.commit()
            db.refresh(new_asset)
            
            return new_asset
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating asset: {str(e)}"
            )

    def get_asset(
        self, 
        db: Session, 
        asset_id: int
    ) -> Optional[RealWorldAsset]:
        """
        Retrieve a specific Real World Asset
        """
        asset = db.query(RealWorldAsset).filter(
            RealWorldAsset.id == asset_id
        ).first()
        
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        return asset

    def list_assets(
        self, 
        db: Session, 
        skip: int = 0, 
        limit: int = 10,
        user_id: Optional[int] = None,
        asset_type: Optional[RWAType] = None,
        status: Optional[RWAStatus] = None
    ) -> List[RealWorldAsset]:
        """
        List Real World Assets with optional filtering
        """
        query = db.query(RealWorldAsset)
        
        if user_id:
            query = query.filter(RealWorldAsset.owner_id == user_id)
        
        if asset_type:
            query = query.filter(RealWorldAsset.asset_type == asset_type)
        
        if status:
            query = query.filter(RealWorldAsset.status == status)
        
        return query.offset(skip).limit(limit).all()

    def verify_asset_documents(
        self, 
        db: Session, 
        verifier_id: int, 
        verification_data: AssetVerificationRequest
    ) -> AssetVerification:
        """
        Submit and process asset verification documents
        """
        try:
            # Find the asset
            asset = db.query(RealWorldAsset).filter(
                RealWorldAsset.id == verification_data.asset_id
            ).first()
            
            if not asset:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Aseset not found"
                )
            
            # Create verification entry
            verification = AssetVerification(
                asset_id=verification_data.asset_id,
                verifier_id=verifier_id,
                verification_type=verification_data.verification_type,
                verification_document_url=verification_data.verification_document_url,
                verification_status='pending'
            )
            
            db.add(verification)
            
            # Update asset status
            asset.status = RWAStatus.PENDING_VERIFICATION
            
            db.commit()
            db.refresh(verification)
            
            return verification
        except Exception as e:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error verifying asset: {str(e)}"
            )

    def tokenize_asset(
        self, 
        db: Session, 
        user_id: int, 
        tokenization_request: RWATokenizationRequest,
        protocol: str = 'centrifuge',  # Default to Centrifuge
        background_tasks: Optional[BackgroundTasks] = None
    ):
        """
        Tokenize a Real World Asset using existing RWA protocol
        """
        try:
            # Get contract address for specified protocol
            contract_address = self.contract_manager.get_known_contract_address(protocol)
            
            # Update provider config to use specified protocol
            provider_config = self.blockchain_providers['polygon']
            provider_config['contract_factory_address'] = contract_address
      
            # Find the asset
            asset = db.query(RealWorldAsset).filter(
                RealWorldAsset.id == tokenization_request.asset_id,
                RealWorldAsset.owner_id == user_id
            ).first()
            
            if not asset:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Asset not found"
                )
            
            # Validate asset is ready for tokenization
            """
            if asset.status != RWAStatus.VERIFIED:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Asset must be verified before tokenization"
                )
            """
            
            # Get blockchain provider configuration
            provider_config = self.blockchain_providers.get(
                tokenization_request.blockchain_network
            )
            
            if not provider_config:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Unsupported blockchain network"
                )
    
            # Initialize Web3 
            w3 = Web3(Web3.HTTPProvider(provider_config['rpc_url']))
            
            # Load contract factory ABI and Bytecode 
            contract_factory = self._get_contract_factory(w3, provider_config)
            print(contract_factory.constructor)
            # Prepare contract deployment parameters
            tx = self._prepare_contract_deployment(
                w3, 
                contract_factory, 
                asset, 
                tokenization_request
            )
            print("tx")
            print(tx)

            
            # Sign and send transaction
            tx_receipt = self._send_blockchain_transaction(w3, tx)
            
            # Update asset with contract details
            asset.blockchain_contract_address = tx_receipt.contractAddress
            asset.status = RWAStatus.TOKENIZED
            asset.token_standard = tokenization_request.token_standard
            
            db.commit()
            db.refresh(asset)
            
            return {
                "asset_id": asset.id,
                "status": "tokenization_initiated",
                "blockchain_contract_address": asset.blockchain_contract_address,
                "blockchain_network": tokenization_request.blockchain_network
            }
        except Exception as e:
        
        
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Tokenization failed: {str(e)}"
            )

    def get_market_listings(
        self, 
        db: Session,
        asset_type: Optional[str] = None,
        status: Optional[str] = None,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None
    ):
        """
        Retrieve RWA marketplace listings with filters
        """
        query = db.query(RealWorldAsset).filter(
            RealWorldAsset.status == RWAStatus.TOKENIZED
        )
        
        if asset_type:
            query = query.filter(RealWorldAsset.asset_type == asset_type)
        
        if status:
            query = query.filter(RealWorldAsset.status == status)
        
        if min_price:
            query = query.filter(RealWorldAsset.token_price >= min_price)
        
        if max_price:
            query = query.filter(RealWorldAsset.token_price <= max_price)
        
        return query.all()

    def list_asset_on_market(
        self, 
        db: Session, 
        asset_id: int, 
        user_id: int,
        listing_price: float
    ):
        """
        List a tokenized asset on the marketplace
        """
        asset = db.query(RealWorldAsset).filter(
            RealWorldAsset.id == asset_id,
            RealWorldAsset.owner_id == user_id
        ).first()
        
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found or not owned by user"
            )
        
        if asset.status != RWAStatus.TOKENIZED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only tokenized assets can be listed"
            )
        
        # Update asset status and listing price got here
        asset.status = RWAStatus.LISTED
        asset.token_price = listing_price
        
        db.commit()
        
        return asset



    def _get_contract_factory(self, w3, provider_config):
        """
        Load contract factory using ABI from Polygonscan
        """
        try:
            contract_address = '******************************************'
            #contract_address = '******************************************'
            abi, bytecode = self.contract_manager.fetch_contract_details(contract_address)
            print(contract_address)
            print(abi)
            print(bytecode)
            return w3.eth.contract(
                address=contract_address,
                abi=abi,
                bytecode=bytecode
            )
        except Exception as e:

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f" ;;;;;;;;;;;;;;;;;;;;s{str(e)}"
            )
    
    def _prepare_contract_deployment(self, w3, contract_factory, asset, tokenization_request):
        """
        Prepare contract deployment transaction
        """
        try:
            # Use the contract factory to create a deploy transaction Contract deployed at:
            deploy_txn = contract_factory.constructor(
                '******************************************'
            ).build_transaction({
                'from': '******************************************',  # Replace with actual account
                'nonce': w3.eth.get_transaction_count('******************************************'),
                'gas': 2000000,
                'gasPrice': w3.eth.gas_price
            })
            print(deploy_txn)
            return deploy_txn
        except Exception as e:
            # Log or handle the exception
            print(f"Error preparing contract deployment: {e}")
            raise

    def _send_blockchain_transaction(self, w3, tx):
        """
        Send blockchain transaction and wait for receipt
        """
        try:
            # Sign and send transaction
            signed_tx = w3.eth.account.sign_transaction(tx, private_key=PRIVATE_KEY)
            
            print("llllllllllllllllllll")
            print(signed_tx)
            # Send transaction
            tx_hash = w3.eth.send_raw_transaction(signed_tx.raw_transaction)
            
            print(tx_hash)

            
            
            # Wait for transaction receipt
            tx_receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
            
            print(f"Contract deployed at: {tx_receipt.contractAddress}")
                    # Wait for transaction receipt
            return tx_receipt
        except Exception as e:

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"{str(e)}"
            )
        

import requests

class RWAContractManager:

    # You can use existing verified RWA contracts
    KNOWN_RWA_CONTRACTS = {
        'centrifuge': '******************************************',  # Centrifuge RWA Market
        'goldfinch': '******************************************',  # Goldfinch Senior Pool
        'maker': '******************************************',      # MakerDAO RWA
        'ondo': '******************************************',       # Ondo Finance RWA
    }

    POLYGONSCAN_BASE_URL = "https://api.polygonscan.com/api"
    POLYGONSCAN_API_KEY = POLYGONSCAN_API_KEY

    @classmethod
    def fetch_contract_details(cls, contract_address: str) -> Tuple[str, str]:
        """
        Fetch both ABI and bytecode from Polygonscan
        """
        try:
            contract_address = web3.to_checksum_address(contract_address)

            print("got here")
            # Fetch ABI
            abi = cls.fetch_contract_abi(contract_address)
            print(abi)
            # Fetch Bytecode
            bytecode = cls.fetch_contract_bytecode(contract_address)

            print(bytecode)
            return abi, bytecode
        except Exception as e:
            raise HTTPException(
                status_code=500, 
                detail=f"Error fetching contract details: {str(e)}"
            )

    @classmethod
    def fetch_contract_abi(cls, contract_address: str) -> str:
        """
        Fetch contract ABI from Polygonscan
        """
        response = requests.get(
            f"{cls.POLYGONSCAN_BASE_URL}",
            params={
                "module": "contract",
                "action": "getabi",
                "address": contract_address,
                "apikey": cls.POLYGONSCAN_API_KEY
            }
        )
        data = response.json()

        if data["status"] != "1":
            raise HTTPException(
                status_code=400, 
                detail=f"Error fetching ABI: {data['result']}"
            )
        
        return data["result"]

    @classmethod
    def fetch_contract_bytecode(cls, contract_address: str) -> str:
        """
        Fetch contract bytecode from Polygonscan
        """

        response = requests.get(
            f"{POLYGONSCAN_BASE_URL}?module=proxy&action=eth_getCode&address={contract_address}&apikey={POLYGONSCAN_API_KEY}"
        )

        data = response.json()

        print(data)
        return data["result"]

    @classmethod
    def get_known_contract_address(cls, protocol: str) -> str:
        """
        Get address of known RWA contract
        """
        if protocol not in cls.KNOWN_RWA_CONTRACTS:
            raise HTTPException(
                status_code=400,
                detail=f"Unknown RWA protocol. Available protocols: {list(cls.KNOWN_RWA_CONTRACTS.keys())}"
            )
        print("Error")
        print(cls.KNOWN_RWA_CONTRACTS[protocol])
        return cls.KNOWN_RWA_CONTRACTS[protocol]
        























import requests
import json

# Define your API keys and endpoints
TRANSAK_API_KEY = 'edda2878-65c5-44d5-bcb3-2739369f9cde'
TRANSAK_BASE_URL = 'https://transak.com'

# Function to create a new tokenized asset
def create_tokenized_asset(asset_name, asset_value, asset_description):
    # Construct the payload for the API request
    payload = {
        "assetName": asset_name,
        "assetValue": asset_value,
        "description": asset_description,
        "currency": "USD",
        # Add other parameters as needed
    }
    
    # Set headers for the request
    headers = {
        'Content-Type': 'application/json',
        #'Authorization': f'Bearer {TRANSAK_API_KEY}'
    }
    
    # Make the API request to create the tokenized asset
    response = requests.post(f'https://api.xalts.com/tokenize', headers=headers, data=json.dumps(payload))
    

    print(response)

    if response.status_code == 200:
        print("Asset tokenized successfully:", response.json())
    else:
        print("Error tokenizing asset:", response.status_code, response.text)










"""
from web3 import Web3
from solcx import compile_source
from .RWA import contract_source_code
# Connect to Polygon network
wallet_address = "0xYourWalletAddress"
private_key = "YourPrivateKey"


WEB3_PROVIDER = f"https://polygon-mainnet.g.alchemy.com/v2/{ALCHEMY_API_KEY}"
w3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER))



# Solidity code for the contract
#with open("RWA", "r") as file:
    #contract_source_code = file.read()


# Compile the contract
compiled_sol = compile_source(contract_source_code)
contract_interface = compiled_sol['<stdin>:HelloWorld']

# Contract bytecode and ABI
bytecode = contract_interface['bin']
abi = contract_interface['abi']

# Deploy contract
def deploy_contract():
    contract = w3.eth.contract(abi=abi, bytecode=bytecode)
    transaction = contract.constructor().buildTransaction({
        "from": wallet_address,
        "nonce": w3.eth.get_transaction_count(wallet_address),
        "gas": 2000000,
        "gasPrice": w3.toWei("20", "gwei"),
    })
    signed_txn = w3.eth.account.sign_transaction(transaction, private_key)
    tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)
    receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
    return receipt.contractAddress

# Deploy and retrieve the contract address
contract_address = deploy_contract()
print(f"Contract deployed at: {contract_address}")






#contract_address = "0xYourDeployedContractAddress"
#abi = [...]  # Replace with the contract's ABI

# Initialize the contract
contract = w3.eth.contract(address=contract_address, abi=abi)
wallet_address = "0xYourWalletAddress"
private_key = "YourPrivateKey"

# Mint a new token
def mint_token(recipient, token_uri, details):
    tx = contract.functions.mint(recipient, token_uri, details).buildTransaction({
        "from": wallet_address,
        "gas": 300000,
        "gasPrice": w3.toWei("20", "gwei"),
        "nonce": w3.eth.get_transaction_count(wallet_address),
    })
    signed_tx = w3.eth.account.sign_transaction(tx, private_key=private_key)
    tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
    return w3.eth.wait_for_transaction_receipt(tx_hash)

# Update asset details
def update_asset_details(token_id, details):
    tx = contract.functions.updateAssetDetails(token_id, details).buildTransaction({
        "from": wallet_address,
        "gas": 300000,
        "gasPrice": w3.toWei("20", "gwei"),
        "nonce": w3.eth.get_transaction_count(wallet_address),
    })
    signed_tx = w3.eth.account.sign_transaction(tx, private_key=private_key)
    tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
    return w3.eth.wait_for_transaction_receipt(tx_hash)

# Fetch asset details
def get_asset_details(token_id):
    return contract.functions.getAssetDetails(token_id).call()



def set_token_price(token_id, price):
    tx = contract.functions.setTokenPrice(token_id, price).buildTransaction({
        "from": wallet_address,
        "gas": 200000,
        "gasPrice": w3.toWei("20", "gwei"),
        "nonce": w3.eth.get_transaction_count(wallet_address),
    })
    signed_tx = w3.eth.account.sign_transaction(tx, private_key=private_key)
    tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
    return w3.eth.wait_for_transaction_receipt(tx_hash)

# Purchase token
def purchase_token(token_id, price):
    tx = contract.functions.purchaseToken(token_id).buildTransaction({
        "from": wallet_address,
        "value": price,  # Send ETH/MATIC
        "gas": 200000,
        "gasPrice": w3.toWei("20", "gwei"),
        "nonce": w3.eth.get_transaction_count(wallet_address),
    })
    signed_tx = w3.eth.account.sign_transaction(tx, private_key=private_key)
    tx_hash = w3.eth.send_raw_transaction(signed_tx.rawTransaction)
    return w3.eth.wait_for_transaction_receipt(tx_hash)

import requests



# Example usage
recipient = "0xRecipientAddress"
token_uri = "ipfs://YourMetadataURI"
details = "Asset: Property, Location: NYC, Value: $1M"


def upload_to_ipfs(file_path):
    url = "https://ipfs.infura.io:5001/api/v0/add"
    files = {'file': open(file_path, 'rb')}
    response = requests.post(url, files=files)
    return response.json()["Hash"]

# Upload metadata.json to IPFS
file_path = "metadata.json"
ipfs_hash = upload_to_ipfs(file_path)
metadata_uri = f"ipfs://{ipfs_hash}"
print(f"Metadata URI: {metadata_uri}")

# Mint token
mint_receipt = mint_token(recipient, token_uri, details)
print(f"Minted token at transaction: {mint_receipt.transactionHash.hex()}")

# Update asset details
update_receipt = update_asset_details(0, "Updated Asset Details")
print(f"Updated asset details: {update_receipt.transactionHash.hex()}")

# Get asset details
asset_details = get_asset_details(0)
print(f"Asset Details: {asset_details}")


# Set a price for token 0
set_receipt = set_token_price(0, w3.toWei(1, "ether"))
print(f"Set token price: {set_receipt.transactionHash.hex()}")

# Purchase token 0
purchase_receipt = purchase_token(0, w3.toWei(1, "ether"))
print(f"Purchased token: {purchase_receipt.transactionHash.hex()}")
"""