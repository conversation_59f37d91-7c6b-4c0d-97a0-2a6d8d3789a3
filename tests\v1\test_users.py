from api.core.logging_config import get_logger
import pytest
import json
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from fastapi import HTTPException, status, UploadFile
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta, timezone
from sqlalchemy import select
import io

from main import app
from api.v1.user.models import User as UserModel, <PERSON>ter as UserAvater
from api.v1.user.services import UserService
from api.v1.user import schemas as user_schema
from config import config
import httpx
from httpx import AsyncClient
from tests.v1.conftest import get_unique_test_user_data

logger = get_logger(__name__)

# Test data
TEST_UPDATE_DATA = {
    "first_name": "Updated",
    "last_name": "User",
    "email": "<EMAIL>"
}

# Helper functions
def create_test_image():
    """Create a test image file for avatar upload testing"""
    return {
        "file": ("test.jpg", io.BytesIO(b"test image content"), "image/jpeg")
    }

@pytest.mark.asyncio
async def test_get_user(client, test_verified_user):
    """Test getting authenticated user information"""
    response = await client.get(
        "/user",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    user_data = response.json()
    assert "email" in user_data
    assert user_data["email"] == test_verified_user["email"]


@pytest.mark.asyncio
async def test_update_user(client, test_verified_user):
    """Test updating user information"""
    update_data = {
        "first_name": TEST_UPDATE_DATA["first_name"],
        "last_name": TEST_UPDATE_DATA["last_name"]
    }

    response = await client.patch(
        "/users/1",  # Using a fixed ID since we're authenticated as this user
        json=update_data,
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    json_response = response.json()
    assert "message" in json_response
    assert json_response["details"]["first_name"] == TEST_UPDATE_DATA["first_name"]
    assert json_response["details"]["last_name"] == TEST_UPDATE_DATA["last_name"]


@pytest.mark.asyncio
async def test_update_user_duplicate_email(client, test_verified_user, test_user):
    """Test updating user with duplicate email"""
    update_data = {
        "email": test_user["email"]  # Try to use another user's email
    }

    response = await client.patch(
        "/users/1",  # Using fixed ID since we're authenticated as this user
        json=update_data,
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 409  # Conflict
    json_response = response.json()
    assert "detail" in json_response
    assert "already exists" in json_response["detail"]


@pytest.mark.asyncio
async def test_delete_user(client, test_verified_user):
    """Test deleting user account"""
    response = await client.delete(
        "/users/1",  # Using fixed ID since we're authenticated as this user
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    json_response = response.json()
    assert "deleted" in json_response["message"]


@pytest.mark.asyncio
async def test_upload_avatar(client, test_verified_user):
    """Test uploading user avatar"""
    try:
        with open("test_image.jpg", "wb") as f:
            f.write(b"test image content")

        files = {"file": ("test_image.jpg", open("test_image.jpg", "rb"), "image/jpeg")}
        response = await client.post(
            "/user/avater",
            files=files,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

        assert response.status_code == 200
        json_response = response.json()
        assert "message" in json_response
        assert "uploaded" in  json_response["message"]
        assert "avatar_url" in json_response["detail"]

    finally:
        # Clean up test file
        import os
        if os.path.exists("test_image.jpg"):
            os.remove("test_image.jpg")



@pytest.mark.asyncio
async def test_get_avatar(client, test_verified_user):
    """Test getting user avatar"""
    response = await client.get(
        "/user/1/avater",  # Using fixed ID since we're authenticated as this user
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    if response.status_code == 200:
        json_response = response.json()
        assert "avatar_url" in json_response
    else:
        # If no avatar is set, should return 404
        assert response.status_code == 404
        json_response = response.json()
        assert "detail" in json_response
        assert "Avatar not found" in json_response["detail"]


@pytest.mark.asyncio
async def test_delete_avatar(client, test_verified_user):
    """Test deleting user avatar"""
    try:
        # First upload an avatar
        with open("test_image.jpg", "wb") as f:
            f.write(b"test image content")

        files = {"file": ("test_image.jpg", open("test_image.jpg", "rb"), "image/jpeg")}
        await client.post(
            "/user/avater",
            files=files,
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

        # Then delete it
        response = await client.delete(
            "/users/avater/1",  # Using fixed ID since we're authenticated as this user
            headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
        )

        assert response.status_code == 200
        json_response = response.json()
        assert "detail" in json_response
        assert "Deleted successfully" in json_response["detail"]

    finally:
        # Clean up test file
        import os
        if os.path.exists("test_image.jpg"):
            os.remove("test_image.jpg")



@pytest.mark.asyncio
async def test_update_notification_settings(client, test_verified_user):
    """Test updating notification settings"""
    notification_settings = {
        "email_notification": True,
        "push_notification": False,
        "sms_notification": True
    }

    response = await client.patch(
        "/user/notification/update",
        json=notification_settings,
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    json_response = response.json()
    assert "message" in json_response
    assert "detail" in json_response
    assert json_response["detail"]["email_notification"] == notification_settings["email_notification"]
    assert json_response["detail"]["push_notification"] == notification_settings["push_notification"]
    assert json_response["detail"]["sms_notification"] == notification_settings["sms_notification"]


@pytest.mark.asyncio
async def test_create_api_key(client, test_verified_user):
    """Test creating a new API key"""
    response = await client.post(
        "/user/api-key",
        params={"name": "Test API Key"},
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 201
    json_response = response.json()
    assert "api_key" in json_response
    assert "id" in json_response
    assert "name" in json_response
    assert json_response["name"] == "Test API Key"


@pytest.mark.asyncio
async def test_list_api_keys(client, test_verified_user):
    """Test listing user's API keys"""
    response = await client.get(
        "/user/api-keys",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    json_response = response.json()
    assert isinstance(json_response, list)
    if len(json_response) > 0:
        assert "id" in json_response[0]
        assert "name" in json_response[0]
        assert "api_key" in json_response[0]


@pytest.mark.asyncio
async def test_regenerate_api_key(client, test_verified_user):
    """Test regenerating an API key"""
    # First create an API key
    create_response = await client.post(
        "/user/api-key",
        params={"name": "Test API Key"},
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    api_key_id = create_response.json()["id"]
    old_api_key = create_response.json()["api_key"]

    # Now regenerate it
    response = await client.put(
        f"/user/api-keys/{api_key_id}/regenerate",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    json_response = response.json()
    assert "api_key" in json_response
    assert json_response["api_key"] != old_api_key
    assert json_response["id"] == api_key_id


@pytest.mark.asyncio
async def test_regenerate_nonexistent_api_key(client, test_verified_user):
    """Test regenerating a non-existent API key"""
    response = await client.put(
        "/user/api-keys/99999/regenerate",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 400
    json_response = response.json()
    assert "detail" in json_response


@pytest.mark.asyncio
async def test_delete_api_key(client, test_verified_user):
    """Test deleting an API key"""
    # First create an API key
    create_response = await client.post(
        "/user/api-key",
        params={"name": "Test API Key"},
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )
    api_key_id = create_response.json()["id"]

    # Now delete it
    response = await client.delete(
        f"/user/api-keys/{api_key_id}",
        headers={"Authorization": f"Bearer {test_verified_user['access_token']}"}
    )

    assert response.status_code == 200
    json_response = response.json()
    assert "message" in json_response
    assert "deleted" in json_response["message"].lower()


@pytest.mark.asyncio
async def test_endpoints_without_token(client):
    """Test accessing protected endpoints without authentication token"""
    # Test various endpoints
    endpoints = [
        ("GET", "/user"),
        ("PATCH", "/users/1"),
        ("DELETE", "/users/1"),
        ("POST", "/user/avater"),
        ("GET", "/user/1/avater"),
        ("DELETE", "/users/avater/1"),
        ("PATCH", "/user/notification/update"),
        ("POST", "/user/api-key"),
        ("GET", "/user/api-keys"),
    ]

    for method, endpoint in endpoints:
        if method == "GET":
            response = await client.get(endpoint)
        elif method == "POST":
            response = await client.post(endpoint)
        elif method == "PATCH":
            response = await client.patch(endpoint)
        elif method == "DELETE":
            response = await client.delete(endpoint)

        assert response.status_code == 401
        json_response = response.json()
        assert "detail" in json_response


@pytest.mark.asyncio
async def test_endpoints_with_invalid_token(client):
    """Test accessing protected endpoints with invalid authentication token"""
    invalid_token = "invalid_token_string"
    
    # Test various endpoints
    endpoints = [
        ("GET", "/user"),
        ("PATCH", "/users/1"),
        ("DELETE", "/users/1"),
        ("GET", "/user/1/avater"),
        ("DELETE", "/users/avater/1"),
        ("PATCH", "/user/notification/update"),
        ("GET", "/user/api-keys"),
    ]

    for method, endpoint in endpoints:
        if method == "GET":
            response = await client.get(
                endpoint,
                headers={"Authorization": f"Bearer {invalid_token}"}
            )
        elif method == "POST":
            response = await client.post(
                endpoint,
                headers={"Authorization": f"Bearer {invalid_token}"}
            )
        elif method == "PATCH":
            response = await client.patch(
                endpoint,
                headers={"Authorization": f"Bearer {invalid_token}"}
            )
        elif method == "DELETE":
            response = await client.delete(
                endpoint,
                headers={"Authorization": f"Bearer {invalid_token}"}
            )

        assert response.status_code == 401
        json_response = response.json()
        assert "detail" in json_response
