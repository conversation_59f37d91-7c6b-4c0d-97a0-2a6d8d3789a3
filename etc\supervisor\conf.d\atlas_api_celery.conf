;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;  SUPERVISOR CONFIG – Celery worker + beat (Atlas) ;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[program:atlas_celery_worker]
directory=/srv/atlas-api
command=/srv/atlas-api/.venv/bin/celery \
         -A api.core.tasks worker \
         --loglevel=INFO \
         --pool=gevent \
         --concurrency=${CELERY_WORKER_CONCURRENCY:-100} \
         --max-tasks-per-child=1000 \
         --pidfile=%(here)s/celery_worker_%(process_num)d.pid
user=caprover-oz
numprocs=%(ENV_CELERY_NUM_WORKERS:-2) ; e.g. 2
process_name=%(program_name)s_%(process_num)02d

autostart=true
autorestart=true
startsecs=10
stopsignal=TERM
stopwaitsecs=60
killasgroup=true

environment=\
    PYTHONUNBUFFERED=true,\
    CELERY_WORKER_CONCURRENCY="%(CELERY_WORKER_CONCURRENCY)"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

[program:atlas_celery_beat]
directory=/srv/atlas-api
command=/srv/atlas-api/.venv/bin/celery \
         -A api.core.tasks beat \
         --loglevel=INFO \
         --pidfile=%(here)s/celerybeat.pid
user=caprover-oz

autostart=true
autorestart=true
startsecs=10
stopsignal=TERM
stopwaitsecs=60
killasgroup=true
