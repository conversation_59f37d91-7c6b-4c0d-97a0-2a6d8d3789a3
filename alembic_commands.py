#!/usr/bin/env python
"""
Alembic Commands Helper Script

This script provides a convenient way to run common Alembic commands.
"""

import argparse
import os
import subprocess
import sys

def run_command(command):
    """Run a shell command and print output"""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, text=True)
    if result.returncode != 0:
        print(f"Command failed with exit code {result.returncode}")
        sys.exit(result.returncode)
    return result

def init():
    """Initialize Alembic (should only be run once)"""
    run_command("alembic init migrations")

def create_migration(message):
    """Create a new migration with autogenerate"""
    run_command(f'alembic revision --autogenerate -m "{message}"')

def upgrade(revision="head"):
    """Upgrade to a newer migration"""
    run_command(f"alembic upgrade {revision}")

def downgrade(revision="-1"):
    """Downgrade to an older migration"""
    run_command(f"alembic downgrade {revision}")

def show_current():
    """Show current migration version"""
    run_command("alembic current")

def show_history():
    """Show migration history"""
    run_command("alembic history --verbose")

def main():
    parser = argparse.ArgumentParser(description="Alembic Commands Helper")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")

    # init command
    init_parser = subparsers.add_parser("init", help="Initialize Alembic (first time only)")

    # revision command
    revision_parser = subparsers.add_parser("revision", help="Create a new migration")
    revision_parser.add_argument("message", help="Migration message")

    # upgrade command
    upgrade_parser = subparsers.add_parser("upgrade", help="Upgrade to a newer migration")
    upgrade_parser.add_argument("revision", nargs="?", default="head", help="Revision to upgrade to (default: head)")

    # downgrade command
    downgrade_parser = subparsers.add_parser("downgrade", help="Downgrade to an older migration")
    downgrade_parser.add_argument("revision", nargs="?", default="-1", help="Revision to downgrade to (default: -1)")

    # current command
    current_parser = subparsers.add_parser("current", help="Show current migration version")

    # history command
    history_parser = subparsers.add_parser("history", help="Show migration history")

    args = parser.parse_args()

    if args.command == "init":
        init()
    elif args.command == "revision":
        create_migration(args.message)
    elif args.command == "upgrade":
        upgrade(args.revision)
    elif args.command == "downgrade":
        downgrade(args.revision)
    elif args.command == "current":
        show_current()
    elif args.command == "history":
        show_history()
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
